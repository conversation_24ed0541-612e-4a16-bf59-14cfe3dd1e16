import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {TypoSkin} from '../assets/skin/typography';

const NotoSansJPExample: React.FC = () => {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Noto Sans JP Typography Examples</Text>
        
        {/* Heading Examples */}
        <View style={styles.subsection}>
          <Text style={styles.subsectionTitle}>Headings</Text>
          <Text style={TypoSkin.heading1}>見出し1 - Heading 1</Text>
          <Text style={TypoSkin.heading2}>見出し2 - Heading 2</Text>
          <Text style={TypoSkin.heading3}>見出し3 - Heading 3</Text>
          <Text style={TypoSkin.heading4}>見出し4 - Heading 4</Text>
          <Text style={TypoSkin.heading5}>見出し5 - Heading 5</Text>
          <Text style={TypoSkin.heading6}>見出し6 - Heading 6</Text>
          <Text style={TypoSkin.heading7}>見出し7 - Heading 7</Text>
          <Text style={TypoSkin.heading8}>見出し8 - Heading 8</Text>
          <Text style={TypoSkin.heading9}>見出し9 - Heading 9</Text>
        </View>

        {/* Body Text Examples */}
        <View style={styles.subsection}>
          <Text style={styles.subsectionTitle}>Body Text</Text>
          <Text style={TypoSkin.body1}>
            これは本文1のサンプルテキストです。日本語の文章がどのように表示されるかを確認できます。
            This is body text 1 sample. You can see how Japanese text is displayed.
          </Text>
          <Text style={TypoSkin.body2}>
            これは本文2のサンプルテキストです。少し小さめのフォントサイズです。
            This is body text 2 sample with smaller font size.
          </Text>
          <Text style={TypoSkin.body3}>
            これは本文3のサンプルテキストです。最も小さい本文サイズです。
            This is body text 3 sample with the smallest body size.
          </Text>
        </View>

        {/* Regular Examples */}
        <View style={styles.subsection}>
          <Text style={styles.subsectionTitle}>Regular Styles</Text>
          <Text style={TypoSkin.regular0}>Regular 0 - 10px</Text>
          <Text style={TypoSkin.regular1}>Regular 1 - 12px</Text>
          <Text style={TypoSkin.regular2}>Regular 2 - 14px</Text>
          <Text style={TypoSkin.regular3}>Regular 3 - 16px</Text>
          <Text style={TypoSkin.regular4}>Regular 4 - 20px</Text>
        </View>

        {/* Semibold Examples */}
        <View style={styles.subsection}>
          <Text style={styles.subsectionTitle}>Semibold Styles</Text>
          <Text style={TypoSkin.semibold1}>Semibold 1 - 12px</Text>
          <Text style={TypoSkin.semibold2}>Semibold 2 - 14px</Text>
          <Text style={TypoSkin.semibold3}>Semibold 3 - 16px</Text>
          <Text style={TypoSkin.semibold4}>Semibold 4 - 20px</Text>
        </View>

        {/* Button Text Examples */}
        <View style={styles.subsection}>
          <Text style={styles.subsectionTitle}>Button Text</Text>
          <View style={styles.buttonExample}>
            <Text style={TypoSkin.buttonText1}>ボタンテキスト1</Text>
          </View>
          <View style={styles.buttonExample}>
            <Text style={TypoSkin.buttonText2}>ボタンテキスト2</Text>
          </View>
          <View style={styles.buttonExample}>
            <Text style={TypoSkin.buttonText3}>ボタンテキスト3</Text>
          </View>
        </View>

        {/* Subtitle Examples */}
        <View style={styles.subsection}>
          <Text style={styles.subsectionTitle}>Subtitles</Text>
          <Text style={TypoSkin.subtitle1}>サブタイトル1 - 重要な補足情報</Text>
          <Text style={TypoSkin.subtitle2}>サブタイトル2 - 追加の説明文</Text>
          <Text style={TypoSkin.subtitle3}>サブタイトル3 - 小さな説明</Text>
        </View>

        {/* Title Examples */}
        <View style={styles.subsection}>
          <Text style={styles.subsectionTitle}>Titles</Text>
          <Text style={TypoSkin.title1}>タイトル1 - Title 1</Text>
          <Text style={TypoSkin.title2}>タイトル2 - Title 2</Text>
          <Text style={TypoSkin.title3}>タイトル3 - Title 3</Text>
        </View>

        {/* Mixed Content Example */}
        <View style={styles.subsection}>
          <Text style={styles.subsectionTitle}>Mixed Content</Text>
          <Text style={TypoSkin.heading4}>日本語学習アプリ</Text>
          <Text style={TypoSkin.body2}>
            このアプリは日本語を学習するためのツールです。ひらがな、カタカナ、漢字を効率的に学ぶことができます。
          </Text>
          <Text style={TypoSkin.subtitle2}>特徴：</Text>
          <Text style={TypoSkin.body3}>• インタラクティブな学習体験</Text>
          <Text style={TypoSkin.body3}>• 進捗追跡機能</Text>
          <Text style={TypoSkin.body3}>• 音声サポート</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333333',
  },
  subsection: {
    marginBottom: 30,
  },
  subsectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    color: '#666666',
  },
  buttonExample: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginVertical: 5,
    alignSelf: 'flex-start',
  },
});

export default NotoSansJPExample;
