// import {
//   AppleButton,
//   appleAuth,
//   appleAuthAndroid,
// } from '@invertase/react-native-apple-authentication';
// import React, {useEffect} from 'react';
// import {
//   ActivityIndicator,
//   StyleSheet,
//   Text,
//   TouchableOpacity,
//   View,
// } from 'react-native';
// import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
// import {faAppleAlt} from '@fortawesome/free-solid-svg-icons';
// import {User} from '@react-native-google-signin/google-signin';
// import auth from '@react-native-firebase/auth';
// import {TypoSkin} from '../../../assets/skin/typography';
// import {AppSvg} from 'wini-mobile-components';
// import {ColorThemes} from '../../../assets/skin/colors';

// const logoApple =
//   '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.5172 12.5555C17.5078 10.957 18.232 9.75234 19.6945 8.86406C18.8766 7.69219 17.6391 7.04766 16.0078 6.92344C14.4633 6.80156 12.7734 7.82344 12.1547 7.82344C11.5008 7.82344 10.0055 6.96563 8.82891 6.96563C6.40078 7.00313 3.82031 8.90156 3.82031 12.7641C3.82031 13.9055 4.02891 15.0844 4.44609 16.2984C5.00391 17.8969 7.01484 21.8133 9.1125 21.75C10.2094 21.7242 10.9852 20.9719 12.4125 20.9719C13.7977 20.9719 14.5148 21.75 15.7383 21.75C17.8547 21.7195 19.6734 18.1594 20.2031 16.5563C17.3648 15.218 17.5172 12.6375 17.5172 12.5555ZM15.0539 5.40703C16.2422 3.99609 16.1344 2.71172 16.0992 2.25C15.0492 2.31094 13.8352 2.96484 13.1437 3.76875C12.382 4.63125 11.9344 5.69766 12.0305 6.9C13.1648 6.98672 14.2008 6.40313 15.0539 5.40703Z" fill="#00204D" fill-opacity="none" style="mix-blend-mode:multiply"/></svg>';

// interface Props {
//   onAuthSuccess: (value: any) => void;
//   onLoading: (value: boolean) => void;
//   isLoading: boolean;
// }

// export default function AppleSignIn(props: Props) {
//   const {onAuthSuccess, onLoading, isLoading} = props;

//   async function onAppleButtonPress() {
//     try {
//       if (isLoading) {
//         return;
//       }
//       onLoading(true);
//       // performs login request
//       const appleAuthRequestResponse = await appleAuth.performRequest({
//         requestedOperation: appleAuth.Operation.LOGIN,
//         requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
//       });

//       // Ensure Apple returned a user identityToken
//       if (!appleAuthRequestResponse.identityToken) {
//         console.log('Apple Sign-In failed - no identify token returned');
//         onLoading(false);
//         throw new Error('Apple Sign-In failed - no identify token returned');
//       }

//       // Create a Firebase credential from the response
//       const {identityToken, nonce} = appleAuthRequestResponse;
//       const appleCredential = auth.AppleAuthProvider.credential(
//         identityToken,
//         nonce,
//       );

//       // Sign the user in with the credential
//       return auth()
//         .signInWithCredential(appleCredential)
//         .then(async data => {
//           // console.log(data);
//           onAuthSuccess(appleAuthRequestResponse);
//           // get current authentication state for user
//           // /!\ This method must be tested on a real device. On the iOS simulator it always throws an error.
//           const credentialState = await appleAuth.getCredentialStateForUser(
//             appleAuthRequestResponse.user,
//           );

//           // use credentialState response to ensure the user is authenticated
//           if (credentialState === appleAuth.State.AUTHORIZED) {
//             // user is authenticated
//             // console.log('====================================');
//             // console.log(credentialState);
//             // console.log('====================================');
//           }
//           onLoading(false);
//         });
//     } catch (error) {
//       console.log('====================================');
//       console.log('Apple Sign-In failed - no identify token returned', error);
//       console.log('====================================');
//       onLoading(false);
//     }
//   }

//   return (
//     <TouchableOpacity onPress={onAppleButtonPress} style={styles.TouchStyle}>
//       {isLoading ? (
//         <View style={styles.TouchStyle}>
//           <View style={styles.loading}>
//             <ActivityIndicator size="large" />
//           </View>
//         </View>
//       ) : (
//         <View style={styles.TouchStyle}>
//           <AppSvg SvgSrc={logoApple} size={24} />
//           <Text
//             style={[
//               TypoSkin.buttonText1,
//               {
//                 color: ColorThemes.light.Neutral_Text_Color_Body,
//                 fontSize: 18,
//                 paddingLeft: 4,
//                 paddingTop: 2,
//               },
//             ]}>
//             Đăng nhập với Apple
//           </Text>
//         </View>
//       )}
//     </TouchableOpacity>
//   );
// }
// const styles = StyleSheet.create({
//   TouchStyle: {
//     flexDirection: 'row',
//     width: '100%',
//     height: 44,
//     borderRadius: 25,
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   loading: {
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
// });
