import {useEffect, useState} from 'react';
import {Alert, TouchableOpacity, View} from 'react-native';
import {SvgXml} from 'react-native-svg';
import ReactNativeBiometrics, {BiometryTypes} from 'react-native-biometrics';
import {saveDataToAsyncStorage} from '../../utils/AsyncStorage';
import {ColorThemes} from '../../assets/skin/colors';

interface Props {
  onSuccess: (value: boolean) => void;
  onSupport?: (value: boolean) => void;
  isFirstTime?: boolean;
  sizeIcon?: number;
}

export default function LocalAuthen(props: Props) {
  const rnBiometrics = new ReactNativeBiometrics({
    allowDeviceCredentials: true,
  });
  const [biometrics, setBiometrics] = useState('');

  useEffect(() => {
    rnBiometrics.isSensorAvailable().then(resultObject => {
      const {available, biometryType} = resultObject;
      if (available && biometryType === BiometryTypes.TouchID) {
        console.log('TouchID is supported');
        saveDataToAsyncStorage('spBiometrics', 'true');
        saveDataToAsyncStorage('biometryType', 'TouchID');
        if (props.onSupport) props.onSupport(true);
        setBiometrics('TouchID');
      } else if (available && biometryType === BiometryTypes.FaceID) {
        console.log('FaceID is supported');
        setBiometrics('FaceID');
        saveDataToAsyncStorage('biometryType', 'FaceID');
        saveDataToAsyncStorage('spBiometrics', 'true');
        if (props.onSupport) props.onSupport(true);
      } else if (available && biometryType === BiometryTypes.Biometrics) {
        console.log('Biometrics is supported');
        if (props.onSupport) props.onSupport(true);
        saveDataToAsyncStorage('biometryType', 'true');

        saveDataToAsyncStorage('spBiometrics', 'true');
        setBiometrics('Biometrics');
      } else {
        console.log('Biometrics not supported');
        saveDataToAsyncStorage('biometryType', 'false');
        saveDataToAsyncStorage('spBiometrics', 'false');
        if (props.onSupport) props.onSupport(false);
        setBiometrics('');
      }
    });
  }, []);

  return (
    <View>
      {biometrics != '' ? (
        <TouchableOpacity
          onPress={() => {
            if (props.isFirstTime === true) {
              Alert.alert(
                'Bạn cần đăng nhập lần đầu trước khi sử dụng chức năng này!',
              );
              return;
            }
            rnBiometrics
              .simplePrompt({
                promptMessage:
                  'Sử dụng sinh trắc học để thao tác chức năng này',
              })
              .then(resultObject => {
                const {success} = resultObject;

                if (success) {
                  console.log('successful biometrics provided');
                  props.onSuccess(true);
                } else {
                  console.log('user cancelled biometric prompt');
                  props.onSuccess(false);
                }
              })
              .catch(() => {
                console.log('biometrics failed');
              });
          }}>
          {biometrics == 'TouchID' ? (
            <TouchID
              size={props.sizeIcon ? props.sizeIcon : 42}
              color={ColorThemes.light.Neutral_Text_Color_Title}
            />
          ) : (
            <FaceID
              size={props.sizeIcon ? props.sizeIcon : 42}
              color={ColorThemes.light.Neutral_Text_Color_Title}
            />
          )}
        </TouchableOpacity>
      ) : (
        <View />
      )}
    </View>
  );
}

export const TouchID = ({
  size,
  color,
  opacity = 1,
}: {
  size?: number;
  color?: string;
  opacity?: number;
}) => (
  <SvgXml
    xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17.8102 4.47C17.7302 4.47 17.6502 4.45 17.5802 4.41C15.6602 3.42 14.0002 3 12.0102 3C10.0302 3 8.15023 3.47 6.44023 4.41C6.20023 4.54 5.90023 4.45 5.76023 4.21C5.63023 3.97 5.72023 3.66 5.96023 3.53C7.82023 2.52 9.86023 2 12.0102 2C14.1402 2 16.0002 2.47 18.0402 3.52C18.2902 3.65 18.3802 3.95 18.2502 4.19C18.1602 4.37 17.9902 4.47 17.8102 4.47ZM3.50023 9.72C3.40023 9.72 3.30023 9.69 3.21023 9.63C2.98023 9.47 2.93023 9.16 3.09023 8.93C4.08023 7.53 5.34023 6.43 6.84023 5.66C9.98023 4.04 14.0002 4.03 17.1502 5.65C18.6502 6.42 19.9102 7.51 20.9002 8.9C21.0602 9.12 21.0102 9.44 20.7802 9.6C20.5502 9.76 20.2402 9.71 20.0802 9.48C19.1802 8.22 18.0402 7.23 16.6902 6.54C13.8202 5.07 10.1502 5.07 7.29023 6.55C5.93023 7.25 4.79023 8.25 3.89023 9.51C3.81023 9.65 3.66023 9.72 3.50023 9.72ZM9.75023 21.79C9.62023 21.79 9.49023 21.74 9.40023 21.64C8.53023 20.77 8.06023 20.21 7.39023 19C6.70023 17.77 6.34023 16.27 6.34023 14.66C6.34023 11.69 8.88023 9.27 12.0002 9.27C15.1202 9.27 17.6602 11.69 17.6602 14.66C17.6602 14.94 17.4402 15.16 17.1602 15.16C16.8802 15.16 16.6602 14.94 16.6602 14.66C16.6602 12.24 14.5702 10.27 12.0002 10.27C9.43023 10.27 7.34023 12.24 7.34023 14.66C7.34023 16.1 7.66023 17.43 8.27023 18.51C8.91023 19.66 9.35023 20.15 10.1202 20.93C10.3102 21.13 10.3102 21.44 10.1202 21.64C10.0102 21.74 9.88023 21.79 9.75023 21.79ZM16.9202 19.94C15.7302 19.94 14.6802 19.64 13.8202 19.05C12.3302 18.04 11.4402 16.4 11.4402 14.66C11.4402 14.38 11.6602 14.16 11.9402 14.16C12.2202 14.16 12.4402 14.38 12.4402 14.66C12.4402 16.07 13.1602 17.4 14.3802 18.22C15.0902 18.7 15.9202 18.93 16.9202 18.93C17.1602 18.93 17.5602 18.9 17.9602 18.83C18.2302 18.78 18.4902 18.96 18.5402 19.24C18.5902 19.51 18.4102 19.77 18.1302 19.82C17.5602 19.93 17.0602 19.94 16.9202 19.94ZM14.9102 22C14.8702 22 14.8202 21.99 14.7802 21.98C13.1902 21.54 12.1502 20.95 11.0602 19.88C9.66023 18.49 8.89023 16.64 8.89023 14.66C8.89023 13.04 10.2702 11.72 11.9702 11.72C13.6702 11.72 15.0502 13.04 15.0502 14.66C15.0502 15.73 15.9802 16.6 17.1302 16.6C18.2802 16.6 19.2102 15.73 19.2102 14.66C19.2102 10.89 15.9602 7.83 11.9602 7.83C9.12023 7.83 6.52023 9.41 5.35023 11.86C4.96023 12.67 4.76023 13.62 4.76023 14.66C4.76023 15.44 4.83023 16.67 5.43023 18.27C5.53023 18.53 5.40023 18.82 5.14023 18.91C4.88023 19.01 4.59023 18.87 4.50023 18.62C4.01023 17.31 3.77023 16.01 3.77023 14.66C3.77023 13.46 4.00023 12.37 4.45023 11.42C5.78023 8.63 8.73023 6.82 11.9602 6.82C16.5102 6.82 20.2102 10.33 20.2102 14.65C20.2102 16.27 18.8302 17.59 17.1302 17.59C15.4302 17.59 14.0502 16.27 14.0502 14.65C14.0502 13.58 13.1202 12.71 11.9702 12.71C10.8202 12.71 9.89023 13.58 9.89023 14.65C9.89023 16.36 10.5502 17.96 11.7602 19.16C12.7102 20.1 13.6202 20.62 15.0302 21.01C15.3002 21.08 15.4502 21.36 15.3802 21.62C15.3302 21.85 15.1202 22 14.9102 22Z" fill="${
      color ?? '#667994'
    }"/></svg>`}
    width={size ?? 16}
    height={size ?? 16}
  />
);

export const FaceID = ({
  size,
  color,
  opacity = 1,
}: {
  size?: number;
  color?: string;
  opacity?: number;
}) => (
  <SvgXml
    xml={`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.82611 2.82611C3.35506 2.29716 4.07247 2 4.82051 2H6.8718C7.29663 2 7.64103 2.3444 7.64103 2.76923C7.64103 3.19407 7.29663 3.53846 6.8718 3.53846H4.82051C4.48049 3.53846 4.1544 3.67353 3.91397 3.91397C3.67353 4.1544 3.53846 4.48049 3.53846 4.82051V6.8718C3.53846 7.29663 3.19407 7.64103 2.76923 7.64103C2.3444 7.64103 2 7.29663 2 6.8718V4.82051C2 4.07247 2.29716 3.35506 2.82611 2.82611Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M16.359 2.76923C16.359 2.3444 16.7034 2 17.1282 2H19.1795C19.9275 2 20.6449 2.29716 21.1739 2.82611C21.7028 3.35506 22 4.07247 22 4.82051V6.8718C22 7.29663 21.6556 7.64103 21.2308 7.64103C20.8059 7.64103 20.4615 7.29663 20.4615 6.8718V4.82051C20.4615 4.48049 20.3265 4.1544 20.086 3.91397C19.8456 3.67353 19.5195 3.53846 19.1795 3.53846H17.1282C16.7034 3.53846 16.359 3.19407 16.359 2.76923Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M16.1026 7.12821C16.5274 7.12821 16.8718 7.4726 16.8718 7.89744V9.94872C16.8718 10.3736 16.5274 10.7179 16.1026 10.7179C15.6777 10.7179 15.3333 10.3736 15.3333 9.94872V7.89744C15.3333 7.4726 15.6777 7.12821 16.1026 7.12821Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M7.89744 7.12821C8.32227 7.12821 8.66667 7.4726 8.66667 7.89744V9.94872C8.66667 10.3736 8.32227 10.7179 7.89744 10.7179C7.4726 10.7179 7.12821 10.3736 7.12821 9.94872V7.89744C7.12821 7.4726 7.4726 7.12821 7.89744 7.12821Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M9.46649 15.5581C9.46649 15.5581 9.46649 15.5581 9.46649 15.5581L9.46506 15.5567L9.46346 15.5551L9.46131 15.553C9.46127 15.553 9.46135 15.553 9.46131 15.553C9.46321 15.5548 9.46868 15.5598 9.4777 15.5677C9.49611 15.5838 9.52921 15.6116 9.57692 15.6474C9.67247 15.7191 9.82568 15.822 10.0363 15.9274C10.4551 16.1367 11.1073 16.359 12 16.359C12.8927 16.359 13.5449 16.1367 13.9637 15.9274C14.1743 15.822 14.3275 15.7191 14.4231 15.6474C14.4708 15.6116 14.5039 15.5838 14.5223 15.5677C14.5313 15.5599 14.5368 15.5548 14.5387 15.553C14.8395 15.2582 15.3223 15.2601 15.6209 15.5586C15.9213 15.859 15.9213 16.3461 15.6209 16.6465L15.0769 16.1026C15.6209 16.6465 15.6211 16.6462 15.6209 16.6465L15.6197 16.6476L15.6185 16.6488L15.6158 16.6515L15.6091 16.658L15.591 16.6753C15.5768 16.6886 15.5582 16.7055 15.5354 16.7255C15.4897 16.7655 15.4266 16.8178 15.3462 16.8782C15.1853 16.9989 14.9539 17.1523 14.6517 17.3034C14.0448 17.6068 13.1585 17.8974 12 17.8974C10.8415 17.8974 9.95519 17.6068 9.3483 17.3034C9.04611 17.1523 8.81471 16.9989 8.65385 16.8782C8.57336 16.8178 8.5103 16.7655 8.46461 16.7255C8.44176 16.7055 8.42322 16.6886 8.40899 16.6753L8.39086 16.658L8.3842 16.6515L8.38147 16.6488L8.38026 16.6476C8.37999 16.6473 8.37915 16.6465 8.92308 16.1026M9.46649 15.5581C9.46649 15.5581 9.46649 15.5581 9.46649 15.5581V15.5581Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M12 7.12821C12.4248 7.12821 12.7692 7.4726 12.7692 7.89744V13.0256C12.7692 13.4505 12.4248 13.7949 12 13.7949H10.9744C10.5495 13.7949 10.2051 13.4505 10.2051 13.0256C10.2051 12.6008 10.5495 12.2564 10.9744 12.2564H11.2308V7.89744C11.2308 7.4726 11.5752 7.12821 12 7.12821Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M2.76923 16.359C3.19407 16.359 3.53846 16.7034 3.53846 17.1282V19.1795C3.53846 19.5195 3.67353 19.8456 3.91397 20.086C4.1544 20.3265 4.48049 20.4615 4.82051 20.4615H6.8718C7.29663 20.4615 7.64103 20.8059 7.64103 21.2308C7.64103 21.6556 7.29663 22 6.8718 22H4.82051C4.07247 22 3.35506 21.7028 2.82611 21.1739C2.29716 20.6449 2 19.9275 2 19.1795V17.1282C2 16.7034 2.3444 16.359 2.76923 16.359Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M21.2308 16.359C21.6556 16.359 22 16.7034 22 17.1282V19.1795C22 19.9275 21.7028 20.6449 21.1739 21.1739C20.6449 21.7028 19.9275 22 19.1795 22H17.1282C16.7034 22 16.359 21.6556 16.359 21.2308C16.359 20.8059 16.7034 20.4615 17.1282 20.4615H19.1795C19.5195 20.4615 19.8456 20.3265 20.086 20.086C20.3265 19.8456 20.4615 19.5195 20.4615 19.1795V17.1282C20.4615 16.7034 20.8059 16.359 21.2308 16.359Z" fill="${
      color ?? '#667994'
    }" opacity="${opacity}"/></svg>`}
    width={size ?? 16}
    height={size ?? 16}
  />
);
