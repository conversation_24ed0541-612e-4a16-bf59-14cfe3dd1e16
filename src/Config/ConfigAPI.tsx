const hostMapUrl = 'https://server.wini.vn/api/data/';

export default class ConfigAPI {
  static url = 'https://redis.ktxgroup.com.vn/api/';
  static pid = 'f5e4a5074091423981f047cf9f883175';
  static googleApiKey = 'AIzaSyBrjZpmgCpST9GWPt7fCnr_EiQi-uL9SQM';
  static urlImg = 'https://redis.ktxgroup.com.vn/api/file/img/';
  static adminITM = 'ddb8e94bb5b44fe4bccb8b59976f58bc';
  static gameALTP = 'cf86bc33ef03447fa744eea2bbf31cfc';
  static gameSakuTB = '7eb45b0edc6247c3bde6bbb15547dfda'; // TODO: Replace with actual game ID
  static gameDHBC = '1d56852db9964d9a878a1d9d5f872cb7'; // Game DHBC ID
  static gameMGHH = '1b1804be1c6049c2876d1626794fa7a0'; // Game MGHH ID
  static gameSKXT = '19d8c9d61ae74f968416b28fcf8e93c3'; // Game MGHH ID
  static GEOCODING_API_URL_BY_GOOGLE = (lat: any, lng: any) => {
    // return `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${ConfigAPI.googleApiKey}`
    return (
      hostMapUrl +
      `geocode/json?latlng=${lat},${lng}&key=${ConfigAPI.googleApiKey}`
    );
  };

  static getAddressByGoogleKey = (inputText: string) => {
    // console.log('====================================');
    // console.log(hostMapUrl + `place/textsearch/json?&query=${encodeURIComponent(inputText)}&components=country:VN&key=${ConfigAPI.googleApiKey}`);
    // console.log('====================================');
    return (
      hostMapUrl +
      `place/textsearch/json?&query=${encodeURIComponent(
        inputText,
      )}&components=country:VN&key=${ConfigAPI.googleApiKey}`
    );
  };

  static provinceUrl = 'https://esgoo.net/api-tinhthanh/1/0.htm';
  static districtUrl = (cityId: string) =>
    `https://esgoo.net/api-tinhthanh/2/${cityId}.htm`;

  static wardUrl = (districtId: string) =>
    `https://esgoo.net/api-tinhthanh/3/${districtId}.htm`;
}
