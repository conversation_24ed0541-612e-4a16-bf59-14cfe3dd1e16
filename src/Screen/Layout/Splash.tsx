import {useEffect, useRef} from 'react';
import {
  Animated,
  Dimensions,
  Easing,
  Image,
  Platform,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import BackgroundJapan from '../../assets/backgroundJapan';
import {SafeAreaView} from 'react-native-safe-area-context';

export const SplashScreen = () => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  // Function to get greeting based on current time
  const getGreeting = () => {
    const currentHour = new Date().getHours();
    // + <PERSON>u<PERSON><PERSON> sáng (06:00 - 9:59): おはよう
    // + Buổi trưa/chiều (10:000 - 18:00): こんにちは
    // + Buổi tối (18:00 trở đi): こんばんは
    if (currentHour >= 6 && currentHour < 10) {
      return 'おはよう'; // Morning (6:00 AM - 9:59 AM)
    } else if (currentHour >= 10 && currentHour < 18) {
      return 'こんにちは'; // Afternoon (10:00 AM - 5:59 PM)
    } else {
      return 'こんばんは'; // Evening/Night (6:00 PM - 5:59 AM)
    }
  };

  useEffect(() => {
    // Animate logo appearance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 800,
        easing: Easing.out(Easing.back(1.5)),
        useNativeDriver: true,
      }),
    ]).start();
  });

  return (
    <View style={styles.splashContainer}>
      <StatusBar backgroundColor="#E86D64" barStyle="light-content" />
      <Animated.View
        style={[
          styles.logoContainer,
          {
            flex: 1,
            opacity: fadeAnim,
            transform: [{scale: scaleAnim}],
          },
        ]}>
        {/* Logo SVG */}
        <BackgroundJapan
          width={Dimensions.get('window').width}
          height={
            Dimensions.get('window').height / 3 + 32 < 430
              ? 400
              : Dimensions.get('window').height / 3 + 32
          }
        />
        <View style={{zIndex: 999, flex: 1, alignItems: 'center'}}>
          <Animated.Text
            style={[
              styles.splashText,
              {opacity: fadeAnim, transform: [{scale: scaleAnim}]},
            ]}>
            ITM
          </Animated.Text>
          <Animated.Text
            style={[
              styles.splashSubtext,
              {opacity: fadeAnim, transform: [{scale: scaleAnim}]},
            ]}>
            {getGreeting()}
          </Animated.Text>
        </View>
        {/* <AppSvg SvgSrc={IconSvg.masco18} size={200} /> */}
        <Image
          source={require('../../assets/images/7.png')}
          height={200}
          resizeMode="contain"
          style={{
            height: 200,
            aspectRatio: 374 / 290,
            alignContent: 'flex-end',
            alignItems: 'flex-end',
            justifyContent: 'center',
            alignSelf: 'center',
          }}
        />
        {Platform.OS === 'ios' ? (
          <SafeAreaView edges={['bottom']} />
        ) : (
          <View style={{height: 24}} />
        )}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Splash Screen Styles
  splashContainer: {
    flex: 1,
    backgroundColor: '#E86D64',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    paddingTop: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  splashText: {
    color: 'white',
    fontSize: 42,
    fontWeight: 'bold',
    marginTop: 8,
  },
  splashSubtext: {
    color: 'white',
    fontSize: 42,
    marginTop: 10,
  },
});
