import {useNavigation} from '@react-navigation/native';
import {Text, View} from 'react-native';
import ScreenHeader from './header';
import React from 'react';
import {AppButton, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';

interface Props {
  isClose?: boolean;
  onBack?: () => void;
  children?: React.ReactNode;
  action?: React.ReactNode;
  bottom?: React.ReactNode;
  titleBottom?: string;
  iconAction?: string;
  iconActionPress?: () => void;
}

export default function TitleWithBackAction(props: Props) {
  const navigation = useNavigation<any>();
  const {t} = useTranslation();
  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <ScreenHeader
        backIcon={
          props.isClose ? (
            <View
              style={{
                paddingLeft: 16,
                paddingVertical: 8,
                paddingRight: 16,
                gap: 4,
                flexDirection: 'row',
                alignItems: 'center',
                alignSelf: 'baseline',
              }}>
              <Winicon src="outline/user interface/e-remove" size={20} />
              <Text
                style={{
                  ...TypoSkin.heading8,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}>
                {t('common.close')}
              </Text>
            </View>
          ) : null
        }
        onBack={() => {
          if (props.onBack) props.onBack();
          else navigation.goBack();
        }}
        action={
          <View style={{flexDirection: 'row', gap: 8, paddingRight: 16}}>
            {props.action ? props.action : null}
            {props.iconActionPress ? (
              <AppButton
                backgroundColor={'transparent'}
                borderColor="transparent"
                onPress={props.iconActionPress}
                containerStyle={{
                  borderRadius: 100,
                  padding: 6,
                  height: 32,
                  width: 32,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Main,
                }}
                title={
                  <Winicon
                    src={props.iconAction ?? 'fill/user interface/bell'}
                    size={18}
                    color={ColorThemes.light.Neutral_Text_Color_Title}
                  />
                }
              />
            ) : null}
          </View>
        }
        bottom={
          <View
            style={{
              width: '100%',
              paddingHorizontal: 16,
            }}>
            {props.titleBottom ? (
              <Text
                style={{
                  ...TypoSkin.heading5,
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}>
                {props.titleBottom ?? t('common.titleBottom')}
              </Text>
            ) : null}
            {props.bottom ? props.bottom : null}
          </View>
        }
      />
      {props.children ? props.children : null}
    </SafeAreaView>
  );
}
