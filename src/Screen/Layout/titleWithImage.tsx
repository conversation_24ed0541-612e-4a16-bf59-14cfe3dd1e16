import {useNavigation} from '@react-navigation/native';
import {Text, View} from 'react-native';
import ScreenHeader from './header';
import React from 'react';
import {AppButton, ListTile, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ProfileEschoolView} from '../Page/Home';

interface Props {
  title?: string;
  subTitle?: string;
  children?: React.ReactNode;
  action?: React.ReactNode;
  iconAction?: string;
  iconActionPress?: () => void;
  prefix?: React.ReactNode;
}

export default function TitleWithImage(props: Props) {
  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <ScreenHeader
        style={{paddingLeft: 16}}
        prefix={
          <View style={{flex: 1}}>
            <ListTile
              style={{padding: 0, alignItems: 'center'}}
              isClickLeading
              leading={
                props.prefix ? (
                  props.prefix
                ) : (
                  <View style={{width: 48, height: 48, borderRadius: 100}}>
                    <View
                      style={{
                        width: '100%',
                        height: '100%',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 100,
                      }}>
                      <Winicon src="fill/users/profile" size={24} />
                    </View>
                    {/* <SkeletonImage
                    source={{uri: ConfigAPI.imgUrlId + customer?.data?.Img}}
                    style={{width: 45, height: 45, borderRadius: 100}}
                  /> */}
                    <View
                      style={{
                        position: 'absolute',
                        padding: 5,
                        borderRadius: 24,
                        backgroundColor: '#fff',
                        right: -2,
                        bottom: -2,
                      }}>
                      <Winicon
                        src="fill/user interface/star"
                        size={12}
                        color={'#000'}
                      />
                    </View>
                  </View>
                )
              }
              title={
                <Text numberOfLines={1} style={{...TypoSkin.heading7}}>
                  {props.title ?? ''}
                </Text>
              }
              titleStyle={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}
              subtitle={props.subTitle}
              subTitleStyle={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}
            />
          </View>
        }
        action={
          <View style={{flexDirection: 'row', gap: 8, paddingRight: 16}}>
            {props.action ? props.action : null}
            <AppButton
              backgroundColor={'transparent'}
              borderColor="transparent"
              onPress={props.iconActionPress}
              containerStyle={{
                borderRadius: 100,
                padding: 6,
                height: 32,
                width: 32,
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Main,
              }}
              title={
                <Winicon
                  src={props.iconAction ?? 'fill/user interface/bell'}
                  size={18}
                  color={ColorThemes.light.Neutral_Text_Color_Title}
                />
              }
            />
            <ProfileEschoolView />
          </View>
        }
      />
      {props.children ? props.children : null}
    </SafeAreaView>
  );
}
