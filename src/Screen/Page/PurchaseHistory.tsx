/* eslint-disable react-native/no-inline-styles */
import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {AppButton, ListTile, TextField, Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import TitleWithBackAction from '../Layout/titleWithBackAction';
import {StatusOrder} from '../../Config/Contanst';
import {Ultis} from '../../utils/Utils';
import {CustomerDA} from '../../modules/customer/da';
import {CourseDA} from '../../modules/Course/da';
import {useTranslation} from 'react-i18next';

// Component hiển thị khi không có dữ liệu
const EmptyListComponent = () => {
  const {t} = useTranslation();
  return (
    <View
      style={{
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 32,
      }}>
      <Winicon
        src="outline/user interface/search-not-found"
        size={48}
        color={ColorThemes.light.Neutral_Text_Color_Subtitle}
      />
      <Text
        style={{
          ...TypoSkin.body2,
          color: ColorThemes.light.Neutral_Text_Color_Subtitle,
          marginTop: 16,
          textAlign: 'center',
        }}>
        {t('purchase.noOrders')}
      </Text>
    </View>
  );
};

// Component hiển thị khi đang tải thêm dữ liệu
const LoadingFooter = () => (
  <View style={{paddingVertical: 20, alignItems: 'center'}}>
    <ActivityIndicator
      size="small"
      color={ColorThemes.light.Primary_Color_Main}
    />
  </View>
);

const PurchaseHistory = () => {
  const {t} = useTranslation();
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const pageSize = 10;
  const customerDA = new CustomerDA();
  const courseDA = new CourseDA();
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);

  // Lấy dữ liệu lịch sử mua hàng
  const fetchPurchaseHistory = async (
    pageNumber: number,
    refresh = false,
    searchText = '',
  ) => {
    try {
      if (refresh) {
        setLoading(true);
      } else if (pageNumber > 1) {
        setLoadingMore(true);
      }

      const result = await customerDA.getPurchaseHistory(
        pageNumber,
        pageSize,
        searchText,
      );

      if (result && result.code === 200) {
        const orders = result.data || [];

        // Lấy thông tin chi tiết khóa học cho mỗi đơn hàng
        const ordersWithCourseDetails = await Promise.all(
          orders.map(async (order: any) => {
            if (order.CourseId) {
              const courseDetail = await courseDA.getCourseDetail(
                order.CourseId,
              );
              return {
                id: order.Id,
                courseName:
                  courseDetail?.data?.Name || t('course.undefinedCourse'),
                date: new Date(order.DateCreated),
                price: order.TotalPrice || order.Price || 0,
                status: order.Status,
                courseId: order.CourseId,
                orderId: order.OrderId,
              };
            }
            return {
              id: order.Id,
              courseName: t('course.undefinedCourse'),
              date: new Date(order.DateCreated),
              price: order.TotalPrice || order.Price || 0,
              status: order.Status,
              orderId: order.OrderId,
            };
          }),
        );

        if (refresh || pageNumber === 1) {
          setFilteredData(ordersWithCourseDetails);
          setInitialLoadComplete(true);
        } else {
          setFilteredData(prev => [...prev, ...ordersWithCourseDetails]);
        }

        // Kiểm tra xem còn dữ liệu để tải thêm không
        setHasMore(orders.length === pageSize);
      } else {
        // Xử lý khi không có dữ liệu hoặc lỗi
        if (refresh || pageNumber === 1) {
          setFilteredData([]);
          setInitialLoadComplete(true);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching purchase history:', error);
      if (refresh || pageNumber === 1) {
        setFilteredData([]);
        setInitialLoadComplete(true);
      }
      setHasMore(false);
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  // Tải dữ liệu khi component được mount
  useEffect(() => {
    fetchPurchaseHistory(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Xử lý refresh
  const onRefresh = () => {
    setRefreshing(true);
    setPage(1);
    setSearchValue('');
    fetchPurchaseHistory(1, true);
  };

  // Xử lý tìm kiếm
  const handleSearch = (text: string) => {
    setSearchValue(text);

    // Đặt timeout để tránh gọi API quá nhiều khi người dùng đang nhập
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    searchTimeout.current = setTimeout(() => {
      setPage(1);
      setLoading(true);
      fetchPurchaseHistory(1, false, text);
    }, 500);
  };

  // Hiển thị trạng thái đơn hàng
  const renderOrderStatus = (status: number) => {
    let statusText = '';
    let statusColor = '';
    let backgroundColor = '';

    switch (status) {
      case StatusOrder.success:
        statusText = t('purchase.orderStatus.completed');
        statusColor = ColorThemes.light.Success_Color_Main;
        backgroundColor = ColorThemes.light.Success_Color_Background;
        break;
      case StatusOrder.proccess:
        statusText = t('purchase.orderStatus.processing');
        statusColor = ColorThemes.light.Warning_Color_Main;
        backgroundColor = ColorThemes.light.Warning_Color_Background;
        break;
      case StatusOrder.cancel:
        statusText = t('purchase.orderStatus.cancelled');
        statusColor = ColorThemes.light.Error_Color_Main;
        backgroundColor = ColorThemes.light.Error_Color_Background;
        break;
      default:
        statusText = t('common.notFound');
        statusColor = ColorThemes.light.Neutral_Text_Color_Subtitle;
        backgroundColor = ColorThemes.light.Neutral_Background_Color_Lighter;
    }

    return (
      <View
        style={{
          paddingHorizontal: 8,
          paddingVertical: 4,
          borderRadius: 4,
          backgroundColor: backgroundColor,
        }}>
        <Text
          style={{
            ...TypoSkin.subtitle4,
            color: statusColor,
          }}>
          {statusText}
        </Text>
      </View>
    );
  };

  // Format date
  const formatDate = (date: Date) => {
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
  };

  // Render item
  const renderItem = ({item}: {item: any}) => {
    return (
      <ListTile
        style={{
          borderColor: ColorThemes.light.Neutral_Border_Color_Main,
          borderWidth: 1,
          borderRadius: 8,
          marginBottom: 16,
          padding: 0,
        }}
        listtileStyle={{
          padding: 16,
        }}
        title={item.courseName}
        titleStyle={{
          ...TypoSkin.heading7,
          color: ColorThemes.light.Neutral_Text_Color_Title,
        }}
        subtitle={
          <View style={{marginTop: 8, gap: 2}}>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
              <Winicon
                src="color/arrows/time-machine"
                size={14}
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}>
                {formatDate(item.date)}
              </Text>
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center', gap: 4}}>
              <Winicon
                src="outline/files/folder-money"
                size={14}
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}>
                {Ultis.money(item.price)} VNĐ
              </Text>
            </View>
          </View>
        }
        trailing={renderOrderStatus(item.status)}
      />
    );
  };

  return (
    <TitleWithBackAction titleBottom={t('purchase.history')}>
      <View
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        {/* Search bar */}
        <View
          style={{
            paddingHorizontal: 16,
            paddingVertical: 12,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          <TextField
            style={{paddingHorizontal: 16, height: 40}}
            onChange={handleSearch}
            value={searchValue}
            placeholder={t('course.search')}
            prefix={
              <Winicon
                src="outline/development/zoom"
                size={14}
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
            }
          />
        </View>

        {/* Order list */}
        <FlatList
          data={filteredData}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={{
            padding: 16,
            paddingTop: 8,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          onEndReached={() => {
            if (initialLoadComplete && !loadingMore && hasMore) {
              const nextPage = page + 1;
              setPage(nextPage);
              fetchPurchaseHistory(nextPage, false, searchValue);
            }
          }}
          onEndReachedThreshold={0.2}
          ListEmptyComponent={loading ? null : EmptyListComponent}
          ListFooterComponent={loadingMore ? LoadingFooter : null}
        />

        {/* Loading indicator */}
        {loading && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
            }}>
            <ActivityIndicator
              size="large"
              color={ColorThemes.light.Primary_Color_Main}
            />
          </View>
        )}
      </View>
    </TitleWithBackAction>
  );
};

export default PurchaseHistory;
