/* eslint-disable react-native/no-inline-styles */
import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {ScrollView, StyleSheet, View} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import TitleWithBottom from '../Layout/titleWithBottom';
import ByNewCategory from '../../modules/community/news/listview/byNewCates';
import ByNewTrending from '../../modules/community/news/listview/byTrending';

import {useNavigation} from '@react-navigation/native';

const NewsFeed = () => {
  const [isRefresh, setRefresh] = useState(false);
  const {t} = useTranslation();
  const navigation = useNavigation<any>();

  return (
    <TitleWithBottom title={t('social')} noAction>
      {/* Main content */}
      <ScrollView nestedScrollEnabled style={styles.container}>
        <ByNewCategory horizontal />
        <View style={{height: 40}} />

        <ByNewTrending
          titleList={t('trending')}
          isRefresh={isRefresh}
          setRefresh={setRefresh}
        />
      </ScrollView>
    </TitleWithBottom>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    width: '100%',
  },
});

export default NewsFeed;
