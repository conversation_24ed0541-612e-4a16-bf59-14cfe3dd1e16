import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {getIsLikeRating, getLikesRatingCourse} from '../../modules/rate/da';
import {CustomerDA} from '../../modules/customer/da';
import {DataController} from '../../base/baseController';
import {randomGID} from '../../utils/Utils';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {StorageContanst} from '../../Config/Contanst';
import store from '../store/store';
import {sub} from 'date-fns';
export const FETCH_COURSE_RATINGS = 'FETCH_COURSE_RATINGS_REQUEST';
export const ADD_RATING = 'ADD_RATING_REQUEST';
export const UPDATE_LIKE = 'UPDATE_LIKE';
export const ADD_COMMENT = 'ADD_COMMENT';
const initialState: {
  courseRatings: {
    list: any[];
    listP: any[];
    averageRating: number;
    totalCount: number;
  };
  data: any[];
  loading: boolean;
  success: boolean;
  error: any; // Hoặc để null|string nếu muốn cụ thể hơn
} = {
  courseRatings: {
    list: [],
    listP: [],
    averageRating: 0,
    totalCount: 0,
  },
  data: [],
  loading: true,
  success: false,
  error: null,
};
export const ratingSlice = createSlice({
  name: 'rating',
  initialState: initialState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case FETCH_COURSE_RATINGS:
          state.courseRatings.list = action.payload.list;
          state.courseRatings.listP = action.payload.listP;
          state.courseRatings.averageRating = action.payload.Average;
          state.courseRatings.totalCount = action.payload.totalCount ?? 0;
          break;
        case ADD_RATING:
          const updatedList = [
            ...state.courseRatings.list,
            action.payload.data,
          ];
          const updatedListP = [
            ...state.courseRatings.listP,
            action.payload.data,
          ];
          const newTotalCount = state.courseRatings.totalCount + 1;
          const newAverage =
            newTotalCount > 0
              ? updatedList.reduce((sum, item) => sum + item.Value, 0) /
                newTotalCount
              : 0;
          state.courseRatings.list = updatedList;
          state.courseRatings.listP = updatedListP;
          state.courseRatings.averageRating = newAverage;
          state.courseRatings.totalCount = newTotalCount;
          break;
        case UPDATE_LIKE:
          state.courseRatings.list = [
            ...state.courseRatings.list.map((rating: any) => {
              if (rating.Id === action.payload.Id) {
                // Cập nhật thông tin likes
                return {
                  ...rating,
                  Likes:
                    action.payload.IsLike === true
                      ? (rating.Likes ?? 0) + 1
                      : (rating.Likes ?? 0) - 1,
                  IsLike: action.payload.IsLike,
                  //   likedByCurrentUser: action.payload.likeData.liked,
                };
              }
              return rating;
            }),
          ];
          break;
        case ADD_COMMENT:
          const ListComment = [
            ...state.courseRatings.list,
            action.payload.data,
          ];
          state.courseRatings.list = ListComment;
          break;
        default:
          break;
      }
      state.loading = false;
    },
    onFetching: state => {
      state.loading = true;
    },
    // onReset: (state) => {

    // }
  },
});
export default ratingSlice.reducer;
const {handleActions, onFetching} = ratingSlice.actions;
const customerDA = new CustomerDA();
export class RatingActions {
  static getRatingCourse = (id: string) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const courseController = new DataController('Rating');
    const courseResult = await courseController.getPatternList({
      query: `@CourseId: {${id}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'Img'],
      },
    });
    if (courseResult.code === 200) {
      const ratingPromises = courseResult.data.map(async (item: any) => {
        // Tạo copy của item để tránh mutate dữ liệu gốc
        const itemCopy = {...item};

        try {
          // Lấy dữ liệu customer
          const customer = courseResult.Customer.find(
            (cus: any) => cus.Id === item.CustomerId,
          );
          // Lấy số lượt thích
          const likes = await getLikesRatingCourse(item.Id);
          const islikes = await getIsLikeRating(item.Id);

          // Đếm số lượng comment cho rating này
          const commentCount =
            courseResult.data.filter((t: any) => t.ParentId === item.Id)
              .length || 0;

          if (customer) {
            return {
              ...itemCopy,
              Name: null,
              Content: itemCopy.Message,
              Likes: likes,
              IsLike: islikes,
              Comment: commentCount,
              relativeUser: {
                image: customer.Img,
                title: `${customer.Name}`,
                subtitle: itemCopy.DateCreated,
              },
            };
          } else {
            return {
              ...itemCopy,
              Likes: likes,
              Comment: commentCount,
              IsLike: islikes,
            };
          }
        } catch (error) {
          console.error('Error processing rating item:', error);
          // Trả về bản copy với thông tin lỗi
          return {
            ...itemCopy,
            Likes: 0,
            Comment: 0,
            hasError: true,
          };
        }
      });
      const lst2 = await Promise.all(ratingPromises);
      var list = lst2.filter((item: any) => item.ParentId == null);
      const Average =
        list.length > 0
          ? lst2.reduce((sum: number, item: any) => sum + item.Value, 0) /
            list.length
          : 0;
      dispatch(
        handleActions({
          type: FETCH_COURSE_RATINGS,
          list: lst2,
          listP: list,
          Average: Average,
          totalCount: list.length,
        }),
      );
    }
  };
  static addRating = (data: any) => async (dispatch: Dispatch) => {
    const courseController = new DataController('Rating');
    const courseResult = await courseController.add([data]);
    if (courseResult.code === 200) {
      const dataMap = {
        ...data,
        Likes: 0,
        Content: data.Content,
        IsLike: false,
        Comment: 0,
        relativeUser: {
          image: store.getState().customer.data.avatarUrl,
          title: `${store.getState().customer.data.Name}`,
          subtitle: new Date().getTime(),
        },
      };
      dispatch(
        handleActions({
          type: ADD_RATING,
          data: dataMap,
        }),
      );
    }
  };
  static updateLike =
    (id: string, isUnLike: boolean) => async (dispatch: Dispatch) => {
      const likeController = new DataController('Like_Rating');
      var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);

      if (cusId) {
        if (isUnLike === true) {
          const result = await likeController.getListSimple({
            query: `@CustomerId: {${cusId}} @RatingId:{${id}}`,
          });
          if (result.data?.length > 0) {
            const unlike = await likeController.delete([result.data[0].Id]);
            if (unlike.code === 200) {
              dispatch(
                handleActions({
                  type: UPDATE_LIKE,
                  Id: id,
                  IsLike: false,
                }),
              );
            }
          }
        } else {
          const data = {
            Id: randomGID(),
            CustomerId: cusId,
            RatingId: id,
            DateCreated: new Date().getTime(),
          };
          const Result = await likeController.add([data]);
          if (Result.code === 200) {
            dispatch(
              handleActions({
                type: UPDATE_LIKE,
                Id: id,
                IsLike: true,
              }),
            );
          }
        }
      }
    };
  static addCommentRating =
    (id: string, content: string, courseId: string) =>
    async (dispatch: Dispatch) => {
      const courseController = new DataController('Rating');
      var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
      if (cusId) {
        const customer = store.getState().customer.data;
        const data = {
          Id: randomGID(),
          ParentId: id,
          CustomerId: cusId,
          CourseId: courseId,
          Message: content,
          DateCreated: new Date().getTime(),
        };
        const courseResult = await courseController.add([data]);
        if (courseResult.code === 200) {
          const dataMap = {
            ...data,
            Likes: 0,
            Content: data.Message,
            IsLike: false,
            Comment: 0,
            relativeUser: {
              image: customer.data.Img,
              title: `${customer.data.Name}`,
              DateCreated: new Date().getTime(),
            },
          };
          dispatch(
            handleActions({
              type: ADD_COMMENT,
              data: dataMap,
            }),
          );
        }
      }
    };
}
