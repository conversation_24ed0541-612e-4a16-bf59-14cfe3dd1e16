import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {DataController} from '../../base/baseController';
import {examDA} from '../../modules/exam/da';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {StorageContanst} from '../../Config/Contanst';
export const FETCH_EXAM_DATA = 'FETCH_EXAM_DATA';
export const CHOOSE = 'CHOOSE';
export const MULTI_CHOOSE = 'MULTI_CHOOSE';
export const SUBMIT_EXAM = 'SUBMIT_EXAM';
const initialState: {
  examInfor: any;
  listQuestion: any[];
  loading: boolean;
  success: boolean;
  error: any; // Hoặc để null|string nếu muốn cụ thể hơn
} = {
  examInfor: null,
  listQuestion: [],
  loading: true,
  success: false,
  error: null,
};
export const examSlice = createSlice({
  name: 'exam',
  initialState: initialState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case FETCH_EXAM_DATA:
          state.examInfor = action.payload.examInfor;
          state.listQuestion = action.payload.listQuestion;
          break;
        case SUBMIT_EXAM:
          var totalS = 0;
          var score = 0;
          var updateListQ = [
            ...state.listQuestion.map((item: any) => {
              totalS += item.Score ?? 0;
              var isResultDetail = false;
              var noResult = false;
                debugger

              if (item.SelectionType === 1) {
                // tính điểm với câu hỏi chọn 1
                if (
                  item.lstAnswer.some(
                    (a: any) => a.choose === true && a.IsResult === true,
                  )
                ) {
                  score += item.Score ?? 1;
                  isResultDetail = true;
                }
              } else {
                // tính điểm với câu hỏi chọn nhiều - Lấy hết đáp án cần phải tích đúng. kiểm tra xem user chọn hết các đáp án đấy k
               
                // kiểm tra xem đáp án user chọn có đủ vs đáp án đúng của câu chọn nhiều ko
                var wrongChoices = item.lstAnswer.filter(
                (i: any) => i.IsResult !== true && i.choose === true,
              );
              var correctAnswers = item.lstAnswer.filter(
                (i: any) => i.IsResult === true,
              );
                 if (
                !correctAnswers.some((a: any) => a.choose !== true) &&
                wrongChoices.length === 0
              ) {
                  score += item.Score ?? 1;
                  isResultDetail = true;
                }
              }
              // kiểm tra xem câu hỏi hiện tại có câu trả lời ko.
              if (!item.lstAnswer.some((a: any) => a.choose === true)) {
                noResult = true;
              }
              return {
                ...item,
                IsResultTest: isResultDetail,
                noResult: noResult,
              };
            }),
          ];
          state.listQuestion = updateListQ;
          state.examInfor = {
            ...state.examInfor,
            CountTest: state.examInfor.CountTest + 1,
            TotalScore: totalS,
            ScoreResult: score,
          };
          break;
        case CHOOSE:
          var updatedList = [
            ...state.listQuestion.map(item => {
              if (item.Id === action.payload.Id) {
                return {
                  ...item,
                  lstAnswer: item.lstAnswer.map((a: any) => {
                    if (item.SelectionType === 2) {
                      if (a.Id === action.payload.answerId) {
                        const test = {
                          ...a,
                          choose: a.choose === true ? false : true,
                        };
                        return test;
                      }
                      return a;
                    } else {
                      if (a.Id === action.payload.answerId) {
                        return {
                          ...a,
                          choose: true,
                        };
                      }
                      return {...a, choose: false};
                    }
                  }),
                };
              }
              return item;
            }),
          ];
          state.listQuestion = updatedList;
          break;
        case MULTI_CHOOSE:
          var updatedList = [
            ...state.listQuestion.map(item => {
              if (item.Id === action.payload.Id) {
                return {
                  ...item,
                  lstAnswer: item.lstAnswer.map((a: any) => {
                    if (action.payload.ids.some((t: any) => t === a.Id)) {
                      return {
                        ...a,
                        choose: a.choose === true ? false : true,
                      };
                    }
                    return a;
                  }),
                };
              }
              return item;
            }),
          ];
          state.listQuestion = updatedList;
          break;
        default:
          break;
      }
      state.loading = false;
    },
    onFetching: state => {
      state.loading = true;
    },
    onReset: state => {
      var updatedList = [
        ...state.listQuestion.map(item => {
          return {
            ...item,
            lstAnswer: item.lstAnswer.map((a: any) => {
              return {
                ...a,
                choose: false,
              };
            }),
          };
        }),
      ];
      state.listQuestion = updatedList;
    },
  },
});
export default examSlice.reducer;
const {handleActions, onFetching, onReset} = examSlice.actions;
const examda = new examDA();
export class ExamActions {
  static getExam = (id: string) => async (dispatch: Dispatch) => {
    dispatch(onFetching());
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const examController = new DataController('Exam');
    const examResult = await examController.getById(id);

    if (examResult.code === 200) {
      const questController = new DataController('Question');
      const questionResult = await questController.getListSimple({
        query: `@Id: {${examResult.data.QuestionId.replaceAll(',', '|')}}`,
      });
      const testController = new DataController('Test_Result');
      var count = 0;
      var tests = [];
      const testResult = await testController.getListSimple({
        query: `@CustomerId: {${cusId}} @ExamId: {${examResult.data.Id}}`,
      });

      if (testResult.code === 200) {
        count = testResult?.data?.length ?? 0;
        tests = testResult?.data ?? [];
      }
      if (questionResult.code === 200) {
        const lst2 = await Promise.all(
          questionResult.data.map(async (item: any) => {
            const answer = await examda.getListAnswer(item.Id); // Gọi API lấy 2 field
            if (answer) {
              return {
                ...item,
                lstAnswer: answer?.data,
              };
            } else {
              return {
                ...item,
                lstAnswer: [],
              };
            }
          }),
        );
        debugger
        dispatch(
          handleActions({
            type: FETCH_EXAM_DATA,
            examInfor: {
              ...examResult?.data,
              CountTest: count,
              TestResults: tests,
            },
            listQuestion: lst2,
          }),
        );
      }
    }
  };
  static choose =
    (questId: any, answerId: any) => async (dispatch: Dispatch) => {
      dispatch(
        handleActions({
          type: CHOOSE,
          Id: questId,
          answerId: answerId,
        }),
      );
    };
  static chooseMulti = (id: any, ids: any) => async (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: MULTI_CHOOSE,
        Id: id,
        lstAnswer: ids,
      }),
    );
  };
  static resetReducer = () => async (dispatch: Dispatch) => {
    dispatch(onReset());
  };
  static examSubmit =
    (data: any) => async (dispatch: Dispatch) => {
      dispatch(onFetching());
      const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
      if (cusId) {
          const testDetailController = new DataController('Test_Result');
          const detailResult = await testDetailController.add([data]);
          debugger
          if (detailResult.code === 200) {
            dispatch(
              handleActions({
                type: SUBMIT_EXAM,
              }),
            );
          }
      }
    };
}
