import {createSlice} from '@reduxjs/toolkit';
export interface Word {
  id: number;
  text: string;
  position: number;
}
const initialState = {
  availableWords: [] as Word[],
  questionDone: 0,
  totalQuestion: 5,
  dropZoneLayout: {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  },
};

const data = [
  {id: 1, text: 'I', position: 1},
  {id: 2, text: 'eat', position: 2},
  {id: 3, text: 'breakfast', position: 3},
  {id: 4, text: 'at', position: 4},
  {id: 5, text: '7', position: 5},
  {id: 6, text: "o'clock", position: 6},
];

export const SakuLCReducer = createSlice({
  name: 'sakuLCReducer',
  initialState,
  reducers: {
    setData(state, action) {
      state[action.payload.stateName] = action.payload.value;
    },
    startGame(state) {
      state.availableWords = data;
      state.questionDone = 0;
      state.totalQuestion = 5;
    },
    reset(state) {
      state.availableWords = data;
      state.questionDone = 0;
      state.totalQuestion = 5;
    },
  },
});

export const {setData, reset, startGame} = SakuLCReducer.actions;

export default SakuLCReducer.reducer;
