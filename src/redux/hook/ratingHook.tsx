import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '../store/store';
import { useEffect } from 'react';
import { RatingActions } from '../reducers/ratingReducer';

export function useRatingData(courseId: string) {
      const dispatch: AppDispatch = useDispatch();
    const ratings = useSelector((state: RootState) => state.ratings.courseRatings);
    const loading = useSelector((state: RootState) => state.ratings.loading);
    const error = useSelector((state: RootState) => state.ratings.error);
    useEffect(() => {
        if(loading === true){
            dispatch(RatingActions.getRatingCourse(courseId));
        }
    }, [dispatch, courseId,ratings.list, loading]);
    return { ratings, loading, error };
}
