import {useDispatch} from 'react-redux';
import {setData, gameOver, restartGame, reset} from '../reducers/gameReducer';

export const useGameHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    pauseGame: () => {
      dispatch(setData({stateName: 'isRunTime', value: false}));
    },
    continueGame: () => {
      dispatch(setData({stateName: 'isRunTime', value: true}));
    },
    resetGame: () => {
      dispatch(reset());
    },
    gameOver: (message: string) => {
      dispatch(gameOver(message));
    },
    restartGame: () => {
      dispatch(restartGame());
    },
  };

  return action;
};
