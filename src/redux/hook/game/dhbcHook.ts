import {useDispatch} from 'react-redux';
import {
  nextQuestion,
  startGame,
  setData,
  loadDHBCGameConfig,
  loadDHBCQuestions,
} from '../../reducers/game/dhbcReducer';
import {AppDispatch} from '../../store/store';

export const useDhbcHook = () => {
  const dispatch = useDispatch<AppDispatch>();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    nextQuestion: () => {
      dispatch(nextQuestion());
    },
    loadGameConfig: (gameId: string) => {
      return dispatch(loadDHBCGameConfig({ gameId }));
    },
    loadQuestions: (gameId: string, milestoneId: number, competenceId: string) => {
      return dispatch(loadDHBCQuestions({ gameId, milestoneId, competenceId }));
    },
  };

  return action;
};
