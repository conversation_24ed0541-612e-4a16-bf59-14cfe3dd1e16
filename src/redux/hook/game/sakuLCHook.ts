import {useDispatch} from 'react-redux';
import {reset, setData, startGame} from '../../reducers/game/sakuLCReducer';

export const useSakuLCHook = () => {
  const dispatch = useDispatch();

  const action = {
    setData: (data: any) => {
      dispatch(setData(data));
    },
    startGame: () => {
      dispatch(startGame());
    },
    reset: () => {
      dispatch(reset());
    },
  };

  return action;
};
