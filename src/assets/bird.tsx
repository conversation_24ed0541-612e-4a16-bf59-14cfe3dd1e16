import * as React from 'react';
import Svg, {<PERSON>, <PERSON>, <PERSON>, Defs, ClipPath} from 'react-native-svg';
const BirdSVG = (props: any) => (
  <Svg
    width={props.width ?? 200}
    height={props.height ?? 200}
    viewBox={`0 0 ${props.width ?? 200} ${props.height ?? 200}`}
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <G clipPath="url(#a)">
      <Path
        d="M28.476 144.776C4.91 119.585-6.82 74.454 4.134 63.636 15.1 52.804 53.673 68.137 77.238 93.328s40.482 16.281 33.623 31.324c-10.98 10.832-24.895 63.557-82.386 20.137z"
        fill="#5CA0D3"
      />
      <Mask
        id="b"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={-1}
        y={60}
        width={114}
        height={102}>
        <Path
          d="M28.476 144.776C4.91 119.585-6.82 74.454 4.134 63.636 15.1 52.804 53.673 68.137 77.238 93.328s40.482 16.281 33.623 31.324c-10.98 10.832-24.895 63.557-82.386 20.137z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#b)">
        <Path
          d="M6.424 96.118c-.368.237-2.606-2.764-3.673-7.173-1.08-4.409-.474-8.094-.04-8.068.488.013.66 3.54 1.673 7.66 1.014 4.146 2.475 7.344 2.04 7.581"
          fill="#fff"
          opacity={0.35}
        />
      </G>
      <Mask
        id="c"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={-1}
        y={60}
        width={114}
        height={102}>
        <Path
          d="M28.476 144.776C4.91 119.585-6.82 74.454 4.134 63.636 15.1 52.804 53.673 68.137 77.238 93.328s40.482 16.281 33.623 31.324c-10.98 10.832-24.895 63.557-82.386 20.137z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#c)">
        <Path
          d="M63.441 158.727c-.013.435-5.963.25-12.756-2.303-3.384-1.25-6.267-2.83-8.242-4.119-.987-.645-1.79-1.33-2.159-1.948-.381-.606-.381-1.027-.302-1.066.118-.053.342.25.79.645.46.408 1.21.842 2.238 1.329 2.066 1.013 4.95 2.356 8.24 3.567 6.57 2.487 12.244 3.395 12.178 3.909z"
          fill="#fff"
          opacity={0.39}
        />
      </G>
      <Path
        d="M77.225 93.315C53.66 68.124 15.087 52.817 4.121 63.635c-1.198 1.185-2.028 2.633-2.489 4.291 4.95 6.555 10.48 13.07 16.154 19.124 23.565 25.191 58.123 28.087 69.089 17.255a12.5 12.5 0 0 0 1.132-1.264c-3.383-2.527-6.99-5.685-10.782-9.74z"
        fill="#286EAD"
      />
      <Path
        d="M41.377 106.424C17.812 81.233-2.963 48.382 7.991 37.55c10.966-10.832 40.824 11.213 64.39 36.418 23.564 25.19 49.051 38.905 38.085 49.724-10.98 10.805-45.524 7.936-69.089-17.281z"
        fill="#5CA0D3"
      />
      <Mask
        id="d"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={4}
        y={34}
        width={110}
        height={96}>
        <Path
          d="M41.377 106.424C17.812 81.233-2.963 48.382 7.991 37.55c10.966-10.832 40.824 11.213 64.39 36.418 23.564 25.19 49.051 38.905 38.085 49.724-10.98 10.805-45.524 7.936-69.089-17.281z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#d)">
        <Path
          d="M6.28 48.46c-.422.172-1.514-2.026-.948-4.83.553-2.83 2.396-4.422 2.712-4.106.368.303-.62 2.066-1.067 4.448-.487 2.33-.25 4.344-.71 4.502z"
          fill="#fff"
          opacity={0.37}
        />
      </G>
      <Mask
        id="e"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={4}
        y={34}
        width={110}
        height={96}>
        <Path
          d="M41.377 106.424C17.812 81.233-2.963 48.382 7.991 37.55c10.966-10.832 40.824 11.213 64.39 36.418 23.564 25.19 49.051 38.905 38.085 49.724-10.98 10.805-45.524 7.936-69.089-17.281z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#e)">
        <Path
          d="M52.436 113.11c-.237.381-4.095-1.606-7.886-5.304-3.818-3.672-5.924-7.476-5.556-7.713.408-.263 3.054 2.961 6.74 6.502 3.634 3.567 6.952 6.107 6.702 6.515"
          fill="#fff"
          opacity={0.39}
        />
      </G>
      <Path
        d="M52.515 101.449c23.38 24.993 49.012 29.771 60.137 19.281 5.897-11.371-18.695-23.704-40.272-46.776C53.686 53.95 34.23 33.457 17.93 34.022 9.926 46.131 29.963 77.297 52.528 101.45z"
        fill="#286EAD"
      />
      <Path
        d="M49.092 93.012C29.529 64.347 8.768 16.544 23.289 5.33 37.823-5.883 50.632 34.746 70.195 63.425c19.576 28.679 55.161 49.974 40.64 61.188-14.521 11.227-42.18-2.935-61.743-31.614z"
        fill="#5CA0D3"
      />
      <Mask
        id="f"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={18}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M49.092 93.012C29.529 64.347 8.768 16.544 23.289 5.33 37.823-5.883 50.632 34.746 70.195 63.425c19.576 28.679 55.161 49.974 40.64 61.188-14.521 11.227-42.18-2.935-61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#f)">
        <Path
          d="M40.416 19.347c-.987-2.171-2.37-4.159-4.028-5.896-1.764-1.869-4.029-3.685-6.583-4.238-2.764-.605-5.648.158-7.451 2.409-1.83 2.29-2.251 5.014-2.554 7.857-.132 1.316 1.264 2.58 2.528 2.527 1.487-.066 2.37-1.106 2.527-2.527.119-1.04.171-1.737.487-2.83.066-.237.158-.474.237-.71.132-.435-.237.42.08-.185.105-.17.184-.355.289-.526.079-.132.29-.434.079-.171.158-.185.316-.343.474-.527.303-.316-.106.013.118-.118.171-.106.316-.224.5-.303-.355.171.356-.079.382-.105.237-.053.461-.066.974 0-.368-.053.422.118.422.118.066.013.619.224.474.158.619.263 1.487.856 1.975 1.264a19 19 0 0 1 1.948 1.921c.158.171.29.316.421.487 0 0 .474.606.25.303.25.355.5.71.737 1.08a20 20 0 0 1 1.383 2.513c.553 1.198 2.396 1.606 3.449.908 1.198-.763 1.461-2.119.856-3.422z"
          fill="#fff"
          opacity={0.37}
        />
      </G>
      <Mask
        id="g"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={18}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M49.092 93.012C29.529 64.347 8.768 16.544 23.289 5.33 37.823-5.883 50.632 34.746 70.195 63.425c19.576 28.679 55.161 49.974 40.64 61.188-14.521 11.227-42.18-2.935-61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#g)">
        <Path
          d="M42.101 30.824c3.252 0 3.252-5.054 0-5.054s-3.252 5.054 0 5.054"
          fill="#fff"
          opacity={0.37}
        />
      </G>
      <Mask
        id="h"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={18}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M49.092 93.012C29.529 64.347 8.768 16.544 23.289 5.33 37.823-5.883 50.632 34.746 70.195 63.425c19.576 28.679 55.161 49.974 40.64 61.188-14.521 11.227-42.18-2.935-61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#h)">
        <Path
          d="M74.395 73.73c.381-.289 3.936 3.923 8.794 8.674 4.818 4.764 9.097 8.265 8.807 8.66-.25.369-5.069-2.592-9.992-7.462-4.95-4.844-7.978-9.621-7.61-9.871"
          fill="#fff"
          opacity={0.33}
        />
      </G>
      <Mask
        id="i"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={18}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M49.092 93.012C29.529 64.347 8.768 16.544 23.289 5.33 37.823-5.883 50.632 34.746 70.195 63.425c19.576 28.679 55.161 49.974 40.64 61.188-14.521 11.227-42.18-2.935-61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#i)">
        <Path
          d="M31.543 59.503c-.382.237-1.988-1.816-3.054-4.778-1.08-2.96-1.146-5.554-.685-5.646.474-.079 1.264 2.25 2.278 5.067 1.013 2.817 1.882 5.12 1.46 5.357"
          fill="#fff"
          opacity={0.33}
        />
      </G>
      <Mask
        id="j"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={18}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M49.092 93.012C29.529 64.347 8.768 16.544 23.289 5.33 37.823-5.883 50.632 34.746 70.195 63.425c19.576 28.679 55.161 49.974 40.64 61.188-14.521 11.227-42.18-2.935-61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#j)">
        <Path
          d="M60.11 102.528c-.263.355-3.922-2.211-7.227-6.528-3.317-4.317-4.845-8.49-4.45-8.674.435-.223 2.58 3.501 5.78 7.647 3.172 4.16 6.214 7.186 5.898 7.555"
          fill="#fff"
          opacity={0.39}
        />
      </G>
      <Path
        d="M345.524 144.776c23.566-25.191 35.309-70.322 24.342-81.153s-49.539 4.5-73.104 29.692c-23.565 25.191-40.482 16.281-33.623 31.324 10.979 10.832 24.895 63.557 82.385 20.137"
        fill="#5CA0D3"
      />
      <Mask
        id="k"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={261}
        y={60}
        width={114}
        height={102}>
        <Path
          d="M345.524 144.776c23.566-25.191 35.309-70.322 24.342-81.153s-49.539 4.5-73.104 29.692c-23.565 25.191-40.482 16.281-33.623 31.324 10.979 10.832 24.895 63.557 82.385 20.137"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#k)">
        <Path
          d="M367.576 96.118c-.422-.237 1.04-3.435 2.027-7.58 1.027-4.12 1.198-7.648 1.672-7.66.434-.053 1.04 3.658-.04 8.067-1.066 4.41-3.278 7.41-3.659 7.173"
          fill="#fff"
          opacity={0.35}
        />
      </G>
      <Mask
        id="l"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={261}
        y={60}
        width={114}
        height={102}>
        <Path
          d="M345.524 144.776c23.566-25.191 35.309-70.322 24.342-81.153s-49.539 4.5-73.104 29.692c-23.565 25.191-40.482 16.281-33.623 31.324 10.979 10.832 24.895 63.557 82.385 20.137"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#l)">
        <Path
          d="M310.559 158.727c-.066-.487 5.608-1.408 12.164-3.895 3.278-1.211 6.148-2.58 8.241-3.567 1.04-.487 1.791-.948 2.238-1.329.474-.382.672-.685.79-.645.105.039.079.46-.303 1.066-.368.605-1.145 1.316-2.159 1.948-1.974 1.303-4.844 2.869-8.241 4.119-6.767 2.527-12.717 2.711-12.73 2.29z"
          fill="#fff"
          opacity={0.39}
        />
      </G>
      <Path
        d="M296.788 93.315c23.565-25.191 62.138-40.498 73.105-29.692 1.198 1.184 2.027 2.632 2.488 4.29-4.95 6.555-10.479 13.07-16.154 19.124-23.565 25.191-58.122 28.086-69.089 17.255-.421-.408-.789-.83-1.132-1.264 3.384-2.527 6.991-5.686 10.782-9.74z"
        fill="#286EAD"
      />
      <Path
        d="M332.623 106.424c23.565-25.191 44.352-58.042 33.386-68.874s-40.824 11.213-64.389 36.418-49.053 38.905-38.086 49.724c10.979 10.805 45.524 7.936 69.089-17.281z"
        fill="#5CA0D3"
      />
      <Mask
        id="m"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={260}
        y={34}
        width={110}
        height={96}>
        <Path
          d="M332.623 106.424c23.565-25.191 44.352-58.042 33.386-68.874s-40.824 11.213-64.389 36.418-49.053 38.905-38.086 49.724c10.979 10.805 45.524 7.936 69.089-17.281z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#m)">
        <Path
          d="M367.734 48.46c-.461-.131-.224-2.158-.711-4.514-.435-2.356-1.435-4.146-1.067-4.448.316-.316 2.173 1.303 2.712 4.106.58 2.83-.526 5.014-.948 4.843z"
          fill="#fff"
          opacity={0.37}
        />
      </G>
      <Mask
        id="n"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={260}
        y={34}
        width={110}
        height={96}>
        <Path
          d="M332.623 106.424c23.565-25.191 44.352-58.042 33.386-68.874s-40.824 11.213-64.389 36.418-49.053 38.905-38.086 49.724c10.979 10.805 45.524 7.936 69.089-17.281z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#n)">
        <Path
          d="M321.578 113.11c-.264-.408 3.067-2.948 6.714-6.515 3.686-3.541 6.332-6.765 6.74-6.502.369.237-1.738 4.027-5.555 7.713-3.805 3.685-7.662 5.672-7.899 5.304"
          fill="#fff"
          opacity={0.39}
        />
      </G>
      <Path
        d="M321.486 101.449c-23.381 24.993-49.013 29.771-60.137 19.281-5.898-11.371 18.694-23.704 40.271-46.776 18.694-20.005 38.151-40.497 54.45-39.932 8.004 12.109-12.033 43.275-34.598 67.427z"
        fill="#286EAD"
      />
      <Path
        d="M324.908 93.012c19.576-28.679 40.324-76.468 25.803-87.682-14.534-11.213-27.343 29.416-46.906 58.095-19.576 28.679-55.161 49.974-40.64 61.188 14.521 11.227 42.18-2.935 61.743-31.614z"
        fill="#5CA0D3"
      />
      <Mask
        id="o"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={259}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M324.908 93.012c19.576-28.679 40.324-76.468 25.803-87.682-14.534-11.213-27.343 29.416-46.906 58.095-19.576 28.679-55.161 49.974-40.64 61.188 14.521 11.227 42.18-2.935 61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#o)">
        <Path
          d="M337.942 21.9a18.5 18.5 0 0 1 1.382-2.513c.237-.369.487-.724.737-1.08-.184.25.105-.118.171-.197.132-.17.29-.342.421-.5a19.6 19.6 0 0 1 2.041-2c.474-.409 1.343-.988 1.975-1.264.618-.25 1.382-.382 1.869-.263.066.013.672.223.461.131-.224-.105.29.172.342.198.527.316-.197-.263.224.184.132.132.263.263.408.421.171.198-.118-.237.079.158.119.198.237.408.356.619.079.17.118.29.039.04.105.262.198.552.29.842.316 1.092.368 1.79.487 2.83.131 1.316 1.066 2.592 2.527 2.526 1.251-.052 2.673-1.105 2.528-2.527-.303-2.79-.711-5.501-2.488-7.778-1.804-2.303-4.7-3.093-7.517-2.488-2.528.553-4.753 2.343-6.504 4.16a22.4 22.4 0 0 0-4.12 5.975c-.553 1.197-.369 2.777.908 3.448 1.08.592 2.831.369 3.423-.908z"
          fill="#fff"
          opacity={0.37}
        />
      </G>
      <Mask
        id="p"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={259}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M324.908 93.012c19.576-28.679 40.324-76.468 25.803-87.682-14.534-11.213-27.343 29.416-46.906 58.095-19.576 28.679-55.161 49.974-40.64 61.188 14.521 11.227 42.18-2.935 61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#p)">
        <Path
          d="M331.899 30.824c3.252 0 3.252-5.054 0-5.054s-3.265 5.054 0 5.054"
          fill="#fff"
          opacity={0.37}
        />
      </G>
      <Mask
        id="q"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={259}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M324.908 93.012c19.576-28.679 40.324-76.468 25.803-87.682-14.534-11.213-27.343 29.416-46.906 58.095-19.576 28.679-55.161 49.974-40.64 61.188 14.521 11.227 42.18-2.935 61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#q)">
        <Path
          d="M299.605 73.73c.369.25-2.659 5.015-7.596 9.872-4.923 4.87-9.742 7.818-9.992 7.462-.289-.381 3.989-3.895 8.807-8.66 4.832-4.751 8.386-8.976 8.781-8.673"
          fill="#fff"
          opacity={0.33}
        />
      </G>
      <Mask
        id="r"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={259}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M324.908 93.012c19.576-28.679 40.324-76.468 25.803-87.682-14.534-11.213-27.343 29.416-46.906 58.095-19.576 28.679-55.161 49.974-40.64 61.188 14.521 11.227 42.18-2.935 61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#r)">
        <Path
          d="M342.457 59.503c-.421-.237.461-2.54 1.461-5.357 1.027-2.816 1.817-5.159 2.278-5.067.461.066.368 2.659-.685 5.646-1.066 2.962-2.659 5.015-3.054 4.778"
          fill="#fff"
          opacity={0.33}
        />
      </G>
      <Mask
        id="s"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={259}
        y={3}
        width={97}
        height={126}>
        <Path
          d="M324.908 93.012c19.576-28.679 40.324-76.468 25.803-87.682-14.534-11.213-27.343 29.416-46.906 58.095-19.576 28.679-55.161 49.974-40.64 61.188 14.521 11.227 42.18-2.935 61.743-31.614z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#s)">
        <Path
          d="M313.889 102.528c-.316-.369 2.712-3.396 5.898-7.555 3.199-4.146 5.345-7.844 5.78-7.647.408.172-1.133 4.357-4.45 8.674-3.305 4.317-6.938 6.87-7.228 6.528"
          fill="#fff"
          opacity={0.39}
        />
      </G>
      <Path
        d="M271.959 206.109c-.105.105-.197.223-.289.316-32.175 35.272-76.712 35.443-85.73 35.483-7.767.013-29.779-.197-52.922-11.029-9.413-4.423-18.997-10.569-27.897-19.177-.158-.131-.303-.289-.461-.434-39.744-38.879-31.845-94.328-30.542-102.567C81.872 60.03 122.315.908 184.216.013c63.099-.895 105.318 59.345 113.52 108.688.461 2.698 1.106 7.186 1.461 12.977 1.264 20.005-.974 55.37-27.251 84.431z"
        fill="#5CA0D3"
      />
      <Path
        d="M176.343 263.717v-44.46H147.96v44.46c-4.016 1.921-6.807 6.014-6.807 10.726v1.408c0 6.555 5.359 11.912 11.915 11.912h18.193c6.557 0 11.915-5.357 11.915-11.912v-1.408c-.014-4.712-2.818-8.805-6.82-10.726z"
        fill="#D9B382"
      />
      <Mask
        id="t"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={141}
        y={219}
        width={43}
        height={69}>
        <Path
          d="M176.343 263.717v-44.46H147.96v44.46c-4.016 1.921-6.807 6.014-6.807 10.726v1.408c0 6.555 5.359 11.912 11.915 11.912h18.193c6.557 0 11.915-5.357 11.915-11.912v-1.408c-.014-4.712-2.818-8.805-6.82-10.726z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#t)">
        <Path
          d="M132.057 279.497c3.975 4.291 9.215 7.292 14.81 8.924 5.556 1.632 11.519 1.645 17.259 1.513 10.993-.263 20.985-3.251 30.016-9.594 1.856-1.317 2.725-3.686 1.514-5.765-1.08-1.83-3.897-2.83-5.766-1.514-7.662 5.383-15.785 8.134-25.132 8.424-9.755.302-19.695-.356-26.751-7.963-3.699-3.962-9.65 2-5.964 5.975z"
          fill="#BF8C55"
        />
      </G>
      <Mask
        id="u"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={141}
        y={219}
        width={43}
        height={69}>
        <Path
          d="M176.343 263.717v-44.46H147.96v44.46c-4.016 1.921-6.807 6.014-6.807 10.726v1.408c0 6.555 5.359 11.912 11.915 11.912h18.193c6.557 0 11.915-5.357 11.915-11.912v-1.408c-.014-4.712-2.818-8.805-6.82-10.726z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#u)">
        <Path
          d="M133.571 223.206c-1.08 3.79-.843 6.251-.672 7.357.132.842.382 2.224 1.198 3.672 1.514 2.672 4.029 3.935 5.424 4.58 10.545 4.922 30.806 9.937 40.706 7.95 1.066-.224 2.29-.553 3.594-1.317 2.462-1.408 4.212-3.566 5.529-6.804a49.2 49.2 0 0 0 3.568-20.953c-.185-3.554-1.027-7.529-4.029-9.411-1.132-.71-2.462-1.039-3.752-1.342-9.018-2.132-18.365-3.672-27.646-2.961-6.411.487-12.283 1.276-16.272 6.633-2.843 3.79-6.332 7.963-7.648 12.596"
          fill="#BF8C55"
        />
      </G>
      <Path
        d="M222.117 263.717v-44.46h-28.383v44.46c-4.015 1.921-6.806 6.014-6.806 10.726v1.408c0 6.555 5.358 11.912 11.914 11.912h18.194c6.556 0 11.914-5.357 11.914-11.912v-1.408c-.013-4.712-2.818-8.805-6.82-10.726z"
        fill="#D9B382"
      />
      <Mask
        id="v"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={186}
        y={219}
        width={43}
        height={69}>
        <Path
          d="M222.117 263.717v-44.46h-28.383v44.46c-4.015 1.921-6.806 6.014-6.806 10.726v1.408c0 6.555 5.358 11.912 11.914 11.912h18.194c6.556 0 11.914-5.357 11.914-11.912v-1.408c-.013-4.712-2.818-8.805-6.82-10.726z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#v)">
        <Path
          d="M176.514 277.865c3.844 5.291 9.755 7.66 15.851 9.424a68.3 68.3 0 0 0 19.905 2.698c5.977-.105 11.493-1.751 16.838-4.343 5.16-2.488 9.952-5.607 15.231-7.897 2.094-.908 2.554-3.962 1.514-5.765-1.263-2.159-3.673-2.422-5.766-1.514-8.623 3.738-16.587 10.53-26.329 11.03-5.543.302-11.151-.316-16.522-1.645-4.318-1.067-10.651-2.409-13.442-6.265-1.329-1.83-3.659-2.751-5.766-1.514-1.83 1.093-2.87 3.935-1.527 5.791z"
          fill="#BF8C55"
        />
      </G>
      <Mask
        id="w"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={186}
        y={219}
        width={43}
        height={69}>
        <Path
          d="M222.117 263.717v-44.46h-28.383v44.46c-4.015 1.921-6.806 6.014-6.806 10.726v1.408c0 6.555 5.358 11.912 11.914 11.912h18.194c6.556 0 11.914-5.357 11.914-11.912v-1.408c-.013-4.712-2.818-8.805-6.82-10.726z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#w)">
        <Path
          d="M183.729 223.824a42.4 42.4 0 0 1 1.908 10.898c.119 3.211-.118 6.515.882 9.568.224.658.501 1.33 1.027 1.764.606.526 1.33.618 2.291.711 1.751.158 2.646.236 3.897.052 3.844-.579 25.553-2.711 33.386-5.765 1.685-.658 2.817-1.381 3.423-1.816 0 0 1.685-1.04 2.909-2.698 3.515-4.685 4.45-10.845 4.358-16.702-.04-2.79-.343-5.725-1.988-7.976-1.198-1.645-3.002-2.75-4.819-3.659-7.714-3.856-16.614-4.961-25.184-4.093-6.938.711-13.678 2.672-20.129 5.265-3.094 1.25-4.66 1.553-4.463 5.106.198 3.251 1.554 6.278 2.515 9.345z"
          fill="#BF8C55"
        />
      </G>
      <Path
        d="M271.959 206.109c-32.188 35.602-76.961 35.773-86.019 35.799-7.767.013-29.779-.197-52.922-11.029-9.571-4.502-19.326-10.78-28.344-19.624-30.516-29.85-32.952-69.48-31.833-89.985.342-6.212.988-10.661 1.303-12.582C81.872 60.03 122.315.908 184.216.013c14.599-.197 28.093 2.87 40.284 8.305 40.548 18.097 66.943 62.451 73.236 100.383.461 2.698 1.106 7.186 1.461 12.977 1.264 20.005-.974 55.37-27.251 84.431z"
        fill="#5CA0D3"
      />
      <Path
        d="M271.959 206.109c-32.188 35.602-76.961 35.773-86.019 35.799-7.767.013-29.779-.197-52.922-11.029-9.571-4.502-19.326-10.78-28.344-19.624-39.745-38.879-31.846-94.328-30.543-102.568C81.872 60.03 122.315.908 184.216.013c63.099-.895 105.318 59.345 113.52 108.688.461 2.698 1.106 7.186 1.461 12.977 1.264 20.005-.974 55.37-27.251 84.431z"
        fill="#5CA0D3"
      />
      <Mask
        id="x"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={72}
        y={0}
        width={228}
        height={242}>
        <Path
          d="M271.959 206.109c-32.188 35.602-76.961 35.773-86.019 35.799-7.767.013-29.779-.197-52.922-11.029-9.571-4.502-19.326-10.78-28.344-19.624-39.745-38.879-31.846-94.328-30.543-102.568C81.872 60.03 122.315.908 184.216.013c63.099-.895 105.318 59.345 113.52 108.688.461 2.698 1.106 7.186 1.461 12.977 1.264 20.005-.974 55.37-27.251 84.431z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#x)">
        <Path
          d="M74.079 183.8c7.411 7.134 18.404 16.57 33.149 25.428 14.125 8.502 39.85 23.98 75.711 25.152 36.414 1.21 64.02-12.964 76.606-20.572 18.944-11.924 37.901-23.848 56.832-35.773"
          fill="#5CA0D3"
          opacity={0.56}
        />
      </G>
      <Mask
        id="y"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={72}
        y={0}
        width={228}
        height={242}>
        <Path
          d="M271.959 206.109c-32.188 35.602-76.961 35.773-86.019 35.799-7.767.013-29.779-.197-52.922-11.029-9.571-4.502-19.326-10.78-28.344-19.624-39.745-38.879-31.846-94.328-30.543-102.568C81.872 60.03 122.315.908 184.216.013c63.099-.895 105.318 59.345 113.52 108.688.461 2.698 1.106 7.186 1.461 12.977 1.264 20.005-.974 55.37-27.251 84.431z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#y)">
        <Path
          d="M221.038 16.228c9.887 2.448 18.628 9.174 23.512 18.123z"
          fill="#5CA0D3"
          opacity={0.25}
        />
      </G>
      <Mask
        id="z"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={72}
        y={0}
        width={228}
        height={242}>
        <Path
          d="M271.959 206.109c-32.188 35.602-76.961 35.773-86.019 35.799-7.767.013-29.779-.197-52.922-11.029-9.571-4.502-19.326-10.78-28.344-19.624-39.745-38.879-31.846-94.328-30.543-102.568C81.872 60.03 122.315.908 184.216.013c63.099-.895 105.318 59.345 113.52 108.688.461 2.698 1.106 7.186 1.461 12.977 1.264 20.005-.974 55.37-27.251 84.431z"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#z)">
        <Path
          d="M115.126 39.471c-10.294 7.49-19.365 21.032-23.288 34.694z"
          fill="#5CA0D3"
          opacity={0.39}
        />
      </G>
      <Path
        d="M221.038 13.622c9.887 2.448 18.628 9.174 23.512 18.124z"
        fill="#5CA0D3"
        opacity={0.25}
      />
      <Path
        d="M115.126 39.471c-10.294 7.49-19.365 21.032-23.288 34.694z"
        fill="#5CA0D3"
        opacity={0.39}
      />
      <Path
        d="M121.222 39.182a40.2 40.2 0 0 0-11.743 14.543z"
        fill="#5CA0D3"
        opacity={0.38}
      />
      <Path
        d="m72.262 100.186-.026-.013-.014-.053v-.027l-.04-.053"
        fill="#5CA0D3"
      />
      <Path
        d="M159.137 85.997c0 11.688-6.793 21.151-15.153 21.151s-15.153-9.463-15.153-21.15c0-11.688 6.793-21.151 15.153-21.151s15.153 9.476 15.153 21.15"
        fill="#020204"
      />
      <Path
        d="M150.816 83.68a4.87 4.87 0 1 0 0-9.739 4.87 4.87 0 1 0 0 9.74"
        fill="#ECE3D2"
      />
      <Path
        d="M159.571 99.62c-6.108 17.372-23.012 18.57-24.092 28.086-1.105 9.963 15.904 21.716 30.964 26.152 32.504 9.581 69.8-10.859 69.247-23.217-.355-7.713-15.245-9.45-21.393-26.692-.579-1.619-2.29-6.791-2.356-13.661-.158-15.017 8.781-24.625 6.674-26.218-1.856-1.408-9.162 5.054-12.045 7.858a52 52 0 0 0-9.15 12.042c.105-3.87.224-7.752.316-11.608-3.844 3.659-7.675 7.33-11.532 10.977-3.213-3.686-6.438-7.397-9.65-11.082-.106 3.685-.224 7.37-.316 11.068-1.672-2.961-4.45-7.278-8.807-11.608-4.266-4.225-10.48-8.897-11.823-7.844-1.79 1.382 5.201 9.107 5.911 21.585.053.895.422 7.423-1.935 14.148z"
        fill="#D04848"
      />
      <Path
        d="M246.235 85.997c0 11.688-6.793 21.151-15.153 21.151s-15.152-9.463-15.152-21.15c0-11.688 6.793-21.151 15.152-21.151 8.36 0 15.153 9.476 15.153 21.15"
        fill="#020204"
      />
      <Path
        d="M237.783 83.68a4.87 4.87 0 1 0 0-9.739 4.87 4.87 0 1 0 0 9.74"
        fill="#ECE3D2"
      />
      <Path
        d="M262.612 215.216a105 105 0 0 1-13.059 9.687c-4.463 2.791-25.448 15.426-57.689 16.887-.987.052-3.146.131-5.95.118-28.765-.053-48.631-9.016-52.923-11.029-6.016-2.83-14.402-7.423-23.446-14.833 12.309-31.614 42.904-52.541 76.738-52.528 33.504.013 63.81 20.545 76.316 51.698zm-2.949-181.904c-8.438-6.068-41.311-28.087-83.28-21.335-45.406 7.305-70.406 43.314-80.477 57.805C82.28 89.42 75.96 108.49 72.828 121.283c.342-6.212.987-10.661 1.303-12.582C81.872 60.03 122.315.908 184.216.013c1.685-.013 9.333-.224 19.233 1.672 28.529 5.435 47.46 22.466 56.214 31.627"
        fill="#9FD1F7"
      />
      <Path
        d="M173.012 123.823c.198.829 4.753 18.571 12.915 18.663 6.53.066 11.203-11.2 13.099-15.807 2.343-5.646 4.279-10.345 2.278-14.464-4.081-8.371-22.499-10.2-28.015-2.659-2.831 3.909-1.514 9.266-.29 14.28z"
        fill="#C08467"
      />
      <Path
        d="M177.989 121.072s4.542 15.491 8.096 15.241 9.505-18.742 9.505-18.742-9.716.501-17.601 3.501"
        fill="#000"
      />
      <Path
        d="M180.859 129.641c.671-2.093 2.422-3.607 4.462-3.857 2.528-.302 5.069 1.409 5.859 4.12.158 3.119-2.225 5.659-4.884 5.791-2.936.158-5.74-2.632-5.437-6.068z"
        fill="#FF5757"
      />
      <Path
        d="M187.191 137.788a2.6 2.6 0 0 1-.987.236c-2.423.172-4.542-2.395-7.096-8.594-1.606-3.869-2.725-7.726-2.778-7.884-.25-.855.224-1.763 1.027-2.092 8.057-3.067 17.72-3.593 18.128-3.607.553-.039 1.092.224 1.448.672.342.434.434 1.026.263 1.566-.066.184-1.527 4.751-3.475 9.41-3.055 7.358-5.003 9.621-6.53 10.266zm-7.096-15.649c1.948 6.159 4.529 11.647 5.858 12.411 1.488-.961 4.542-7.463 7.175-15.057-3.014.316-8.241 1.04-13.033 2.646"
        fill="#000"
      />
      <Path
        d="M205.108 113.662a2.5 2.5 0 0 1-.237.579c-.645 1.159-2.422 2.369-5.542 4.462-6.332 4.238-9.518 6.37-13.31 6.344-3.054-.013-5.503-1.145-8.425-3.014-1.04-.671-2.159-1.435-3.357-2.251-4.16-2.869-6.227-4.29-6.635-6.396-1.08-5.62 8.688-14.938 18.97-14.807 10.361.132 19.813 9.845 18.523 15.083z"
        fill="#D9B382"
      />
      <Path
        opacity={0.6}
        d="M200.672 104.634c-.738-.553-7.636-5.502-15.772-2.975-7.003 2.172-12.032 8.99-12.309 16.979a14.6 14.6 0 0 1-3.304-2.541c-1.225-1.276-1.501-2.053-1.58-2.329-.303-.987-.171-1.856-.079-2.422.184-1.145.645-2.106 1.343-3.211 1.461-2.277 3.172-3.778 3.804-4.33a22.1 22.1 0 0 1 6.556-3.857c1.264-.473 3.384-1.25 6.254-1.368 4.278-.172 7.477 1.25 8.991 1.934a21.8 21.8 0 0 1 6.096 4.12"
        fill="#fff"
      />
      <Path
        d="M204.871 114.242c-.645 1.158-2.422 2.369-5.542 4.461-6.332 4.238-9.518 6.371-13.31 6.344-3.054-.013-5.503-1.145-8.425-3.014 1.329.369 2.699.658 4.173.711 2.528.079 5.042-.25 7.464-.921 4.832-1.343 9.242-3.922 13.481-6.647.659-.421 1.409-.855 2.159-.921z"
        fill="#CCA172"
      />
      <Path
        d="M270.893 45.157c1.724-3.29 1.343-8.634-2.041-11.135-2.435-1.803-8.188-4.277-10.663-5.29-44.339-18.203-94.221-17.268-140.021-3.133-5.49 1.698-14.64 5.212-14.166 12.517.461 6.936 6.912 5.975 12.494 6.33 17.404 1.08 34.847 1.738 52.277 2.12 8.729.197 100.356 1.96 102.12-1.409"
        fill="#fff"
      />
      <Mask
        id="A"
        style={{
          maskType: 'luminance',
        }}
        maskUnits="userSpaceOnUse"
        x={103}
        y={15}
        width={169}
        height={33}>
        <Path
          d="M270.893 45.157c1.724-3.29 1.343-8.634-2.041-11.135-2.435-1.803-8.188-4.277-10.663-5.29-44.339-18.203-94.221-17.268-140.021-3.133-5.49 1.698-14.64 5.212-14.166 12.517.461 6.936 6.912 5.975 12.494 6.33 17.404 1.08 34.847 1.738 52.277 2.12 8.729.197 100.356 1.96 102.12-1.409"
          fill="#fff"
        />
      </Mask>
      <G mask="url(#A)">
        <Path
          d="M101.488 42.84s160.637-2.368 176.251 4.357L146.59 56.818 101.488 46.46z"
          fill="#C9C8D1"
        />
      </G>
      <Path
        d="M270.893 45.157c1.724-3.29 1.343-8.634-2.041-11.135-2.435-1.803-8.188-4.277-10.663-5.29-44.339-18.203-94.221-17.268-140.021-3.133-5.49 1.698-14.64 5.212-14.166 12.517.461 6.936 6.912 5.975 12.494 6.33 17.404 1.08 34.847 1.738 52.277 2.12 8.729.197 100.356 1.96 102.12-1.409Z"
        stroke="#000"
        strokeWidth={2}
        strokeMiterlimit={10}
      />
      <Path
        d="M186.928 41.682c5.889 0 10.663-4.773 10.663-10.66s-4.774-10.661-10.663-10.661-10.664 4.773-10.664 10.66 4.774 10.661 10.664 10.661"
        fill="#FA000E"
      />
      <Path
        d="m178.805 187.064-47.96 39.761-5.305.013h-.013l-.053-.053-.197-.105c-1.212-.737-2.436-1.474-3.66-2.277-.579-.368-1.159-.776-1.738-1.171a61 61 0 0 1-1.83-1.277c-.553-.395-1.119-.79-1.645-1.184l-2.357-1.817c-.355-.289-.724-.566-1.145-.934-.435-.329-.856-.698-1.304-1.066l-.131-.106a6 6 0 0 1-.422-.368 96 96 0 0 1-2.804-2.488c-.223-.197-.434-.408-.658-.618-.237-.224-.474-.448-.645-.606l-.342-.342a4 4 0 0 1-.237-.224l-.185-.197a98.3 98.3 0 0 1-16.1-20.558c-.514-.895-1.027-1.79-1.514-2.672l-.066-.132c-.224-.408-.448-.816-.671-1.237a119 119 0 0 1-1.962-3.896 1.2 1.2 0 0 0-.105-.223 83 83 0 0 1-1.198-2.672 72 72 0 0 1-.988-2.303v-.461l-.302-.29a115 115 0 0 1-1.277-3.343v-.447l-.264-.263c-.237-.671-.46-1.343-.684-2.001l-.027-.592-.25-.224-.066-.21c-.197-.632-.395-1.238-.579-1.869l-.079-.25c-.17-.553-.329-1.132-.5-1.685l-.158-.579c-.026-.105-.066-.224-.092-.329l-.5-1.882c-.145-.54-.29-1.093-.409-1.645l-.487-1.988c-.237-1.105-.474-2.198-.684-3.277 2.962-.987 9.781-3.185 18.457-5.554 13.046-3.554 24.697-5.897 33.755-6.792l30.753 26.455 18.641 15.978zm-48.934-46.447c-.447.04-.895.079-1.356.145l1.369-.132s.119-.013.171-.013z"
        fill="#37458D"
      />
      <Path d="M125.553 226.838h-.039l.013-.013z" fill="#37458D" />
      <Path
        d="M175.882 230.773a11 11 0 0 0-.553 2.777l-3.712 2.435a8.2 8.2 0 0 0-3.462 4.923c-6.714-.908-13.297-2.304-19.59-4.173-3.199-.934-6.332-2-9.636-3.316a19 19 0 0 1-.896-.342c-.763-.316-1.435-.593-2.08-.895-.46-.198-.842-.356-1.079-.461-.224-.119-.448-.211-.658-.316l-.369-.158a5 5 0 0 0-.434-.224l-.119-.052a3 3 0 0 0-.316-.145l-.079-.04c-.013 0-.013-.013-.026-.013z"
        fill="#FDFDFF"
      />
      <Path
        d="M125.514 226.825h.013l5.305.013-5.332-.013zm11.519 0h-.013l8.175.013h.013zm42.075 1.974s0 .013-.013.013h.079z"
        fill="#000"
      />
      <Path
        d="M238.363 230.786a108 108 0 0 1-9.413 3.923c-.79.302-1.593.579-2.449.868-.211.079-.434.145-.698.237-.908.316-1.856.619-2.764.895h-.04l-1.408.395a7 7 0 0 1-1.014.29l-.132.039c-.513.145-1.027.29-1.54.421l-.895.237h-.013c-.369.105-.738.198-1.119.29l-.948.237c-1.093.25-2.186.5-3.344.737l-.54.118c-.289.053-.579.119-.882.184-.132.027-.29.053-.5.092l-.329.066c-.645.119-1.29.237-1.949.356a8.25 8.25 0 0 0-3.225-4.186l-3.726-2.435c-.066-.96-.25-1.882-.539-2.777h37.467zm-93.853-3.487-.013.013h-8.307l.013-.013zm-12.532 4.001s.013.013.026.013h13.994zm111.453-83.181-92.759 79.18h-17.615l47.13-38.932 1.567-1.29 1.566-1.29 5.029-4.132 1.58-1.303 15.39-12.741 24.342-20.097c2.514-.171 5.279-.132 8.214.079.922.052 1.87.131 2.818.224h.013c.895.092 1.79.184 2.712.302zm-56.622 32.312-1.54 1.29-3.37 2.79-1.541 1.276-18.918-16.201-31.371-26.995a71 71 0 0 1 2.791-.21c2.843-.184 5.516-.211 7.977-.053l29.964 24.849h.013z"
        fill="#F9FEFD"
      />
      <Path
        d="M179.108 228.799v.013h.079l-.066-.013zm19.721 5.528v1.961c0 .092 0 .184-.014.276 0 .158 0 .329-.026.487-.013.158-.026.329-.053.487a6 6 0 0 1-.092.474c-.026.171-.066.316-.105.474a7 7 0 0 1-.29.921c-.052.145-.118.29-.184.435a6 6 0 0 1-.527.987 7.9 7.9 0 0 1-1.421 1.75 7 7 0 0 1-.751.645 7.9 7.9 0 0 1-2.514 1.343 8.6 8.6 0 0 1-2.765.46h-3.357a8.7 8.7 0 0 1-2.764-.46 8.4 8.4 0 0 1-2.989-1.738c-.026 0-.039-.013-.039-.026a9 9 0 0 1-.711-.711l-.04-.039c-.21-.237-.408-.5-.592-.764a8 8 0 0 1-.514-.802 8.7 8.7 0 0 1-1.013-3.146 5 5 0 0 1-.053-.487c0-.105-.013-.211-.013-.329v-2.198c0-1.237.25-2.435.763-3.54a9 9 0 0 1 .935-1.593q.139-.198.316-.395a8.1 8.1 0 0 1 2.251-1.974 8.63 8.63 0 0 1 4.463-1.237h3.357c1.593 0 3.147.447 4.476 1.25a8.3 8.3 0 0 1 2.265 1.974c.118.119.223.25.316.382.368.5.671 1.04.921 1.593a8.6 8.6 0 0 1 .764 3.54"
        fill="#F3F4ED"
      />
      <Path
        d="M206.175 242.487a5.3 5.3 0 0 1-.264 2.093h-.013a4.7 4.7 0 0 1-.605 1.263 5.54 5.54 0 0 1-4.608 2.488 5.5 5.5 0 0 1-3.028-.908l-3.186-2.093a8 8 0 0 0 1.04-.592 5.4 5.4 0 0 0 .658-.461q.415-.316.79-.671c.421-.382.79-.79 1.119-1.237.079-.106.158-.211.224-.303q.237-.316.434-.671a1.5 1.5 0 0 0 .211-.369 4.3 4.3 0 0 0 .329-.658.5.5 0 0 0 .066-.145c.119-.276.224-.553.316-.842.053-.158.105-.316.145-.487.052-.171.079-.329.118-.487.066-.342.119-.671.158-1.013.04-.369.066-.738.066-1.106v-.434l3.568 2.342a5.55 5.55 0 0 1 2.462 4.291m-23.816 2.843-3.185 2.093a5.524 5.524 0 0 1-7.649-1.58c-.132-.184-.237-.381-.343-.579a5.35 5.35 0 0 1-.552-2.172c-.093-1.947.855-3.83 2.488-4.882l3.567-2.356v.434c0 .171 0 .355.014.526.013.172.026.343.052.527.013.132.027.263.053.395.039.329.092.645.184.961.04.197.092.394.158.579.066.237.145.447.237.671.066.171.132.329.211.5.158.355.342.698.553 1.04.131.237.289.474.46.697.04.066.079.119.119.171.145.198.303.382.474.566.079.092.158.185.25.264.197.223.395.421.606.605.039.039.092.079.144.118.264.224.527.435.817.632q.651.454 1.342.79"
        fill="#FFFDFF"
      />
      <Path
        d="M179.108 228.799s0 .013-.014.013h.08zm-3.226 1.974v.014h-34.729l.014-.014zm71.341-83.536-.027.027c-.79-.119-1.566-.237-2.343-.342z"
        fill="#37458D"
      />
      <Path
        d="M179.174 228.799h-.013l-.067.013c.014 0 .014-.013.014-.013zm114.586-65.36c-4.252 16.544-12.032 31.153-23.117 43.459-.106.093-.171.185-.198.211l-.46.513c-.303.316-.593.645-.869.935l-1.922 1.974c-.369.382-.751.75-1.133 1.119-1.132 1.092-2.093 2-3.12 2.895-.5.448-1 .895-1.579 1.382-.224.211-.461.395-.685.579l-.487.421c-.198.172-.395.343-.606.514l-.355.289c-.303.25-.619.487-.869.698a96 96 0 0 1-4.95 3.659c-.29.21-.592.421-.974.658a69 69 0 0 1-3.239 2.119h-51.711a12 12 0 0 0-7.399-2.567h-3.357a11.82 11.82 0 0 0-7.372 2.554h-22.736l86.572-74.165c19.866 2.566 44.076 10.542 50.566 12.753"
        fill="#37458D"
      />
      <Path
        d="m189.916 180.444 15.153-12.583 23.96-19.847c2.475-.171 5.2-.132 8.083.079.908.053 1.843.132 2.778.224h.013c.882.092 1.764.184 2.672.302l-91.298 78.206h-6.082l-8.175.013h.013l-3.094-.013 46.393-38.458 1.54-1.277 1.541-1.276 4.95-4.08zm-57.056-38.063c2.843-.184 5.516-.211 7.978-.053l29.963 24.849h.013l15.995 13.254-1.54 1.29-3.37 2.79-1.541 1.276-18.918-16.201-31.371-26.995a73 73 0 0 1 2.791-.21m-2.015 84.444-5.305.013h-.013l-.053-.053-.197-.105c-1.211-.737-2.436-1.474-3.66-2.277-.579-.368-1.159-.776-1.738-1.171a61 61 0 0 1-1.83-1.277c-.553-.395-1.119-.79-1.645-1.184l-2.357-1.817c-.355-.289-.724-.566-1.145-.934-.435-.329-.856-.698-1.304-1.066l-.131-.105a7 7 0 0 1-.422-.369 96 96 0 0 1-2.804-2.488c-.223-.197-.434-.408-.658-.618-.237-.224-.474-.448-.645-.606l-.342-.342c-.079-.066-.158-.145-.237-.223l-.185-.198a98.3 98.3 0 0 1-16.1-20.558c-.514-.895-1.027-1.79-1.514-2.672l-.066-.132c-.224-.408-.448-.816-.671-1.237a119 119 0 0 1-1.962-3.896 1.2 1.2 0 0 0-.105-.223 83 83 0 0 1-1.198-2.672 72 72 0 0 1-.988-2.303v-.461l-.302-.289a116 116 0 0 1-1.277-3.344v-.447l-.264-.263c-.237-.671-.46-1.343-.684-2.001l-.027-.592-.25-.224-.066-.21c-.197-.632-.394-1.238-.579-1.869l-.079-.25c-.17-.553-.329-1.132-.5-1.685l-.158-.579c-.026-.105-.066-.224-.092-.329l-.5-1.882c-.145-.54-.29-1.093-.409-1.645l-.487-1.988c-.237-1.105-.474-2.198-.684-3.277 2.962-.987 9.781-3.185 18.457-5.554 13.046-3.554 24.697-5.897 33.755-6.792l30.753 26.455 18.641 15.978-47.959 39.761zm37.31 14.083c-6.715-.908-13.297-2.304-19.59-4.173-3.199-.934-6.332-2-9.636-3.316a18 18 0 0 1-.896-.342c-.763-.316-1.435-.593-2.08-.895-.46-.198-.842-.356-1.079-.461-.224-.118-.448-.211-.658-.316l-.369-.158a5 5 0 0 0-.434-.224l-.119-.052a3 3 0 0 0-.316-.145l-.079-.039c-.013 0-.013-.014-.026-.014h43.009a11 11 0 0 0-.553 2.777l-3.712 2.435a8.2 8.2 0 0 0-3.462 4.923m11.019 6.515a5.524 5.524 0 0 1-7.649-1.58c-.132-.184-.237-.381-.343-.579a5.35 5.35 0 0 1-.552-2.172c-.093-1.947.855-3.829 2.488-4.882l3.567-2.356v.434c0 .171 0 .355.014.526.013.172.026.343.052.527 0 .132.027.263.053.395.039.329.092.645.184.961.04.197.092.394.158.579.066.237.145.447.237.671.066.171.132.329.211.5.158.355.342.698.553 1.04.131.237.289.474.46.697.04.066.079.119.119.171.145.198.303.382.474.566.079.093.158.185.25.264.197.223.395.421.606.605.039.04.092.079.144.119.264.223.527.434.817.631q.651.454 1.342.79zm13.678-2.856a8.6 8.6 0 0 1-2.765.46h-3.357a8.7 8.7 0 0 1-2.764-.46 8.4 8.4 0 0 1-2.989-1.738c-.026 0-.039-.013-.039-.026a9 9 0 0 1-.711-.711l-.04-.039c-.21-.237-.408-.5-.592-.764a8 8 0 0 1-.514-.802 8.7 8.7 0 0 1-1.013-3.146 5 5 0 0 1-.053-.487c0-.105-.013-.211-.013-.329v-2.198c0-1.237.25-2.435.763-3.54a9 9 0 0 1 .935-1.593q.139-.198.316-.395a8.1 8.1 0 0 1 2.251-1.974 8.63 8.63 0 0 1 4.463-1.237h3.357c1.593 0 3.147.447 4.476 1.25a8.3 8.3 0 0 1 2.265 1.974c.118.119.223.25.316.382.368.5.671 1.04.921 1.593a8.6 8.6 0 0 1 .764 3.54v1.961c0 .092 0 .184-.014.276 0 .158 0 .329-.026.487-.013.158-.026.329-.053.487a6 6 0 0 1-.092.474c0 .158-.052.316-.105.474a7 7 0 0 1-.29.921c-.052.145-.118.29-.184.435a6 6 0 0 1-.527.987 7.9 7.9 0 0 1-1.421 1.75 7 7 0 0 1-.751.645 7.8 7.8 0 0 1-2.514 1.343m-13.757-15.755.013-.013h.052zm26.816 15.768h-.013a4.7 4.7 0 0 1-.605 1.263 5.54 5.54 0 0 1-4.608 2.488 5.5 5.5 0 0 1-3.028-.908l-3.186-2.093a8 8 0 0 0 1.04-.592 5.4 5.4 0 0 0 .658-.461q.415-.316.79-.671a8.3 8.3 0 0 0 1.119-1.237c.079-.106.158-.211.224-.303q.237-.316.434-.671a1.5 1.5 0 0 0 .211-.369c.119-.21.237-.434.329-.658a.5.5 0 0 0 .066-.145c.119-.276.224-.552.316-.842.053-.158.105-.316.145-.487.053-.171.079-.329.118-.487.066-.342.119-.671.158-1.013.04-.369.066-.737.066-1.106v-.434l3.568 2.342a5.55 5.55 0 0 1 2.462 4.291 5.3 5.3 0 0 1-.264 2.093m23.039-9.871c-.79.302-1.593.579-2.449.868-.21.079-.434.145-.698.237-.908.316-1.856.619-2.764.895-.013 0-.027.013-.04.013l-1.408.395a7 7 0 0 1-1.014.29l-.132.039c-.513.145-1.027.29-1.54.421l-.895.237h-.013c-.369.106-.738.198-1.119.29l-.948.237c-1.093.25-2.186.5-3.344.737l-.54.118c-.289.053-.579.119-.882.185-.132.026-.29.052-.5.092l-.329.065c-.645.119-1.29.237-1.949.356a8.25 8.25 0 0 0-3.225-4.186l-3.726-2.434a11.6 11.6 0 0 0-.539-2.777h37.467a109 109 0 0 1-9.413 3.922m41.693-27.811c-.105.093-.171.185-.198.211l-.46.513c-.303.316-.593.645-.869.935l-1.922 1.974c-.369.382-.751.75-1.133 1.119-1.132 1.092-2.093 2-3.12 2.895-.5.448-1 .895-1.579 1.382-.224.211-.461.395-.685.579l-.487.421c-.198.172-.395.343-.606.514l-.355.289c-.303.25-.619.487-.869.698a96 96 0 0 1-4.95 3.659c-.29.21-.592.421-.974.658a69 69 0 0 1-3.239 2.119h-51.711a12 12 0 0 0-7.399-2.567h-3.357a11.82 11.82 0 0 0-7.372 2.554h-22.736l86.572-74.165c19.866 2.566 44.076 10.542 50.566 12.753-4.252 16.544-12.032 31.153-23.117 43.459m25.684-46.749c-.184-.079-.394-.145-.592-.211-4.752-1.645-28.12-9.502-48.539-12.674-.79-.119-1.566-.237-2.343-.342a2 2 0 0 0-.263-.027 42 42 0 0 0-1.778-.237l-.855-.105c-.566-.066-1.119-.118-1.672-.184l-1.04-.105c-3.989-.356-7.399-.422-10.348-.224l-.632.039-39.902 33.075-46.762-38.747-.645-.04c-3.133-.21-6.793-.118-10.9.25-.053 0-.119.013-.172.013l-1.369.132-.461.04c-.882.092-1.803.197-2.725.315-8.675 1.119-18.97 3.304-30.713 6.502-10.743 2.922-19.234 5.778-20.353 6.186l-1.567.566.303 1.645c.303 1.672.645 3.356 1.04 5.133l.145.632c.105.474.21.908.329 1.356l.382 1.513c.013.079.04.171.066.25l.447 1.711c.027.105.053.211.092.316l.237.829c.172.592.343 1.185.514 1.751l.079.237c.184.644.395 1.276.592 1.921l.382 1.224.026.027c.264.763.527 1.539.803 2.303l.145.395h.013c.461 1.289.935 2.527 1.449 3.803l.157.408h.014c.329.803.671 1.593 1.013 2.369.395.922.816 1.843 1.238 2.751l.079.184a133 133 0 0 0 2.08 4.12c.224.434.447.855.684 1.277l.08.144c.5.922 1.026 1.843 1.566 2.777a102 102 0 0 0 16.733 21.375l.131.131c.132.132.263.263.329.316l.356.342c.21.211.434.408.645.619.237.224.474.461.724.671a77 77 0 0 0 2.844 2.54c.223.198.447.395.671.553.461.408.921.79 1.303 1.093.395.329.79.644 1.185.96l2.449 1.896c.579.421 1.145.829 1.803 1.289.606.422 1.198.843 1.804 1.251.158.118.329.223.487.342l5.503 3.475.369.236.315.185c.145.092.277.171.422.237l.697.381c.356.211.711.408 1.08.606l.237.131.25.132c.171.079.342.171.514.263l1.395.764h.066c.25.118.5.25.763.394.237.119.501.237.619.29.316.158.619.316.909.461l.579.263c.092.039.171.092.263.145l.448.237h.079c.144.065.289.118.539.25.356.158.764.329 1.172.513a39 39 0 0 0 2.199.934c.355.158.724.303 1.013.395a110 110 0 0 0 9.9 3.409 135 135 0 0 0 22.236 4.554 6.845 6.845 0 0 0 10.202 3.448l3.989-2.619c.922.29 1.883.434 2.857.434h3.357c.974 0 1.935-.144 2.857-.434l3.989 2.619a6.9 6.9 0 0 0 3.752 1.119 6.78 6.78 0 0 0 5.7-3.08 6.3 6.3 0 0 0 .948-2.211c1.264-.197 2.515-.421 3.713-.645l.302-.053c.106-.026.198-.039.33-.079l.289-.039c.29-.079.593-.132.882-.197l.553-.106c1.172-.25 2.317-.5 3.436-.763.342-.079.672-.171 1.014-.25.421-.105.856-.224 1.316-.329.119-.027.224-.053.343-.105l.408-.106c.526-.145 1.066-.276 1.58-.434a13 13 0 0 0 1.224-.342c.21-.053.408-.119.566-.171l.184-.053c.106-.026.211-.053.329-.105l.211-.04a92 92 0 0 0 3.015-.974c.289-.092.566-.184.803-.263.829-.29 1.659-.579 2.488-.882 4.779-1.75 9.347-3.764 13.533-5.962.566-.276 1.133-.592 1.725-.908.461-.25.908-.5 1.369-.763h.013l.764-.435c.263-.145.513-.289.711-.408 0 0 .013 0 .013-.013.263-.145.527-.303.777-.447.263-.158.513-.316.776-.474.106-.066.211-.119.316-.198l.487-.289s.027-.013.04-.027l2.554-1.658 1.198-.776c.197-.119.382-.251.566-.369.184-.132.369-.25.54-.382.21-.144.421-.289.632-.447a101 101 0 0 0 4.515-3.369c.277-.237.553-.448.843-.672l.355-.289c.25-.198.5-.421.751-.645l.395-.329c.276-.237.566-.461.763-.658a41 41 0 0 0 1.633-1.422c1.04-.908 2.04-1.856 3.199-2.974.408-.395.803-.79 1.211-1.185l1.961-2.027c.316-.329.632-.671.935-1.013l.395-.434.079-.079.132-.145c11.966-13.241 20.208-29.048 24.526-46.987l.408-1.75z"
        fill="#000"
      />
      <Path d="M156.622 224.851h-20.84l-3.475 2.737 23.064.461z" fill="#000" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h374v290H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default BirdSVG;
