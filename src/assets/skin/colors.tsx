export interface IColorTheme {
  // primary
  Primary_Color_Bolder: string;
  Primary_Color_Main: string;
  Primary_Color_Lighter: string;
  Primary_Color_Border: string;
  Primary_Color_Background: string;
  Primary_Color_Tag: string;

  // secondary 1-6
  Secondary_1_Color_Bolder: string;
  Secondary_1_Color_Main: string;
  Secondary_1_Color_Lighter: string;
  Secondary_1_Color_Border: string;
  Secondary_1_Color_Background: string;
  Secondary_1_Color_Tag: string;

  Secondary_2_Color_Bolder: string;
  Secondary_2_Color_Main: string;
  Secondary_2_Color_Lighter: string;
  Secondary_2_Color_Border: string;
  Secondary_2_Color_Background: string;
  Secondary_2_Color_Tag: string;

  Secondary_3_Color_Bolder: string;
  Secondary_3_Color_Main: string;
  Secondary_3_Color_Lighter: string;
  Secondary_3_Color_Border: string;
  Secondary_3_Color_Background: string;
  Secondary_3_Color_Tag: string;

  Secondary_4_Color_Bolder: string;
  Secondary_4_Color_Main: string;
  Secondary_4_Color_Lighter: string;
  Secondary_4_Color_Border: string;
  Secondary_4_Color_Background: string;
  Secondary_4_Color_Tag: string;

  Secondary_5_Color_Bolder: string;
  Secondary_5_Color_Main: string;
  Secondary_5_Color_Lighter: string;
  Secondary_5_Color_Border: string;
  Secondary_5_Color_Background: string;
  Secondary_5_Color_Tag: string;

  Secondary_6_Color_Bolder: string;
  Secondary_6_Color_Main: string;
  Secondary_6_Color_Lighter: string;
  Secondary_6_Color_Border: string;
  Secondary_6_Color_Background: string;
  Secondary_6_Color_Tag: string;

  // status colors
  Success_Color_Bolder: string;
  Success_Color_Main: string;
  Success_Color_Lighter: string;
  Success_Color_Border: string;
  Success_Color_Background: string;
  Success_Color_Tag: string;

  Info_Color_Bolder: string;
  Info_Color_Main: string;
  Info_Color_Lighter: string;
  Info_Color_Border: string;
  Info_Color_Background: string;
  Info_Color_Tag: string;

  Warning_Color_Bolder: string;
  Warning_Color_Main: string;
  Warning_Color_Lighter: string;
  Warning_Color_Border: string;
  Warning_Color_Background: string;
  Warning_Color_Tag: string;

  Error_Color_Bolder: string;
  Error_Color_Main: string;
  Error_Color_Lighter: string;
  Error_Color_Border: string;
  Error_Color_Background: string;
  Error_Color_Tag: string;

  // neutral backgrounds
  Neutral_Background_Color_Bolder: string;
  Neutral_Background_Color_Absolute: string;
  Neutral_Background_Color_Main: string;
  Neutral_Background_Color_Lighter: string;
  Neutral_Background_Color_Overlay: string;
  Neutral_Background_Color_Disable: string;
  Neutral_Background_Color_Selected: string;
  Neutral_Background_Color_Hover: string;
  Neutral_Background_Color_Stable: string;
  Neutral_Background_Color_Shadow: string;

  // reverse backgrounds
  Neutral_Background_Color_AbsoluteReverse: string;
  Neutral_Background_Color_BolderReverse: string;
  Neutral_Background_Color_MainReverse: string;
  Neutral_Background_Color_LighterReverse: string;
  Neutral_Background_Color_DisableReverse: string;
  Neutral_Background_Color_HoverReverse: string;
  Neutral_Background_Color_StableReverse: string;

  // neutral text
  Neutral_Text_Color_Title: string;
  Neutral_Text_Color_Subtitle: string;
  Neutral_Text_Color_Body: string;
  Neutral_Text_Color_Label: string;
  Neutral_Text_Color_Placeholder: string;
  Neutral_Text_Color_Disable: string;
  Neutral_Text_Color_Stable: string;
  Neutral_Text_Color_Hover: string;
  Neutral_Text_Color_Selected: string;

  // reverse text
  Neutral_Text_Color_TitleReverse: string;
  Neutral_Text_Color_SubtitleReverse: string;
  Neutral_Text_Color_BodyReverse: string;
  Neutral_Text_Color_LabelReverse: string;
  Neutral_Text_Color_PlaceholderReverse: string;
  Neutral_Text_Color_DisableReverse: string;
  Neutral_Text_Color_StableReverse: string;

  // neutral borders
  Neutral_Border_Color_Bolder: string;
  Neutral_Border_Color_Main: string;
  Neutral_Border_Color_Lighter: string;
  Neutral_Border_Color_Hover: string;

  // reverse borders
  Neutral_Border_Color_BolderReverse: string;
  Neutral_Border_Color_MainReverse: string;
  Neutral_Border_Color_LighterReverse: string;

  // shadows
  shadow_top: string;
  shadow_bottom: string;
  shadow_right: string;
  shadow_left: string;
  shadow_dropdown: string;

  // misc
  transparent: string;
  white: string;

  // Cho phép thêm các thuộc tính động từ API
  [key: string]: any;
}

export const ColorThemes: {
  light: IColorTheme;
  dark: IColorTheme;
} = {
  light: {
    /* primary */
    Primary_Color_Bolder: '#c8291e',
    Primary_Color_Main: '#e14337',
    Primary_Color_Lighter: '#E86D64',
    Primary_Color_Border: '#c8291e',
    Primary_Color_Background: '#e8f7ef',
    Primary_Color_Tag: '#c8291e',
    /* secondary 1 */
    Secondary_1_Color_Bolder: '#E36003',
    Secondary_1_Color_Main: '#FC7A1C',
    Secondary_1_Color_Tag: '#FC7A1C',
    Secondary_1_Color_Lighter: '#FD974F',
    Secondary_1_Color_Border: '#FED3B3',
    Secondary_1_Color_Background: '#FFF3EB',
    /* secondary 2 */
    Secondary_2_Color_Bolder: '#2D8655',
    Secondary_2_Color_Main: '#3AAC6D',
    Secondary_2_Color_Tag: '#3AAC6D',
    Secondary_2_Color_Lighter: '#53C586',
    Secondary_2_Color_Border: '#C6ECD7',
    Secondary_2_Color_Background: '#E8F7EF',
    /* secondary 3 */
    Secondary_3_Color_Bolder: '#22C3AE',
    Secondary_3_Color_Main: '#3CDDC7',
    Secondary_3_Color_Tag: '#3CDDC7',
    Secondary_3_Color_Lighter: '#67E4D4',
    Secondary_3_Color_Border: '#BEF4EC',
    Secondary_3_Color_Background: '#EEFCFA',
    /* secondary 4 */
    Secondary_4_Color_Bolder: '#7B22C3',
    Secondary_4_Color_Main: '#943CDD',
    Secondary_4_Color_Tag: '#943CDD',
    Secondary_4_Color_Lighter: '#AC67E4',
    Secondary_4_Color_Border: '#DBBEF4',
    Secondary_4_Color_Background: '#F6EEFC',
    /* secondary 5 */
    Secondary_5_Color_Bolder: '#E19405',
    Secondary_5_Color_Main: '#FAAD1E',
    Secondary_5_Color_Tag: '#FAAD1E',
    Secondary_5_Color_Lighter: '#FBBF50',
    Secondary_5_Color_Border: '#FDE4B4',
    Secondary_5_Color_Background: '#FFF8EB',
    /* secondary 6 */
    Secondary_6_Color_Bolder: '#C8291E',
    Secondary_6_Color_Main: '#E86D64',
    Secondary_6_Color_Tag: '#E86D64',
    Secondary_6_Color_Lighter: '#E86D64',
    Secondary_6_Color_Border: '#F5C0BC',
    Secondary_6_Color_Background: '#FCEEED',
    /* success */
    Success_Color_Bolder: '#2D8655',
    Success_Color_Main: '#3AAC6D',
    Success_Color_Tag: '#3AAC6D',
    Success_Color_Lighter: '#53C586',
    Success_Color_Border: '#C6ECD7',
    Success_Color_Background: '#E8F7EF',
    /* info */
    Info_Color_Bolder: '#0F62D7',
    Info_Color_Main: '#287CF0',
    Info_Color_Tag: '#287CF0',
    Info_Color_Lighter: '#5899F3',
    Info_Color_Border: '#B7D3FA',
    Info_Color_Background: '#ECF3FE',
    /* warning */
    Warning_Color_Bolder: '#E36003',
    Warning_Color_Main: '#FC7A1C',
    Warning_Color_Tag: '#FC7A1C',
    Warning_Color_Lighter: '#FD974F',
    Warning_Color_Border: '#FED3B3',
    Warning_Color_Background: '#FFF3EB',
    /* error */
    Error_Color_Bolder: '#C8291E',
    Error_Color_Main: '#E14337',
    Error_Color_Tag: '#E14337',
    Error_Color_Lighter: '#E86D64',
    Error_Color_Border: '#F5C0BC',
    Error_Color_Background: '#FCEEED',
    /* neutral_background */
    Neutral_Background_Color_Bolder: '#EAEAEC',
    Neutral_Background_Color_Absolute: '#FFFFFF',
    Neutral_Background_Color_Main: '#EFEFF0',
    Neutral_Background_Color_Lighter: '#F4F4F5',
    Neutral_Background_Color_Overlay: '#000000B3',
    Neutral_Background_Color_Disable: '#F4F4F5',
    Neutral_Background_Color_Selected: '#18181B14',
    Neutral_Background_Color_Hover: '#18181B0A',
    Neutral_Background_Color_Stable: '#FFFFFF',
    Neutral_Background_Color_Shadow: '#000000',
    /* reverse */
    Neutral_Background_Color_AbsoluteReverse: '#000000',
    Neutral_Background_Color_BolderReverse: '#18181B',
    Neutral_Background_Color_MainReverse: '#242428',
    Neutral_Background_Color_LighterReverse: '#313135',
    Neutral_Background_Color_DisableReverse: '#313135',
    Neutral_Background_Color_HoverReverse: '#000000',
    Neutral_Background_Color_StableReverse: '#000000',
    /* neutral_text */
    Neutral_Text_Color_Title: '#18181B',
    Neutral_Text_Color_Subtitle: '#61616B',
    Neutral_Text_Color_Body: '#313135',
    Neutral_Text_Color_Label: '#313135',
    Neutral_Text_Color_Placeholder: '#878792',
    Neutral_Text_Color_Disable: '#A2A2AA',
    Neutral_Text_Color_Stable: '#FFFFFF',
    Neutral_Text_Color_Hover: '#FFFFFF',
    Neutral_Text_Color_Selected: '#18181B',
    /* reverse */
    Neutral_Text_Color_TitleReverse: '#F4F4F5',
    Neutral_Text_Color_SubtitleReverse: '#A2A2AA',
    Neutral_Text_Color_BodyReverse: '#D7D7DB',
    Neutral_Text_Color_LabelReverse: '#EAEAEC',
    Neutral_Text_Color_PlaceholderReverse: '#A2A2AA',
    Neutral_Text_Color_DisableReverse: '#878792',
    Neutral_Text_Color_StableReverse: '#000000',
    /* neutral_border_color */
    Neutral_Border_Color_Bolder: '#D7D7DB',
    Neutral_Border_Color_Main: '#EAEAEC',
    Neutral_Border_Color_Lighter: '#F4F4F5',
    Neutral_Border_Color_Hover: '#000000',
    /* neutral_border */
    /* neutral_border_color reverse */

    /* neutral_border reverse */
    Neutral_Border_Color_BolderReverse: '1px solid #242428',
    Neutral_Border_Color_MainReverse: '1px solid #313135',
    Neutral_Border_Color_LighterReverse: '1px solid #494950',

    shadow_top: '0px _1px 6px 0px #2D32390F',
    shadow_bottom: '0px 1px 6px 0px #2D32390F',
    shadow_right: '1px 0px 6px 0px #2D32390F',
    shadow_left: '_1px 0px 6px 0px #2D32390F',
    shadow_dropdown: '2px 0px 16px 0px #0000000A',

    transparent: 'transparent',
    white: '#FFFFFF',
  },
  dark: {
    /* primary */
    Primary_Color_Bolder: '#c8291e',
    Primary_Color_Main: '#e14337',
    Primary_Color_Lighter: '#E86D64',
    Primary_Color_Border: '#c8291e',
    Primary_Color_Background: '#e8f7ef',
    Primary_Color_Tag: '#c8291e',

    /* secondary 1 */
    Secondary_1_Color_Bolder: '#E36003',
    Secondary_1_Color_Main: '#FC7A1C',
    Secondary_1_Color_Tag: '#FC7A1C',
    Secondary_1_Color_Lighter: '#FD974F',
    Secondary_1_Color_Border: '#FED3B3',
    Secondary_1_Color_Background: '#FFF3EB',
    /* secondary 2 */
    Secondary_2_Color_Bolder: '#2D8655',
    Secondary_2_Color_Main: '#3AAC6D',
    Secondary_2_Color_Tag: '#3AAC6D',
    Secondary_2_Color_Lighter: '#53C586',
    Secondary_2_Color_Border: '#C6ECD7',
    Secondary_2_Color_Background: '#E8F7EF',
    /* secondary 3 */
    Secondary_3_Color_Bolder: '#22C3AE',
    Secondary_3_Color_Main: '#3CDDC7',
    Secondary_3_Color_Tag: '#3CDDC7',
    Secondary_3_Color_Lighter: '#67E4D4',
    Secondary_3_Color_Border: '#BEF4EC',
    Secondary_3_Color_Background: '#EEFCFA',
    /* secondary 4 */
    Secondary_4_Color_Bolder: '#7B22C3',
    Secondary_4_Color_Main: '#943CDD',
    Secondary_4_Color_Tag: '#943CDD',
    Secondary_4_Color_Lighter: '#AC67E4',
    Secondary_4_Color_Border: '#DBBEF4',
    Secondary_4_Color_Background: '#F6EEFC',
    /* secondary 5 */
    Secondary_5_Color_Bolder: '#E19405',
    Secondary_5_Color_Main: '#FAAD1E',
    Secondary_5_Color_Tag: '#FAAD1E',
    Secondary_5_Color_Lighter: '#FBBF50',
    Secondary_5_Color_Border: '#FDE4B4',
    Secondary_5_Color_Background: '#FFF8EB',
    /* secondary 6 */
    Secondary_6_Color_Bolder: '#C8291E',
    Secondary_6_Color_Main: '#E86D64',
    Secondary_6_Color_Tag: '#E86D64',
    Secondary_6_Color_Lighter: '#E86D64',
    Secondary_6_Color_Border: '#F5C0BC',
    Secondary_6_Color_Background: '#FCEEED',
    /* success */
    Success_Color_Bolder: '#2D8655',
    Success_Color_Main: '#3AAC6D',
    Success_Color_Tag: '#3AAC6D',
    Success_Color_Lighter: '#53C586',
    Success_Color_Border: '#C6ECD7',
    Success_Color_Background: '#E8F7EF',
    /* info */
    Info_Color_Bolder: '#0F62D7',
    Info_Color_Main: '#287CF0',
    Info_Color_Tag: '#287CF0',
    Info_Color_Lighter: '#5899F3',
    Info_Color_Border: '#B7D3FA',
    Info_Color_Background: '#ECF3FE',
    /* warning */
    Warning_Color_Bolder: '#E36003',
    Warning_Color_Main: '#FC7A1C',
    Warning_Color_Tag: '#FC7A1C',
    Warning_Color_Lighter: '#FD974F',
    Warning_Color_Border: '#FED3B3',
    Warning_Color_Background: '#FFF3EB',
    /* error */
    Error_Color_Bolder: '#C8291E',
    Error_Color_Main: '#E14337',
    Error_Color_Tag: '#E14337',
    Error_Color_Lighter: '#E86D64',
    Error_Color_Border: '#F5C0BC',
    Error_Color_Background: '#FCEEED',
    /* neutral_background */
    Neutral_Background_Color_Bolder: '#EAEAEC',
    Neutral_Background_Color_Absolute: '#FFFFFF',
    Neutral_Background_Color_Main: '#EFEFF0',
    Neutral_Background_Color_Lighter: '#F4F4F5',
    Neutral_Background_Color_Overlay: '#FFFFFF',
    Neutral_Background_Color_Disable: '#F4F4F5',
    Neutral_Background_Color_Selected: '#18181B14',
    Neutral_Background_Color_Hover: '#18181B0A',
    Neutral_Background_Color_Stable: '#FFFFFF',
    Neutral_Background_Color_Shadow: '#000000',
    /* reverse */
    Neutral_Background_Color_AbsoluteReverse: '#000000',
    Neutral_Background_Color_BolderReverse: '#18181B',
    Neutral_Background_Color_MainReverse: '#242428',
    Neutral_Background_Color_LighterReverse: '#313135',
    Neutral_Background_Color_DisableReverse: '#313135',
    Neutral_Background_Color_HoverReverse: '#000000',
    Neutral_Background_Color_StableReverse: '#000000',

    /* neutral_text */
    Neutral_Text_Color_Title: '#18181B',
    Neutral_Text_Color_Subtitle: '#61616B',
    Neutral_Text_Color_Body: '#313135',
    Neutral_Text_Color_Label: '#313135',
    Neutral_Text_Color_Placeholder: '#878792',
    Neutral_Text_Color_Disable: '#A2A2AA',
    Neutral_Text_Color_Stable: '#FFFFFF',
    Neutral_Text_Color_Hover: '#FFFFFF',
    Neutral_Text_Color_Selected: '#18181B',
    /* reverse */
    Neutral_Text_Color_TitleReverse: '#F4F4F5',
    Neutral_Text_Color_SubtitleReverse: '#A2A2AA',
    Neutral_Text_Color_BodyReverse: '#D7D7DB',
    Neutral_Text_Color_LabelReverse: '#EAEAEC',
    Neutral_Text_Color_PlaceholderReverse: '#A2A2AA',
    Neutral_Text_Color_DisableReverse: '#878792',
    Neutral_Text_Color_StableReverse: '#000000',
    /* neutral_border_color */
    Neutral_Border_Color_Bolder: '#D7D7DB',
    Neutral_Border_Color_Main: '#EAEAEC',
    Neutral_Border_Color_Lighter: '#F4F4F5',
    Neutral_Border_Color_Hover: '#ffffff',
    /* neutral_border */
    /* neutral_border_color reverse */
    /* neutral_border reverse */
    Neutral_Border_Color_BolderReverse: '1px solid #242428',
    Neutral_Border_Color_MainReverse: '1px solid #313135',
    Neutral_Border_Color_LighterReverse: '1px solid #494950',

    shadow_top: '0px _1px 6px 0px #2D32390F',
    shadow_bottom: '0px 1px 6px 0px #2D32390F',
    shadow_right: '1px 0px 6px 0px #2D32390F',
    shadow_left: '_1px 0px 6px 0px #2D32390F',
    shadow_dropdown: '2px 0px 16px 0px #0000000A',

    transparent: 'transparent',
    white: '#FFFFFF',
  },
};

// Type helper để lấy tất cả các key có sẵn
export type ColorThemeKeys = keyof typeof ColorThemes.light;
