import {TextStyle} from 'react-native';

export class TypoSkin {
  static regular0: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 14,
    fontSize: 10,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular1: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 16,
    fontSize: 12,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular2: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular3: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular4: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 28,
    fontSize: 20,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular5: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 32,
    fontSize: 14,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular6: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 38,
    fontSize: 30,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular7: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 48,
    fontSize: 38,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular8: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 54,
    fontSize: 46,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular9: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 64,
    fontSize: 56,
    fontWeight: '400',
    color: '#18181B',
  };

  static regular11: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 14,
    fontSize: 11,
    fontWeight: '400',
    color: '#18181B',
  };

  static medium1: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 16,
    fontSize: 12,
    fontWeight: '500',
    color: '#18181B',
  };

  static medium11: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 14,
    fontSize: 11,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold0: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 14,
    fontSize: 10,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold1: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 16,
    fontSize: 12,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold2: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold3: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 28,
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold4: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 28,
    fontSize: 20,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold5: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 32,
    fontSize: 24,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold6: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 48,
    fontSize: 30,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold7: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 48,
    fontSize: 38,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold8: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 54,
    fontSize: 46,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold9: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 64,
    fontSize: 56,
    fontWeight: '500',
    color: '#18181B',
  };

  static semibold11: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 14,
    fontSize: 11,
    fontWeight: '500',
    color: '#18181B',
  };

  static heading1: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 68,
    fontSize: 56,
    fontWeight: '500',
    color: '#18181B',
  };

  static heading2: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 56,
    fontSize: 46,
    fontWeight: '500',
    color: '#18181B',
  };

  static heading3: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 46,
    fontSize: 38,
    fontWeight: '500',
    color: '#18181B',
  };

  static heading4: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 38,
    fontSize: 30,
    fontWeight: '500',
    color: '#18181B',
  };

  static heading5: TextStyle = {
    fontFamily: 'NotoSansJP-SemiBold',
    fontSize: 22,
    fontWeight: '600',
    color: '#18181B',
  };

  static heading6: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 28,
    fontSize: 20,
    fontWeight: '500',
    color: '#18181B',
  };

  static heading7: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  };

  static heading8: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '500',
    color: '#18181B',
  };

  static heading9: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 20,
    fontSize: 12,
    fontWeight: 'normal',
    color: '#18181B',
  };

  static subtitle1: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 26,
    fontSize: 18,
    fontWeight: '400',
    color: '#18181B',
  };

  static subtitle2: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '400',
    color: '#18181B',
  };

  static subtitle3: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '400',
    color: '#18181B',
  };

  static subtitle4: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 16,
    fontSize: 12,
    fontWeight: '400',
    color: '#18181B',
  };

  static subtitle5: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 14,
    fontSize: 10,
    fontWeight: '400',
    color: '#18181B',
  };

  static body1: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 28,
    fontSize: 18,
    fontWeight: '400',
    color: '#18181B',
  };

  static body2: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '400',
    color: '#18181B',
  };

  static body3: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '400',
    color: '#18181B',
  };

  static buttonText1: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  };

  static buttonText2: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  };

  static buttonText3: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '500',
    color: '#18181B',
  };

  static buttonText4: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '400',
    color: '#18181B',
  };

  static buttonText5: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 16,
    fontSize: 12,
    fontWeight: '500',
    color: '#18181B',
  };

  static buttonText6: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 16,
    fontSize: 12,
    fontWeight: '400',
    color: '#18181B',
  };

  static label1: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  };

  static label2: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 22,
    fontSize: 16,
    fontWeight: '400',
    color: '#18181B',
  };

  static label3: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '500',
    color: '#18181B',
  };

  static label4: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '400',
    color: '#18181B',
  };

  static placeholder1: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 28,
    fontSize: 16,
    fontWeight: '400',
    color: '#18181B',
  };

  static placeholder2: TextStyle = {
    fontFamily: 'NotoSansJP-Regular',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '400',
    color: '#18181B',
  };

  static highlight1: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 68,
    fontSize: 56,
    fontWeight: '500',
    color: '#18181B',
  };

  static highlight2: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 56,
    fontSize: 46,
    fontWeight: '500',
    color: '#18181B',
  };

  static highlight3: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 46,
    fontSize: 38,
    fontWeight: '500',
    color: '#18181B',
  };

  static highlight4: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 38,
    fontSize: 30,
    fontWeight: '500',
    color: '#18181B',
  };

  static highlight5: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 32,
    fontSize: 24,
    fontWeight: '500',
    color: '#18181B',
  };

  static highlight6: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 28,
    fontSize: 20,
    fontWeight: '500',
    color: '#18181B',
  };

  static title1: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 32,
    fontSize: 24,
    fontWeight: '500',
    color: '#18181B',
  };

  static title2: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 28,
    fontSize: 20,
    fontWeight: '500',
    color: '#18181B',
  };

  static title3: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  };

  static title4: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 16,
    fontSize: 12,
    fontWeight: '500',
    color: '#18181B',
  };

  static title5: TextStyle = {
    fontFamily: 'NotoSansJP-Medium',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '500',
    color: '#18181B',
  };
}
