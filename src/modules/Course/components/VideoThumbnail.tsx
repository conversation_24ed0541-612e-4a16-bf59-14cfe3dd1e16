import React, {useState, useEffect, useRef} from 'react';
import {View, Image, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import VideoThumbnailGenerator from '../../../utils/VideoThumbnailGenerator';
import Video from 'react-native-video';

interface VideoThumbnailProps {
  videoUrl: string;
  width?: number;
  height?: number;
  style?: any;
  fallbackImage?: any;
}

const VideoThumbnail: React.FC<VideoThumbnailProps> = ({
  videoUrl,
  width = 80,
  height = 60,
  style,
  fallbackImage = require('../../../assets/appstore.png'),
}) => {
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [useVideoCapture, setUseVideoCapture] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const videoRef = useRef<any>(null);

  useEffect(() => {
    const generateThumbnail = async () => {
      setLoading(true);
      setError(false);
      setUseVideoCapture(false);

      try {
        // Try to generate thumbnail URL
        const thumbnail = VideoThumbnailGenerator.generateThumbnailUrl(
          videoUrl,
          {
            width,
            height,
            time: 1, // Capture at 1 second
          },
        );

        if (thumbnail === 'USE_VIDEO_CAPTURE') {
          // This is an MP4 or other video file, use video capture
          setUseVideoCapture(true);
          setLoading(false); // Let video component handle loading
        } else if (thumbnail) {
          setThumbnailUrl(thumbnail);
          setLoading(false);
        } else {
          // No thumbnail available, use fallback
          setError(true);
          setLoading(false);
        }
      } catch (err) {
        console.error('Error generating thumbnail:', err);
        setError(true);
        setLoading(false);
      }
    };

    if (videoUrl) {
      generateThumbnail();
    }
  }, [videoUrl, width, height]);

  // Handle video load for MP4 files
  const handleVideoLoad = (data: any) => {
    setVideoLoaded(true);
    // Seek to 1 second to get a good thumbnail frame
    if (videoRef.current) {
      videoRef.current.seek(1);
    }
  };

  const handleVideoError = () => {
    setError(true);
    setUseVideoCapture(false);
  };

  const handleImageError = () => {
    setError(true);
  };

  return (
    <View style={[styles.container, {width, height}, style]}>
      {loading && (
        <View style={[styles.loadingOverlay, {width, height}]}>
          <View style={styles.loadingIndicator} />
        </View>
      )}

      {/* For MP4 and other video files - use video capture */}
      {useVideoCapture && !error && (
        <Video
          ref={videoRef}
          source={{uri: videoUrl}}
          style={[styles.videoPlayer, {width, height}]}
          paused={true}
          muted={true}
          resizeMode="cover"
          onLoad={handleVideoLoad}
          onError={handleVideoError}
          poster={undefined}
        />
      )}

      {/* For YouTube/Vimeo - use thumbnail URL */}
      {!loading && !error && !useVideoCapture && thumbnailUrl && (
        <Image
          source={{uri: thumbnailUrl}}
          style={[styles.thumbnailImage, {width, height}]}
          resizeMode="cover"
          onError={handleImageError}
        />
      )}

      {/* Fallback image */}
      {(error || (!loading && !useVideoCapture && !thumbnailUrl)) && (
        <Image
          source={fallbackImage}
          style={[styles.fallbackImage, {width, height}]}
          resizeMode="cover"
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderRadius: 6,
    overflow: 'hidden',
  },
  videoPlayer: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  thumbnailImage: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  fallbackImage: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  loadingIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: ColorThemes.light.Neutral_Text_Color_Subtitle,
    opacity: 0.3,
  },
});

export default VideoThumbnail;
