import React, {useState} from 'react';
import {View, StyleSheet, TouchableOpacity} from 'react-native';
import {VideoPlayer} from '../../../utils/VideoPlayer';
import VideoPlayerFullscreen from './VideoPlayerFullscreen';
import {Winicon} from 'wini-mobile-components';

interface VideoPlayerWithFullscreenProps {
  source: string;
  onProgressPercent?: (percent: number) => void;
  onLoad?: (time: number) => void;
}

const VideoPlayerWithFullscreen: React.FC<VideoPlayerWithFullscreenProps> = ({
  source,
  onProgressPercent,
  onLoad,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);

  // Xử lý tiến độ video và lưu thời gian hiện tại
  const handleProgress = (percent: number) => {
    // Ước tính thời gian hiện tại dựa trên phần trăm
    if (onProgressPercent) {
      onProgressPercent(percent);
    }
  };

  // Xử lý khi video được tải
  const handleLoad = (time: number) => {
    if (onLoad) {
      onLoad(time);
    }
  };

  return (
    <>
      {/* Video Player thường */}
      {!isFullscreen && (
        <View style={styles.container}>
          <VideoPlayer
            source={source}
            onProgressPercent={handleProgress}
            onLoad={handleLoad}
            customFullscreenUI={true}
            onFullscreenChange={isFullscreenMode => {
              setIsFullscreen(isFullscreenMode);
            }}
            disableFullscreen={true}
          />

          {/* Nút fullscreen */}
          <TouchableOpacity
            style={styles.fullscreenButton}
            onPress={() => {
              setIsFullscreen(true);
            }}>
            <Winicon src="fill/arrows/fullscreen" size={16} color="#fff" />
          </TouchableOpacity>
        </View>
      )}

      {/* Video Player fullscreen */}
      {isFullscreen && (
        <VideoPlayerFullscreen
          source={source}
          initialPosition={currentTime}
          onProgressPercent={handleProgress}
          onClose={() => {
            setIsFullscreen(false);
          }}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
  },
  fullscreenButton: {
    position: 'absolute',
    bottom: 23,
    right: 10,
    borderRadius: 20,
    padding: 8,
    zIndex: 100,
  },
});

export default VideoPlayerWithFullscreen;
