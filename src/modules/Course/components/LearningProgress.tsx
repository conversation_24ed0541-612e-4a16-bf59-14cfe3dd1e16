/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import Svg, {Circle} from 'react-native-svg';

interface LearningProgressProps {
  progress: number; // 0-100
  title?: string;
  subtitle?: string;
  backgroundColor?: string;
  progressColor?: string;
  size?: number;
  strokeWidth?: number;
}

const LearningProgress: React.FC<LearningProgressProps> = ({
  progress,
  title = 'Learning Progress',
  subtitle,
  backgroundColor = ColorThemes.light.Info_Color_Background,
  progressColor = ColorThemes.light.Primary_Color_Main,
  size = 80,
  strokeWidth = 8,
}) => {
  // Tính toán các giá trị cho vòng tròn tiến độ
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  // Xác định subtitle dựa trên tiến độ nếu không được cung cấp
  const defaultSubtitle = () => {
    if (progress < 25) return 'Just started';
    if (progress < 50) return 'Making progress';
    if (progress < 75) return 'Almost done';
    if (progress < 100) return 'Almost complete';
    return 'Completed';
  };

  const displaySubtitle = subtitle || defaultSubtitle();

  return (
    <View style={[styles.container, {backgroundColor}]}>
      <View style={styles.contentContainer}>
        <View style={styles.textContainer}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subtitle}>{displaySubtitle}</Text>
        </View>

        <View style={styles.progressContainer}>
          <Svg width={size} height={size}>
            {/* Background Circle */}
            <Circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              stroke="#FFFFFF"
              strokeWidth={strokeWidth}
              fill="transparent"
            />
            {/* Progress Circle */}
            <Circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              stroke={progressColor}
              strokeWidth={strokeWidth}
              strokeDasharray={`${circumference} ${circumference}`}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              fill="transparent"
              transform={`rotate(-90, ${size / 2}, ${size / 2})`}
            />
          </Svg>
          <Text style={[styles.progressText, {color: progressColor}]}>
            {Math.round(progress)}%
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  contentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 4,
  },
  subtitle: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  progressContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressText: {
    ...TypoSkin.heading7,
    position: 'absolute',
    textAlign: 'center',
  },
});

export default LearningProgress;
