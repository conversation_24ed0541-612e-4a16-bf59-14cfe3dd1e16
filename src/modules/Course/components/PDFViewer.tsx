import React, {useState, useRef, useEffect, useCallback} from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import WebView from 'react-native-webview';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import EmptyPage from '../../../Screen/emptyPage';

interface PDFViewerProps {
  url: string;
  fileName?: string;
  height?: number;
  onError?: (error: any) => void;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  maxFileSize?: number; // MB
  enableOptimization?: boolean;
  useGoogleViewer?: boolean; // Force use Google Docs Viewer
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  url,
  fileName = 'Tài liệu PDF',
  height = 500,
  onError,
  onLoadStart,
  onLoadEnd,
  maxFileSize = 10, // Default 10MB
  enableOptimization = true,
  useGoogleViewer = true, // Default to Google Docs Viewer
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [fileSize, setFileSize] = useState<number | null>(null);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [showWarning, setShowWarning] = useState(false);
  const [useOptimizedView, setUseOptimizedView] = useState(false);
  const [useGoogleDocsViewer, setUseGoogleDocsViewer] =
    useState(useGoogleViewer);
  const [retryCount, setRetryCount] = useState(0);
  const webViewRef = useRef<WebView>(null);
  const loadTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const {height: screenHeight} = Dimensions.get('window');

  // Improved URL processing
  const getFullPdfUrl = () => {
    // If it's already a full URL, use it directly
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // Build full URL from ConfigAPI
    let baseUrl = ConfigAPI.url.replace('/api/', '');

    // Ensure baseUrl ends with /
    if (!baseUrl.endsWith('/')) {
      baseUrl += '/';
    }

    // Ensure url doesn't start with /
    let cleanUrl = url.startsWith('/') ? url.substring(1) : url;

    const fullUrl = baseUrl + cleanUrl;

    // Fix any double slashes (except after protocol)
    return fullUrl.replace(/([^:]\/)\/+/g, '$1');
  };

  const fullPdfUrl = getFullPdfUrl();

  // Option to use Google Docs Viewer as fallback
  const googleDocsUrl = `https://docs.google.com/gview?embedded=true&url=${encodeURIComponent(
    fullPdfUrl,
  )}`;

  // Debug logging
  console.log('PDFViewer Debug Info:');
  console.log('- Original URL:', url);
  console.log('- ConfigAPI.url:', ConfigAPI.url);
  console.log('- Full PDF URL:', fullPdfUrl);
  console.log('- Use Google Viewer:', useGoogleDocsViewer);
  console.log('- Google Docs URL:', googleDocsUrl);
  console.log('- Loading state:', loading);
  console.log('- Error state:', error);

  // Check file size before loading
  const checkFileSize = useCallback(async () => {
    if (!enableOptimization || !fullPdfUrl) {
      return;
    }

    try {
      const response = await fetch(fullPdfUrl, {method: 'HEAD'});
      const contentLength = response.headers.get('content-length');

      if (contentLength) {
        const sizeInMB = parseInt(contentLength, 10) / (1024 * 1024);
        setFileSize(sizeInMB);
        console.log(`File size detected: ${sizeInMB.toFixed(1)}MB`);

        // Google Docs Viewer limits
        const GOOGLE_VIEWER_LIMIT = 25; // MB
        const WEBVIEW_SAFE_LIMIT = 10; // MB - Safe limit for WebView

        if (sizeInMB > GOOGLE_VIEWER_LIMIT && useGoogleDocsViewer) {
          console.warn(
            `File size ${sizeInMB.toFixed(
              1,
            )}MB exceeds Google Docs Viewer limit (${GOOGLE_VIEWER_LIMIT}MB)`,
          );
          // Auto-switch to direct URL for very large files
          setUseGoogleDocsViewer(false);
          setShowWarning(true);
          setUseOptimizedView(true);
        } else if (sizeInMB > WEBVIEW_SAFE_LIMIT) {
          console.warn(
            `File size ${sizeInMB.toFixed(1)}MB is large, showing warning`,
          );
          // For large files, prefer Google Docs Viewer if not already using it
          if (!useGoogleDocsViewer) {
            console.log('Switching to Google Docs Viewer for large file');
            setUseGoogleDocsViewer(true);
          }
          setShowWarning(true);
          setUseOptimizedView(false); // Don't force optimized view yet
        } else if (sizeInMB > maxFileSize) {
          setShowWarning(true);
          setUseOptimizedView(true);
        }
      }
    } catch (fetchError) {
      console.warn('Could not check file size:', fetchError);
    }
  }, [fullPdfUrl, maxFileSize, enableOptimization, useGoogleDocsViewer]);

  // Simplified timeout - no complex dependencies
  useEffect(() => {
    if (loading) {
      loadTimeoutRef.current = setTimeout(() => {
        if (loading) {
          setError('Tài liệu đang tải lâu. Vui lòng thử lại.');
          setLoading(false);
        }
      }, 30000); // 30 second timeout
    }

    return () => {
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
      }
    };
  }, [loading]);

  // Simplified initialization - no complex dependencies
  useEffect(() => {
    console.log('PDF Viewer initialized for URL:', url);
    setLoading(true);
    setError(null);
    setLoadingProgress(0);
    setRetryCount(0);
    setShowWarning(false);
    setUseOptimizedView(false);
    setUseGoogleDocsViewer(useGoogleViewer);

    // Clear any existing timeout
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }

    // Check file size for large file handling
    if (enableOptimization) {
      checkFileSize().catch(err => {
        console.warn('File size check failed:', err);
      });
    }
  }, [url, useGoogleViewer, enableOptimization, checkFileSize]); // Simple dependencies

  // Add timeout for loading with dynamic duration based on file size
  useEffect(() => {
    if (loading) {
      // Dynamic timeout based on file size
      let timeoutDuration = 30000; // Default 30 seconds

      if (fileSize) {
        if (fileSize > 25) {
          timeoutDuration = 120000; // 2 minutes for very large files
        } else if (fileSize > 10) {
          timeoutDuration = 60000; // 1 minute for large files
        } else if (fileSize > 5) {
          timeoutDuration = 45000; // 45 seconds for medium files
        }
      }

      console.log(
        `Setting timeout for ${
          timeoutDuration / 1000
        } seconds (file size: ${fileSize?.toFixed(1)}MB)`,
      );

      loadTimeoutRef.current = setTimeout(() => {
        if (loading) {
          const errorMsg =
            fileSize && fileSize > 10
              ? `Tài liệu quá lớn (${fileSize.toFixed(
                  1,
                )}MB) và tải lâu. Thử tải xuống để xem.`
              : 'Tài liệu đang tải lâu. Vui lòng thử lại.';
          setError(errorMsg);
          setLoading(false);
        }
      }, timeoutDuration);
    }

    return () => {
      if (loadTimeoutRef.current) {
        clearTimeout(loadTimeoutRef.current);
      }
    };
  }, [loading, fileSize]);

  const handleLoadStart = () => {
    setLoading(true);
    setError(null);
    setLoadingProgress(0);
    if (onLoadStart) {
      onLoadStart();
    }
  };

  const handleLoadEnd = () => {
    setLoading(false);
    setLoadingProgress(100);
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }
    if (onLoadEnd) {
      onLoadEnd();
    }
  };

  const handleLoadProgress = (event: any) => {
    const progress = event.nativeEvent.progress * 100;
    setLoadingProgress(progress);
  };

  const handleError = (syntheticEvent: any) => {
    const {nativeEvent} = syntheticEvent;
    console.error('PDF load error:', nativeEvent);
    console.error(
      'Current URL being loaded:',
      useGoogleDocsViewer ? googleDocsUrl : fullPdfUrl,
    );
    console.error('Retry count:', retryCount);

    setLoading(false);
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }

    // Safe auto-fallback logic
    if (useGoogleDocsViewer && retryCount === 0) {
      console.log('Auto-fallback from Google Docs Viewer to Direct URL...');
      setRetryCount(1);
      setUseGoogleDocsViewer(false);
      setError(null);
      setLoading(true);
      // Force WebView to reload
      setTimeout(() => {
        webViewRef.current?.reload();
      }, 100);
      return;
    }

    // If direct URL also fails, try Google Docs Viewer as last resort
    if (!useGoogleDocsViewer && retryCount === 1) {
      console.log(
        'Direct URL failed, trying Google Docs Viewer as last resort...',
      );
      setRetryCount(2);
      setUseGoogleDocsViewer(true);
      setError(null);
      setLoading(true);
      setTimeout(() => {
        webViewRef.current?.reload();
      }, 100);
      return;
    }

    // Show error after all retries
    let errorMessage = 'Không thể tải tài liệu PDF.';
    if (retryCount > 0) {
      errorMessage += ' (Đã thử cả 2 phương thức)';
    }

    setError(errorMessage + ' Vui lòng thử lại.');
    if (onError) {
      onError(nativeEvent);
    }
  };

  const forceOptimizedView = () => {
    setUseOptimizedView(true);
    setShowWarning(false);
  };

  const proceedWithNormalView = () => {
    setShowWarning(false);
    setUseOptimizedView(false);
  };

  const tryGoogleDocsViewer = () => {
    console.log('Manual retry with Google Docs Viewer');
    setUseGoogleDocsViewer(true);
    setRetryCount(0);
    setError(null);
    setLoading(true);
    setLoadingProgress(0);
    // Clear any existing timeout
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }
    // Force reload after state update
    setTimeout(() => {
      webViewRef.current?.reload();
    }, 100);
  };

  const tryDirectURL = () => {
    console.log('Manual retry with Direct URL');
    setUseGoogleDocsViewer(false);
    setRetryCount(0);
    setError(null);
    setLoading(true);
    setLoadingProgress(0);
    // Clear any existing timeout
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
    }
    // Force reload after state update
    setTimeout(() => {
      webViewRef.current?.reload();
    }, 100);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const renderError = () => (
    <View style={styles.errorContainer}>
      <Winicon
        src="outline/user interface/alert-triangle"
        size={48}
        color={ColorThemes.light.Error_Color_Main}
      />
      <Text style={styles.errorText}>{error}</Text>
      <Text style={styles.errorSubtext}>
        URL: {useGoogleDocsViewer ? 'Google Docs Viewer' : fullPdfUrl}
      </Text>
      <View style={styles.errorButtons}>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={tryGoogleDocsViewer}>
          <Text style={styles.retryButtonText}>Thử Google Viewer</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.retryButtonSecondary}
          onPress={tryDirectURL}>
          <Text style={styles.retryButtonSecondaryText}>Thử URL gốc</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderWarning = () => {
    const GOOGLE_VIEWER_LIMIT = 25;
    const WEBVIEW_SAFE_LIMIT = 10;
    const isOverGoogleLimit = fileSize && fileSize > GOOGLE_VIEWER_LIMIT;
    const isOverWebViewLimit = fileSize && fileSize > WEBVIEW_SAFE_LIMIT;

    return (
      <View style={styles.warningContainer}>
        <Winicon
          src="outline/user interface/alert-triangle"
          size={48}
          color={ColorThemes.light.Warning_Color_Main}
        />
        <Text style={styles.warningTitle}>
          Tài liệu lớn ({fileSize?.toFixed(1)} MB)
        </Text>
        <Text style={styles.warningText}>
          {isOverGoogleLimit
            ? `Tài liệu này vượt quá giới hạn Google Docs Viewer (${GOOGLE_VIEWER_LIMIT}MB). Khuyến nghị tải xuống để xem hoặc thử URL gốc.`
            : isOverWebViewLimit
            ? `Tài liệu này có kích thước lớn (${fileSize?.toFixed(
                1,
              )}MB) và có thể tải chậm hoặc gây lag. Khuyến nghị tải xuống để xem tốt hơn.`
            : 'Tài liệu này có kích thước lớn và có thể tải chậm. Bạn có muốn tiếp tục?'}
        </Text>
        <View style={styles.warningButtons}>
          <TouchableOpacity
            style={styles.warningButtonSecondary}
            onPress={forceOptimizedView}>
            <Text style={styles.warningButtonSecondaryText}>Tải xuống</Text>
          </TouchableOpacity>
          {!isOverGoogleLimit && (
            <TouchableOpacity
              style={styles.warningButtonPrimary}
              onPress={proceedWithNormalView}>
              <Text style={styles.warningButtonPrimaryText}>
                {isOverWebViewLimit ? 'Vẫn xem (chậm)' : 'Tiếp tục xem'}
              </Text>
            </TouchableOpacity>
          )}
          {isOverGoogleLimit && (
            <TouchableOpacity
              style={styles.warningButtonPrimary}
              onPress={() => {
                setUseGoogleDocsViewer(false);
                proceedWithNormalView();
              }}>
              <Text style={styles.warningButtonPrimaryText}>Thử URL gốc</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator
        size="large"
        color={ColorThemes.light.Primary_Color_Main}
      />
      <Text style={styles.loadingText}>
        Đang tải tài liệu...{' '}
        {loadingProgress > 0 && `${Math.round(loadingProgress)}%`}
      </Text>
      {loadingProgress > 0 && (
        <View style={styles.progressBarContainer}>
          <View style={[styles.progressBar, {width: `${loadingProgress}%`}]} />
        </View>
      )}
      {fileSize && fileSize > 5 && (
        <Text style={styles.loadingSubtext}>
          Tài liệu lớn ({fileSize.toFixed(1)} MB), vui lòng chờ...
        </Text>
      )}
    </View>
  );

  const containerHeight = height;

  return (
    <View style={[styles.container, {height: containerHeight}]}>
      {/* PDF Content */}
      <View style={styles.pdfContainer}>
        {showWarning ? (
          renderWarning()
        ) : error ? (
          renderError()
        ) : (
          <>
            {loading && renderLoading()}
            <WebView
              ref={webViewRef}
              source={{uri: useGoogleDocsViewer ? googleDocsUrl : fullPdfUrl}}
              style={styles.webView}
              onLoadStart={handleLoadStart}
              onLoadEnd={handleLoadEnd}
              onLoadProgress={handleLoadProgress}
              onError={handleError}
              startInLoadingState={false}
              scalesPageToFit={true}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              originWhitelist={['*']}
              mixedContentMode="compatibility"
              allowsInlineMediaPlayback={true}
              mediaPlaybackRequiresUserAction={false}
              // Optimizations for large files
              cacheEnabled={fileSize ? fileSize < 10 : true} // Disable cache for large files
              incognito={fileSize ? fileSize > 10 : false} // Use incognito for large files
              thirdPartyCookiesEnabled={false}
              sharedCookiesEnabled={false}
              // Memory optimizations for large files
              androidLayerType={
                fileSize && fileSize > 10 ? 'software' : 'hardware'
              }
              // Simple key for re-rendering
              key={`webview-${
                useGoogleDocsViewer ? 'google' : 'direct'
              }-${url}-${retryCount}`}
            />
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  fileNameContainer: {
    flex: 1,
    marginLeft: 8,
  },
  fileName: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  viewerIndicator: {
    ...TypoSkin.body2,
    fontSize: 10,
    color: ColorThemes.light.Primary_Color_Main,
    backgroundColor: ColorThemes.light.primary_light_color,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginTop: 2,
    alignSelf: 'flex-start',
  },
  fullscreenButton: {
    padding: 4,
  },
  pdfContainer: {
    flex: 1,
    position: 'relative',
    width: '100%',
  },
  webView: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: 1,
  },
  loadingText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginTop: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.Error_Color_Main,
    textAlign: 'center',
    marginVertical: 16,
  },
  retryButton: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  retryButtonText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
    fontWeight: '600',
  },
  errorSubtext: {
    ...TypoSkin.body2,
    fontSize: 12,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    textAlign: 'center',
    marginVertical: 8,
    fontFamily: 'monospace',
  },
  errorButtons: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  retryButtonSecondary: {
    backgroundColor: 'transparent',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: ColorThemes.light.Primary_Color_Main,
  },
  retryButtonSecondaryText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Primary_Color_Main,
    fontWeight: '600',
  },
  // Warning styles
  warningContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  warningTitle: {
    ...TypoSkin.title3,
    color: ColorThemes.light.Warning_Color_Main,
    textAlign: 'center',
    marginVertical: 12,
  },
  warningText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  warningButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  warningButtonPrimary: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  warningButtonSecondary: {
    backgroundColor: 'transparent',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  warningButtonPrimaryText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
    fontWeight: '600',
  },
  warningButtonSecondaryText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: '600',
  },
  // Progress styles
  progressBarContainer: {
    width: '100%',
    height: 4,
    backgroundColor: ColorThemes.light.Neutral_Border_Color_Main,
    borderRadius: 2,
    marginTop: 12,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    borderRadius: 2,
  },
  loadingSubtext: {
    ...TypoSkin.body2,
    fontSize: 12,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default PDFViewer;
