import React, {useState, useEffect} from 'react';
import {View, Image, StyleSheet} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {Winicon} from 'wini-mobile-components';
import VideoThumbnailGenerator from '../../../utils/VideoThumbnailGenerator';

interface SimpleVideoThumbnailProps {
  videoUrl: string;
  width?: number;
  height?: number;
  style?: any;
  fallbackImage?: any;
}

/**
 * Simple Video Thumbnail Component
 *
 * This is a simpler alternative that:
 * 1. Shows YouTube/Vimeo thumbnails when available
 * 2. Shows a video icon overlay for MP4 files
 * 3. Falls back to placeholder image
 */
const SimpleVideoThumbnail: React.FC<SimpleVideoThumbnailProps> = ({
  videoUrl,
  width = 80,
  height = 60,
  style,
  fallbackImage = require('../../../assets/appstore.png'),
}) => {
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [isVideoFile, setIsVideoFile] = useState(false);

  useEffect(() => {
    const generateThumbnail = async () => {
      setLoading(true);
      setError(false);
      setIsVideoFile(false);

      try {
        // Try to generate thumbnail URL
        const thumbnail = VideoThumbnailGenerator.generateThumbnailUrl(
          videoUrl,
          {
            width,
            height,
            time: 1,
          },
        );

        if (thumbnail === 'USE_VIDEO_CAPTURE') {
          // This is an MP4 or other video file
          setIsVideoFile(true);
          setThumbnailUrl(null);
        } else if (thumbnail) {
          // YouTube/Vimeo thumbnail
          setThumbnailUrl(thumbnail);
        } else {
          // No thumbnail available
          setError(true);
        }
      } catch (err) {
        console.error('Error generating thumbnail:', err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    if (videoUrl) {
      generateThumbnail();
    }
  }, [videoUrl, width, height]);

  const handleImageError = () => {
    setError(true);
  };

  return (
    <View style={[styles.container, {width, height}, style]}>
      {loading && (
        <View style={[styles.loadingOverlay, {width, height}]}>
          <View style={styles.loadingIndicator} />
        </View>
      )}

      {/* YouTube/Vimeo thumbnail */}
      {!loading && !error && thumbnailUrl && (
        <Image
          source={{uri: thumbnailUrl}}
          style={[styles.thumbnailImage, {width, height}]}
          resizeMode="cover"
          onError={handleImageError}
        />
      )}

      {/* MP4 video file - show placeholder with video icon */}
      {!loading && !error && isVideoFile && (
        <View style={[styles.videoFileContainer, {width, height}]}>
          <Image
            source={fallbackImage}
            style={[styles.fallbackImage, {width, height}]}
            resizeMode="cover"
          />
          <View style={styles.videoIconOverlay}>
            <Winicon
              src="fill/media/play"
              size={Math.min(width, height) * 0.3}
              color="rgba(255, 255, 255, 0.9)"
            />
          </View>
        </View>
      )}

      {/* Fallback image */}
      {(error || (!loading && !thumbnailUrl && !isVideoFile)) && (
        <View style={[styles.videoFileContainer, {width, height}]}>
          <Image
            source={fallbackImage}
            style={[styles.fallbackImage, {width, height}]}
            resizeMode="cover"
          />
          <View style={styles.videoIconOverlay}>
            <Winicon
              src="outline/media/video"
              size={Math.min(width, height) * 0.25}
              color="rgba(255, 255, 255, 0.7)"
            />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderRadius: 6,
    overflow: 'hidden',
  },
  thumbnailImage: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  videoFileContainer: {
    position: 'relative',
  },
  fallbackImage: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  videoIconOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  loadingIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: ColorThemes.light.Neutral_Text_Color_Subtitle,
    opacity: 0.3,
  },
});

export default SimpleVideoThumbnail;
