import React from 'react';
import {TouchableOpacity, StyleSheet} from 'react-native';
import {Winicon} from 'wini-mobile-components';

interface FullscreenButtonProps {
  onPress: () => void;
  size?: number;
  color?: string;
  style?: any;
}

const FullscreenButton: React.FC<FullscreenButtonProps> = ({
  onPress,
  size = 24,
  color = '#fff',
  style,
}) => {
  return (
    <TouchableOpacity
      style={[styles.fullscreenButton, style]}
      onPress={onPress}>
      <Winicon src="fill/arrows/fullscreen" size={size} color={color} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  fullscreenButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 8,
    zIndex: 100,
  },
});

export default FullscreenButton;
