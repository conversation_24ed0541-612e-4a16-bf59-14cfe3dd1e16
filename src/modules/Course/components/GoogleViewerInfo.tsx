import React from 'react';
import {View, StyleSheet, Text, TouchableOpacity} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

interface GoogleViewerInfoProps {
  onClose: () => void;
}

const GoogleViewerInfo: React.FC<GoogleViewerInfoProps> = ({onClose}) => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Google Docs Viewer - Thông tin</Text>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Winicon src="outline/user interface/e-remove" size={20} />
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        <View style={styles.section}>
          <Winicon
            src="outline/user interface/info"
            size={24}
            color={ColorThemes.light.Primary_Color_Main}
          />
          <Text style={styles.sectionTitle}>Giới hạn File Size</Text>
        </View>
        <Text style={styles.text}>
          • <Text style={styles.highlight}>PDF: 25MB</Text> (giới hạn chính thức)
        </Text>
        <Text style={styles.text}>
          • File lớn hơn có thể load nhưng không ổn định
        </Text>
        <Text style={styles.text}>
          • Timeout: 15 giây cho file > 25MB
        </Text>

        <View style={styles.section}>
          <Winicon
            src="outline/user interface/check"
            size={24}
            color={ColorThemes.light.Success_Color_Main}
          />
          <Text style={styles.sectionTitle}>Ưu điểm</Text>
        </View>
        <Text style={styles.text}>✅ Tương thích cao với mọi PDF</Text>
        <Text style={styles.text}>✅ Không cần CORS configuration</Text>
        <Text style={styles.text}>✅ Zoom/Pan controls tự động</Text>
        <Text style={styles.text}>✅ Responsive design</Text>

        <View style={styles.section}>
          <Winicon
            src="outline/user interface/alert-triangle"
            size={24}
            color={ColorThemes.light.Warning_Color_Main}
          />
          <Text style={styles.sectionTitle}>Lưu ý</Text>
        </View>
        <Text style={styles.text}>⚠️ Cần kết nối internet</Text>
        <Text style={styles.text}>⚠️ PDF được gửi qua Google servers</Text>
        <Text style={styles.text}>⚠️ Chậm hơn direct loading</Text>
        <Text style={styles.text}>⚠️ Không phù hợp tài liệu nhạy cảm</Text>

        <View style={styles.section}>
          <Winicon
            src="outline/user interface/settings"
            size={24}
            color={ColorThemes.light.Neutral_Text_Color_Title}
          />
          <Text style={styles.sectionTitle}>Khuyến nghị</Text>
        </View>
        <Text style={styles.text}>
          • <Text style={styles.highlight}>File &lt; 25MB:</Text> Dùng Google Viewer
        </Text>
        <Text style={styles.text}>
          • <Text style={styles.highlight}>File &gt; 25MB:</Text> Dùng Direct URL hoặc tải xuống
        </Text>
        <Text style={styles.text}>
          • <Text style={styles.highlight}>Tài liệu nhạy cảm:</Text> Dùng Direct URL
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderRadius: 12,
    margin: 16,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
  },
  title: {
    ...TypoSkin.title3,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  section: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    ...TypoSkin.title4,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginLeft: 8,
  },
  text: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Body,
    marginBottom: 4,
    lineHeight: 20,
  },
  highlight: {
    fontWeight: '600',
    color: ColorThemes.light.Primary_Color_Main,
  },
});

export default GoogleViewerInfo;
