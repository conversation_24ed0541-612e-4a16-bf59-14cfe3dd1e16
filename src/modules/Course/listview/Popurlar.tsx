/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  Pressable,
  Dimensions,
  ScrollView,
} from 'react-native';
import PagerView from 'react-native-pager-view';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {
  DefaultProduct,
  SkeletonPlaceCard,
} from '../../Default/card/defaultProduct';
import {CourseDA} from '../da';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {StorageContanst} from '../../../Config/Contanst';
import {RootScreen} from '../../../router/router';
import {useNavigation} from '@react-navigation/native';
import {App<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>og, Winicon} from 'wini-mobile-components';
import {useTranslation} from 'react-i18next';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import EmptyPage from '../../../Screen/emptyPage';

interface Props {
  titleList?: string;
}

export default function CoursePopular(props: Props) {
  const [isLoading, setLoading] = useState(true);
  const navigation = useNavigation<any>();
  // const [isLoadMore, setLoadMore] = useState(false);
  const [data, setData] = useState<Array<any>>([]);
  const dialogRef = useRef<any>(null);
  const {t} = useTranslation();
  const courseDA = new CourseDA();
  useEffect(() => {
    getData();
  }, []);
  const pages = (array: any[], size: number) => {
    const result = [];
    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size));
    }
    return result;
  };
  const getData = async () => {
    const result = await courseDA.getCoursePopular(10);
    if (result) {
      const lst = await Promise.all(
        result.data.map(async (item: any) => {
          return {
            ...item,
            IsLike: await courseDA.checkCourseIsWishlishCustomer(item.Id),
          };
        }),
      );
      setData(lst);
    }
    setLoading(false);
  };
  return (
    <View style={{width: '100%', height: undefined}}>
      <FDialog ref={dialogRef} />
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingBottom: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            {props.titleList}
          </Text>
        </View>
      ) : null}
      <Pressable style={{flex: 1, paddingBottom: 32}}>
        <ScrollView
          style={{height: '100%'}}
          horizontal
          showsHorizontalScrollIndicator={false}>
          {pages(data, 5).map((item, index) => (
            <Pressable key={index}>
              <FlatList
                nestedScrollEnabled
                scrollEnabled={false}
                data={item}
                contentContainerStyle={{
                  gap: 12,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                }}
                renderItem={({item, index}) => {
                  return (
                    <DefaultProduct
                      key={index}
                      flexDirection="row"
                      containerStyle={{
                        paddingHorizontal: 16,
                        width: Dimensions.get('window').width - 32,
                      }}
                      onPressDetail={() => {
                        navigation.push(RootScreen.CourseDetail, {id: item.Id});
                      }}
                      imgStyle={{
                        borderColor:
                          ColorThemes.light.Neutral_Border_Color_Main,
                        borderWidth: 1,
                      }}
                      titleStyle={{
                        ...TypoSkin.heading7,
                        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                      }}
                      // actionView={
                      //   <View
                      //     style={{
                      //       flexDirection: 'row',
                      //       height: 32,
                      //       width: 232,
                      //       backgroundColor: 'red',
                      //     }}>
                      //     <Winicon
                      //       src={'outline/emoticons/heart'}
                      //       size={18}
                      //       color={ColorThemes.light.Neutral_Text_Color_Title}
                      //     />
                      //   </View>
                      // }
                      onPressLikeAction={async () => {
                        var accessToken = await getDataToAsyncStorage(
                          StorageContanst.accessToken,
                        );
                        if (accessToken) {
                          if (item.IsLike === true) {
                            const result = await courseDA.deleteWishlistCourse(
                              item.Id,
                            );
                            if (result.code === 200) {
                              setData(prevData =>
                                prevData.map(a =>
                                  a.Id === item.Id
                                    ? {...item, IsLike: false}
                                    : a,
                                ),
                              );
                            }
                          } else {
                            const result = await courseDA.addWishlistCourse(
                              item.Id,
                            );
                            if (result) {
                              setData(prevData =>
                                prevData.map(a =>
                                  a.Id === item.Id
                                    ? {...item, IsLike: true}
                                    : a,
                                ),
                              );
                            }
                          }
                        } else {
                          //TODO: add wishlish chưa có token thì navigate tới login và truyền theo router.
                          dialogCheckAcc(dialogRef);
                        }
                      }}
                      data={item}
                    />
                  );
                }}
                style={{width: '100%', height: '100%'}}
                keyExtractor={a => a.Id?.toString()}
                onEndReachedThreshold={0.5}
                ListEmptyComponent={() => {
                  if (isLoading) {
                    return [1, 2, 3].map((_, index) => (
                      <SkeletonPlaceCard key={`skeleton-${index}`} />
                    ));
                  }
                  return <EmptyPage title={t('nodata')} />;
                }}
              />
            </Pressable>
          ))}
        </ScrollView>
      </Pressable>
    </View>
  );
}
