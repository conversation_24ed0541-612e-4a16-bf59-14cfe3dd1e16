import {ScrollView, StyleSheet, Text, View} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {ListTile, StepCircleProgress, Winicon} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';
import {
  JSXElementConstructor,
  Key,
  ReactElement,
  ReactNode,
  ReactPortal,
  useEffect,
  useState,
} from 'react';
import {CourseDA} from '../da';
import LearningProgress from '../components/LearningProgress';

export default function Lesson(pros: any) {
  const [data, setData] = useState<any>(pros.data);
  const [lessonList, setlessonList] = useState<Array<any>>();
  const courseDA = new CourseDA();
  useEffect(() => {
    getLesson();
  }, []);
  const getLesson = async () => {
    const result = await courseDA.getLessonInCourseDetail(
      data?.Id,
      pros.checkBuy,
    );
    if (result) {
      // Thêm trạng thái hoàn thành cho mỗi step
      // const enhancedResult = result.map((lesson: any) => {
      //   // Giả lập dữ liệu hoàn thành (trong thực tế sẽ lấy từ API)
      //   const completedSteps = Math.floor(Math.random() * lesson.listItem?.length);

      //   return {
      //     ...lesson,
      //     completedSteps: completedSteps,
      //     listItem: lesson.listItem?.map((item: any, idx: number) => ({
      //       ...item,
      //       isCompleted: idx < completedSteps // Các step có index nhỏ hơn completedSteps được coi là đã hoàn thành
      //     }))
      //   };
      // });
      setlessonList(result);
    }
  };

  // Tính toán tổng tiến độ học tập

  return (
    <ScrollView
      ref={pros.scrollviewRef}
      {...pros.refreshControlProps}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        paddingTop: 16,
      }}>
      {/* Learning Progress Component */}
      {pros.checkBuy ? (
        <LearningProgress
          progress={
            ((lessonList?.filter((item: any) => item.isCompleted === true)
              ?.length ?? 0) /
              (lessonList?.length ?? 1)) *
            100
          }
        />
      ) : null}

      {lessonList?.map((item, index) => {
        return (
          <ListTile
            key={item.Id}
            style={{paddingHorizontal: 16, padding: 0, paddingBottom: 16}}
            leading={
              <View
                style={{
                  flex: 1,
                  alignItems: 'center',
                  gap: 8,
                }}>
                <View style={{alignItems: 'center', justifyContent: 'center'}}>
                  <StepCircleProgress
                    showTitle={false}
                    size={32}
                    strokeWidth={3}
                    totalStep={5}
                    step={4}
                  />
                  <Text
                    style={{
                      position: 'absolute',
                      textAlign: 'center',
                      ...TypoSkin.label3,
                      color: ColorThemes.light.Primary_Color_Main,
                    }}>
                    {item.step}
                  </Text>
                </View>
                <View
                  style={{
                    flex: 1,
                    width: 1,
                    backgroundColor:
                      ColorThemes.light.Neutral_Border_Color_Main,
                  }}
                />
              </View>
            }
            listtileStyle={{gap: 16, alignItems: 'flex-start'}}
            title={
              <Text
                style={{
                  ...TypoSkin.heading7,
                  fontWeight: '600',
                  color: ColorThemes.light.Neutral_Text_Color_Title,
                }}>
                {item.Name}
              </Text>
            }
            trailing={
              <Text
                style={{
                  ...TypoSkin.subtitle3,
                  color:
                    item.PercentCompleted === 100
                      ? ColorThemes.light.Success_Color_Main
                      : ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}>
                {`${0}/${item.listItem?.length}`}
              </Text>
            }
            subtitle={
              item.listItem?.length == 0 ? null : (
                <View style={{flex: 1, width: '100%'}}>
                  {item.listItem?.map((item: any, index: number) => {
                    return (
                      <View
                        key={index}
                        style={{
                          flexDirection: 'row',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                          paddingVertical: 10,
                          gap: 8,
                        }}>
                        <View
                          style={[
                            {
                              width: 16,
                              borderRadius: 100,
                            },
                          ]}>
                          {item.Icon ? (
                            <Winicon src={item.Icon} size={16} />
                          ) : (
                            <Text style={[stylesDefault.inforTitle]}>*</Text>
                          )}
                        </View>
                        <Text
                          style={[
                            TypoSkin.label4,
                            {
                              color:
                                item.PercentCompleted === 100
                                  ? ColorThemes.light.Success_Color_Main
                                  : ColorThemes.light.Neutral_Text_Color_Label,
                            },
                          ]}>
                          {item.Name}
                        </Text>
                        {item.PercentCompleted === 100 && (
                          <View style={{marginLeft: 'auto'}}>
                            <Winicon
                              src="fill/user interface/check"
                              size={16}
                              color={ColorThemes.light.Success_Color_Main}
                            />
                          </View>
                        )}
                      </View>
                    );
                  })}
                </View>
              )
            }
          />
        );
      })}
      <View style={{height: 100, width: '100%'}} />
    </ScrollView>
  );
}

const stylesDefault = StyleSheet.create({
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  inforTitle: {
    fontSize: 12,
    color: '#61616B',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
});
