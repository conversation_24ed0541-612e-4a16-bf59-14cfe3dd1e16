import React, {
  JSX,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {svgPathProperties} from 'svg-path-properties';
import {
  View,
  ScrollView,
  StyleSheet,
  Animated,
  Text,
  Image,
  Dimensions,
  TouchableOpacity,
  ImageBackground,
  Platform,
} from 'react-native';
import Svg, {Path, Circle, Ellipse, Polygon} from 'react-native-svg';
import {useRoute, useNavigation} from '@react-navigation/native';
import {
  AppButton,
  ComponentStatus,
  FDialog,
  FLoading,
  showDialog,
  showSnackbar,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {useDispatch} from 'react-redux';
import {useProccessLesson} from '../../../redux/hook/proccessLessonHook';
import {LessonActions} from '../../../redux/reducers/proccessLessonReducer';
import {navigateBack, RootScreen} from '../../../router/router';
import {AppDispatch} from '../../../redux/store/store';
import ScreenHeader from '../../../Screen/Layout/header';
import {TypoSkin} from '../../../assets/skin/typography';
import {ColorThemes} from '../../../assets/skin/colors';
import {SafeAreaView} from 'react-native-safe-area-context';
import {randomGID} from '../../../utils/Utils';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {CourseDA} from '../da';
import TitleWithImage from '../../../Screen/Layout/titleWithImage';
import {useTranslation} from 'react-i18next';
import {IconSvg} from '../../../components/iconSvg';
import Masco18 from './masco18';

const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

const CONFIG = {
  STEP_VERTICAL_SPACING: 220,
  LOGICAL_STEP_SPACING: 160,
  CENTER_X: SCREEN_WIDTH / 2,
  BIRD_SIZE: 100,
  OUTER_CIRCLE_SIZE: 90,
  STEP_SIZE: 70,
  BIRD_OFFSET: 30,
  EDGE_THRESHOLD: 80,
};

interface Lesson {
  id: string;
  title: string;
  PercentCompleted: number;
  steps: Array<{
    type: string;
    displayName: string;
    order: number;
    id?: string;
    PercentCompleted: number;
    IsCurrentStep?: boolean;
  }>;
}

interface Step {
  type: string;
  displayName: string;
  icon: JSX.Element;
  stepIndex: number;
  order: number;
  progress: number;
  status: 'pending' | 'active' | 'completed';
  lessonId: string;
  id?: string;
  isCurrentStep?: boolean;
  videoIds?: string[];
}

const stepIcons: Record<string, JSX.Element> = {
  Intro: (
    <SkeletonImage
      source={require('../../../assets/images/intro.png')}
      style={{width: 70, height: 70}}
    />
  ),
  Video: (
    <SkeletonImage
      source={require('../../../assets/images/play.png')}
      style={{width: 70, height: 70}}
    />
  ),
  Quiz: <SkeletonImage source={require('../../../assets/images/quiz.png')} />,
  Exam: <SkeletonImage source={require('../../../assets/images/exam.png')} />,
  Document: (
    <SkeletonImage source={require('../../../assets/images/document.png')} />
  ),
  default: (
    <SkeletonImage source={require('../../../assets/images/quiz.png')} />
  ),
};

const useSvgPath = (totalSteps: number): string => {
  return useMemo(() => {
    if (totalSteps <= 0) return '';
    let path = `M${CONFIG.CENTER_X},50`;
    let direction = 1;
    for (
      let i = 1;
      i <
      Math.ceil(
        totalSteps *
          (CONFIG.LOGICAL_STEP_SPACING / CONFIG.STEP_VERTICAL_SPACING),
      );
      i++
    ) {
      const y = 50 + i * CONFIG.STEP_VERTICAL_SPACING;
      const controlY = y - CONFIG.STEP_VERTICAL_SPACING / 2;
      const controlX = CONFIG.CENTER_X + 200 * direction;
      path += ` Q${controlX},${controlY} ${CONFIG.CENTER_X},${y}`;
      direction *= -1;
    }
    return path;
  }, [totalSteps]);
};

interface Point {
  x: number;
  y: number;
}

const useStepPositions = (pathD: string, totalSteps: number): Point[] => {
  return useMemo(() => {
    if (!pathD || totalSteps <= 0) return [];
    try {
      const properties = new svgPathProperties(pathD);
      const totalLength = properties.getTotalLength();
      const stepLength =
        totalSteps > 1 ? totalLength / (totalSteps - 1) : totalLength;
      return Array.from({length: totalSteps}, (_, index) =>
        properties.getPointAtLength(index * stepLength),
      );
    } catch (error) {
      console.error('Error calculating step positions:', error);
      return [];
    }
  }, [pathD, totalSteps]);
};

const useBirdAnimation = (
  stepCoordinates: Point[],
  currentStep: number,
): Animated.ValueXY => {
  const birdPosition = useRef(new Animated.ValueXY()).current;

  useEffect(() => {
    if (!stepCoordinates.length || !stepCoordinates[currentStep]) return;

    const step = stepCoordinates[currentStep];
    let birdX: number;

    if (step.x >= SCREEN_WIDTH - CONFIG.EDGE_THRESHOLD - CONFIG.STEP_SIZE / 2) {
      birdX =
        step.x - CONFIG.STEP_SIZE / 2 - CONFIG.BIRD_SIZE - CONFIG.BIRD_OFFSET;
    } else if (step.x <= CONFIG.EDGE_THRESHOLD + CONFIG.STEP_SIZE / 2) {
      birdX = step.x + CONFIG.STEP_SIZE / 2 + CONFIG.BIRD_OFFSET;
    } else {
      birdX = step.x + CONFIG.STEP_SIZE / 2 + CONFIG.BIRD_OFFSET;
    }

    birdX = Math.max(0, Math.min(birdX, SCREEN_WIDTH - CONFIG.BIRD_SIZE));

    Animated.spring(birdPosition, {
      toValue: {x: birdX, y: step.y - 30},
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start();
  }, [currentStep, stepCoordinates]);

  return birdPosition;
};

interface ProgressCircleProps {
  progress: number;
  size?: number;
  outerSize?: number;
}

const ProgressCircle: React.FC<ProgressCircleProps> = React.memo(
  ({
    progress,
    size = CONFIG.STEP_SIZE,
    outerSize = CONFIG.OUTER_CIRCLE_SIZE,
  }) => {
    const strokeWidth = 5;
    const radius = (outerSize - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (progress / 100) * circumference;

    // Xác định màu dựa trên tiến độ
    const progressColor = progress >= 100 ? '#4CAF50' : '#FF9800'; // Xanh lá khi hoàn thành, cam khi đang làm
    const textColor = progress >= 100 ? '#4CAF50' : '#f57c00'; // Màu text tương ứng

    return (
      <View style={styles.progressCircleContainer}>
        <Svg width={outerSize} height={outerSize}>
          <Circle
            cx={outerSize / 2}
            cy={outerSize / 2}
            r={radius}
            stroke="#e0e0e0"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          <Circle
            cx={outerSize / 2}
            cy={outerSize / 2}
            r={radius}
            stroke={progressColor}
            strokeWidth={strokeWidth}
            strokeDasharray={`${circumference} ${circumference}`}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            fill="transparent"
            transform={`rotate(-90, ${outerSize / 2}, ${outerSize / 2})`}
          />
        </Svg>
        <Text style={[styles.progressText, {color: textColor}]}>
          {Math.round(progress)}%
        </Text>
      </View>
    );
  },
);

const ProccessCourseDetail: React.FC = () => {
  const {t} = useTranslation();
  const scrollY = useRef(new Animated.Value(0)).current;
  const scrollViewRef = useRef<ScrollView>(null);
  const [viewedStep, setViewedStep] = useState<number>(0);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [isLoading, setLoading] = useState(false);
  const [currentStepProgress, setCurrentStepProgress] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const customer = useSelectorCustomerState().data;
  const route = useRoute<any>();
  const navigation = useNavigation<any>();
  const dialogRef = useRef<any>(null);
  const AnimatedSvg = Animated.createAnimatedComponent(Svg);
  const {
    id,
    name,
    isCertificate,
    author,
    signature,
    certificateName,
    courseCustomerId,
  } = route.params;
  const dispatch: AppDispatch = useDispatch();
  const courseDA = new CourseDA();
  const {lessons, loading, error: fetchError} = useProccessLesson(id);

  useEffect(() => {
    if (loading) {
      const timeout = setTimeout(() => {
        if (loading) {
          setError(t('course.loadingTimeout'));
        }
      }, 10000);
      return () => clearTimeout(timeout);
    }
  }, [loading]);

  const updateCurrentStepFromLessons = () => {
    // Kiểm tra dữ liệu đầu vào
    if (!lessons || !Array.isArray(lessons) || lessons.length === 0) {
      console.warn('Invalid lessons data:', lessons);
      setCurrentStep(0);
      setViewedStep(0);
      setCurrentStepProgress(0);
      setError(t('course.noLessonsFound'));
      return;
    }

    try {
      // PHẦN 1: Tìm bước có IsCurrentStep = true
      let currentStepFound = false;
      let globalStepIndex = 0;
      let activeLessonIndex = 0;
      let activeStep = null;
      // Tìm step được đánh dấu là current
      for (let i = 0; i < lessons.length; i++) {
        const lesson = lessons[i];
        for (let j = 0; j < lesson.steps.length; j++) {
          const step = lesson.steps[j];
          if (step.IsCurrentStep === true) {
            activeLessonIndex = i;
            activeStep = step;
            globalStepIndex =
              lessons.slice(0, i).reduce((sum, l) => sum + l.steps.length, 0) +
              j;
            currentStepFound = true;
            break;
          }
        }
        if (currentStepFound) break;
      }

      // PHẦN 2: Nếu tìm thấy step hiện tại, sử dụng nó
      if (currentStepFound && activeStep) {
        console.log('Found IsCurrentStep = true:', {
          lessonIndex: activeLessonIndex,
          step: activeStep,
          globalStepIndex,
        });
        setCurrentStep(globalStepIndex);
        setViewedStep(globalStepIndex);
        setCurrentStepProgress(activeStep.PercentCompleted || 0);
        return;
      }

      // PHẦN 3: Nếu không tìm thấy IsCurrentStep, tìm step đầu tiên chưa hoàn thành
      for (let i = 0; i < lessons.length; i++) {
        const lesson = lessons[i];
        for (let j = 0; j < lesson.steps.length; j++) {
          const step = lesson.steps[j];
          if (step.PercentCompleted < 100) {
            globalStepIndex =
              lessons.slice(0, i).reduce((sum, l) => sum + l.steps.length, 0) +
              j;

            console.log('Found first incomplete step:', {
              lessonIndex: i,
              stepOrder: step.order,
              globalStepIndex,
              percentCompleted: step.PercentCompleted,
            });

            setCurrentStep(globalStepIndex);
            setViewedStep(globalStepIndex);
            setCurrentStepProgress(step.PercentCompleted || 0);
            return;
          }
        }
      }

      // PHẦN 4: Nếu tất cả các step đã hoàn thành, set về step đầu tiên
      console.log('All steps completed, setting to first step');
      setCurrentStep(0);
      setViewedStep(0);
      setCurrentStepProgress(100);
    } catch (err) {
      console.error('Error updating current step:', err);
      setError(t('course.failedToProcessLessonData'));
      setCurrentStep(0);
      setViewedStep(0);
      setCurrentStepProgress(0);
    }
  };

  useEffect(() => {
    console.log('useEffect triggered - Loading:', loading, 'Lessons:', lessons);
    if (!loading) {
      if (!lessons || !Array.isArray(lessons)) {
        console.warn('Invalid lessons data:', lessons);
        setError(t('course.invalidLessonsData'));
        setCurrentStep(0);
        setViewedStep(0);
        setCurrentStepProgress(0);
      } else if (lessons.length === 0) {
        console.warn('No lessons found');
        setError(t('course.noLessonsFound'));
        setCurrentStep(0);
        setViewedStep(0);
        setCurrentStepProgress(0);
      } else {
        lessons.forEach((lesson, index) => {
          console.log(`Lesson ${index}:`, {
            id: lesson.id,
            steps: lesson.steps.map((s: any) => ({
              order: s.order,
              PercentCompleted: s.PercentCompleted,
              IsCurrentStep: s.IsCurrentStep,
            })),
          });
        });
        updateCurrentStepFromLessons();
      }
    }
  }, [lessons, loading]);

  const checkCourseCompletion = useCallback(async () => {
    if (!lessons || !Array.isArray(lessons) || lessons.length === 0)
      return false;

    const allLessonsCompleted = lessons.every(lesson =>
      lesson.steps.every((step: any) => step.PercentCompleted === 100),
    );
    if (allLessonsCompleted) {
      if (!isCertificate) {
        return;
      } else {
        const cer = await courseDA.getCertificateItem(id);
        // if(!cer){
        //   const certificate = {
        //     Id: randomGID(),
        //     CourseId: id,
        //     CustomerId: customer.Id,
        //     CerticateNo:'CERT-' + Math.floor(Math.random() * 1000000),
        //     DateCreated: new Date().getTime(),
        //     CustomerName: customer.Name,
        //   };
        //   await courseDA.addCertificate(certificate);
        // }
        navigation.navigate(RootScreen.Certificate, {
          courseId: id,
          courseName: name,
          author: author,
          signature: signature,
          CerticateNo:
            cer?.CerticateNo ?? 'CERT-' + Math.floor(Math.random() * 1000000),
          customerName: cer?.CustomerName ?? customer.Name,
          dateCompleted: cer
            ? new Date(cer.DateCreated).toLocaleDateString('vi-VN')
            : new Date().toLocaleDateString('vi-VN'),
          isCertificate: cer ? true : false,
          certificateName: certificateName,
        });
      }
    } else {
      showSnackbar({
        message: t('course.notCompletedYet'),
        status: ComponentStatus.ERROR,
      });
    }
    // return allLessonsCompleted;
  }, [lessons]);

  const baseSteps = useMemo<Step[]>(() => {
    if (!lessons || !Array.isArray(lessons) || !lessons.length) return [];
    let stepIndex = 0;
    const steps: Step[] = [];

    lessons.forEach(lesson => {
      lesson.steps.forEach((stepTemplate: any) => {
        steps.push({
          type: stepTemplate.type,
          displayName: stepTemplate.displayName,
          icon: stepIcons[stepTemplate.type] || stepIcons.default,
          stepIndex: stepIndex++,
          order: stepTemplate.order,
          progress: stepTemplate.PercentCompleted || 0,
          status: 'pending',
          lessonId: String(lesson.id),
          id: stepTemplate.id,
          videoIds: stepTemplate.videoIds,
          isCurrentStep: stepTemplate.IsCurrentStep || false,
        });
      });
    });

    return steps;
  }, [lessons]);

  const allSteps = useMemo<Step[]>(() => {
    if (!baseSteps.length) return [];

    return baseSteps.map(step => {
      const lesson = lessons.find(l => String(l.id) === String(step.lessonId));
      if (!lesson) return step;
      const stepData = lesson.steps.find((s: any) => s.order === step.order);
      const progress = stepData?.PercentCompleted || 0;
      const isCurrentStep = stepData?.IsCurrentStep || false;
      let status: 'pending' | 'active' | 'completed' = 'pending';

      if (progress >= 100) {
        status = 'completed';
      } else if (isCurrentStep) {
        status = 'active';
      } else {
        const activeLesson =
          lessons.find(l =>
            l.steps.some((s: any) => s.PercentCompleted < 100),
          ) || lessons[0];
        if (activeLesson.id === step.lessonId) {
          const activeStep =
            activeLesson.steps.find((s: any) => s.PercentCompleted < 100) ||
            activeLesson.steps[0];
          if (activeStep.order === step.order && !isCurrentStep) {
            status = 'active';
          }
        }
      }

      return {
        ...step,
        progress,
        status,
        isCurrentStep,
      };
    });
  }, [baseSteps, lessons]);

  const pathD = useSvgPath(allSteps.length);
  const stepCoordinates = useStepPositions(pathD, allSteps.length);
  const birdPosition = useBirdAnimation(stepCoordinates, viewedStep);

  const contentHeight = Math.max(
    SCREEN_HEIGHT,
    allSteps.length * CONFIG.LOGICAL_STEP_SPACING + 200,
  );

  useEffect(() => {
    if (
      scrollViewRef.current &&
      stepCoordinates &&
      stepCoordinates[viewedStep] &&
      !loading
    ) {
      const stepY = stepCoordinates[viewedStep].y;
      const scrollToY = Math.max(0, stepY - SCREEN_HEIGHT / 2);
      const maxScroll = Math.max(0, contentHeight - SCREEN_HEIGHT);
      const finalScrollY = Math.min(scrollToY, maxScroll);

      setTimeout(() => {
        scrollViewRef.current?.scrollTo({y: finalScrollY, animated: true});
      }, 300);
    }
  }, [stepCoordinates, viewedStep, loading, contentHeight]);

  const getId = async (step: Step) => {
    switch (step.type) {
      case 'Intro':
      case 'Video':
      case 'Document':
        return step.lessonId;
      case 'Quiz':
      case 'Exam':
        return step.id;
      default:
        return step.lessonId;
    }
  };

  const handleStepPress = async (index: number) => {
    const targetStep = allSteps[index];
    if (!targetStep) return;
    const lessonIndex = lessons.findIndex(l => l.id === targetStep.lessonId);
    const currentLessonIndex = lessons.findIndex(l => {
      const lessonHasIncompleteStep = l.steps.some(
        (s: any) => s.PercentCompleted < 100,
      );
      return lessonHasIncompleteStep || lessons.indexOf(l) === 0;
    });

    // Kiểm tra xem bài học có được mở khóa không
    let isLessonUnlocked = lessonIndex <= currentLessonIndex;
    if (lessonIndex > currentLessonIndex) {
      isLessonUnlocked = lessons
        .slice(0, lessonIndex)
        .every(lesson =>
          lesson.steps.every((s: any) => s.PercentCompleted >= 100),
        );
    }

    if (!isLessonUnlocked) {
      console.log(
        'Bước này bị khóa! Hoàn thành tất cả các bài học trước đó để mở khóa.',
      );
      return;
    }

    const currentLesson = lessons[lessonIndex];
    const nextLessonId =
      lessonIndex + 1 < lessons.length ? lessons[lessonIndex + 1].id : null;
    const IdDetail = await getId(targetStep);
    //kiểm tra xem các step trong lesson hiện tại đã done hết chưa
    var isLastStep = false;

    isLastStep = currentLesson.steps.some(
      (s: any) => s.PercentCompleted >= 100 && s.order !== targetStep.order,
    );

    // Dispatch action SET_CURRENTSTEP
    dispatch(
      LessonActions.setCurrentStep({
        lessonId: currentLesson.id,
        stepOrder: targetStep.order,
        courseId: id,
      }),
    );
    const navigationData = {
      lessonId: currentLesson.id,
      lessonIndex: lessonIndex,
      courseId: id,
      stepId: targetStep.id,
      stepOrder: targetStep.order,
      stepType: targetStep.type,
      nextLessonId,
      isLastStep: isLastStep,
      maxStepLessonCurrent: currentLesson.steps.length,
      courseCustomerId: courseCustomerId,
      videoIds: targetStep.videoIds,
    };
    switch (targetStep.type) {
      case 'Intro':
        navigation.push(RootScreen.IntroductionLesson, {
          Step: navigationData,
          id: IdDetail,
          name: targetStep.displayName,
        });
        break;
      case 'Video':
        navigation.push(RootScreen.LearnCourse, {
          Step: navigationData,
          id: IdDetail,
        });
        break;
      case 'Quiz':
        navigation.push(RootScreen.Test, {Step: navigationData, id: IdDetail});
        break;
      case 'Exam':
        navigation.push(RootScreen.Test, {Step: navigationData, id: IdDetail});
        break;
      case 'Document':
        navigation.push(RootScreen.LearnCourse, {
          Step: navigationData,
          id: IdDetail,
          type: 'Document',
        });
        break;
      default:
        console.log(`Không tìm thấy màn hình cho bước: ${targetStep.type}`);
    }
  };

  const handleScroll = Animated.event(
    [{nativeEvent: {contentOffset: {y: scrollY}}}],
    {useNativeDriver: false},
  );

  const LoadingView = () => {
    if (loading) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Loading lessons...</Text>
        </View>
      );
    }

    if (fetchError) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error: {fetchError}</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      );
    }

    if (!stepCoordinates.length || !allSteps.length) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            No steps available. Please try again.
          </Text>
        </View>
      );
    }
  };

  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FDialog ref={dialogRef} />
      <ScreenHeader
        // title={`${name ?? 'Tiến trình học'}`}
        style={{paddingTop: Platform.OS === 'ios' ? 0 : 24}}
        title={
          <View style={{flex: 1, alignItems: 'center'}}>
            <Text
              style={[TypoSkin.title3, styles.title]}
              numberOfLines={1}
              ellipsizeMode="tail">
              {name ?? 'Tiến trình học'}
            </Text>
            <Text
              style={{
                ...TypoSkin.subtitle4,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                textAlign: 'center',
                marginTop: 30,
              }}>
              {`${currentStep + 1}/${allSteps.length ?? 0}`}
            </Text>
          </View>
        }
        action={
          <View style={{flexDirection: 'row', gap: 8}}>
            {isCertificate ? (
              <AppButton
                title={
                  <Winicon
                    src={'outline/user interface/verified'}
                    size={18}
                    color={ColorThemes.light.Neutral_Text_Color_Title}
                  />
                }
                containerStyle={{
                  justifyContent: 'flex-start',
                  alignSelf: 'baseline',
                }}
                backgroundColor={'transparent'}
                textStyle={TypoSkin.buttonText3}
                borderColor="transparent"
                onPress={() => {
                  checkCourseCompletion();
                }}
                textColor={ColorThemes.light.Info_Color_Main}
              />
            ) : (
              <></>
            )}
            <AppButton
              title={
                <Winicon
                  src={'outline/travel/c-info'}
                  size={18}
                  color={ColorThemes.light.Neutral_Text_Color_Title}
                />
              }
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'baseline',
                paddingRight: 8,
              }}
              backgroundColor={'transparent'}
              textStyle={TypoSkin.buttonText3}
              borderColor="transparent"
              onPress={() => {
                navigation.push(RootScreen.CourseDetail, {id: id, check: true});
              }}
              textColor={ColorThemes.light.Info_Color_Main}
            />
          </View>
        }
        backIcon={<Winicon src="outline/arrows/left-arrow" size={20} />}
        onBack={() => {
          navigation.goBack();
        }}
      />
      {loading ? (
        <LoadingView />
      ) : (
        <ImageBackground
          source={require('../../../assets/images/bg_roadmap1.png')}
          style={styles.container}
          resizeMode="cover">
          <Animated.ScrollView
            ref={scrollViewRef}
            style={styles.container}
            contentContainerStyle={{height: contentHeight}}
            scrollEventThrottle={16}
            onScroll={handleScroll}
            showsVerticalScrollIndicator={false}>
            <Svg
              height={contentHeight}
              width={SCREEN_WIDTH}
              style={styles.svgContainer}>
              <Path d={pathD} stroke="#b2e4f2" strokeWidth="40" fill="none" />
            </Svg>

            {allSteps.map((step, index) => {
              const point = stepCoordinates[index];
              if (!point) return null;

              const isActive = index === viewedStep;
              const isCompleted = step.status === 'completed';
              const progress = step.progress;
              const currentLessonIndex = lessons.findIndex(l => {
                const lessonHasIncompleteStep = l.steps.some(
                  (s: any) => s.PercentCompleted < 100,
                );
                return lessonHasIncompleteStep || lessons.indexOf(l) === 0;
              });
              const stepLessonIndex = lessons.findIndex(
                l => String(l.id) === String(step.lessonId),
              );
              const isDisabled = lessons
                .slice(0, stepLessonIndex)
                .some(lesson =>
                  lesson.steps.some((s: any) => s.PercentCompleted < 100),
                );

              const normalTop = point.y - CONFIG.STEP_SIZE / 2;
              const normalLeft = point.x - CONFIG.STEP_SIZE / 2;
              const circleTop =
                normalTop - (CONFIG.OUTER_CIRCLE_SIZE - CONFIG.STEP_SIZE) / 2;
              const circleLeft =
                normalLeft - (CONFIG.OUTER_CIRCLE_SIZE - CONFIG.STEP_SIZE) / 2;

              // Kiểm tra xem step này có phải là step đầu tiên của lesson không
              // Tìm tất cả các step cùng lesson và kiểm tra xem step hiện tại có order nhỏ nhất không
              const lessonSteps = allSteps.filter(
                s => s.lessonId === step.lessonId,
              );
              const isFirstStepOfLesson =
                lessonSteps.length > 0 &&
                Math.min(...lessonSteps.map(s => s.order)) === step.order;

              // Tìm tên của lesson tương ứng
              const lessonTitle = isFirstStepOfLesson
                ? lessons.find(l => String(l.id) === String(step.lessonId))
                    ?.title
                : null;

              return (
                <View key={index}>
                  {isActive && (
                    <View
                      style={[
                        styles.progressCircleWrapper,
                        {top: circleTop, left: circleLeft},
                      ]}>
                      <ProgressCircle progress={progress} />
                    </View>
                  )}
                  <TouchableOpacity
                    style={[styles.step, {top: normalTop, left: normalLeft}]}
                    onPress={() => handleStepPress(index)}
                    disabled={isDisabled}>
                    <View
                      style={[
                        styles.iconWrapper,
                        isActive && styles.activeIcon,
                        isCompleted && styles.completedIcon,
                        isDisabled && styles.disabledIcon,
                      ]}>
                      {step.icon}
                    </View>

                    {/* Hiển thị tên lesson nếu là step đầu tiên của lesson */}
                    {isFirstStepOfLesson && lessonTitle && (
                      <View style={styles.lessonTitleContainer}>
                        <Text style={styles.lessonTitle}>{lessonTitle}</Text>
                      </View>
                    )}
                  </TouchableOpacity>
                </View>
              );
            })}

            {/* <Animated.Image
              source={require('../../../assets/images/bird.png')}
              style={[
                styles.bird,
                {
                  transform: [
                    {translateX: birdPosition.x},
                    {translateY: birdPosition.y},
                  ],
                },
              ]}
              resizeMode="contain"
            /> */}
            <AnimatedSvg
              width={120}
              height={120}
              viewBox="0 0 228.74 196.08"
              style={[
                styles.bird,
                {
                  transform: [
                    {translateX: birdPosition.x},
                    {translateY: birdPosition.y},
                  ],
                },
              ]}>
              {/* Bird Body - Main blue parts */}
              <Masco18 />
            </AnimatedSvg>
          </Animated.ScrollView>
        </ImageBackground>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 8,
    backgroundColor: 'transparent',
  },
  svgContainer: {
    position: 'absolute',
    left: 0,
  },
  step: {
    position: 'absolute',
    alignItems: 'center',
    zIndex: 2,
  },
  progressCircleWrapper: {
    position: 'absolute',
    zIndex: 1,
  },
  progressCircleContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconWrapper: {
    width: CONFIG.STEP_SIZE,
    height: CONFIG.STEP_SIZE,
    borderRadius: CONFIG.STEP_SIZE / 2,
    backgroundColor: '#1DB2F6',
    justifyContent: 'center',
    alignItems: 'center',
    // elevation: 5,
    // shadowColor: '#000',
    // shadowOffset: {width: 0, height: 2},
    // shadowOpacity: 0.25,
    // shadowRadius: 3.84,
    overflow: 'hidden',
  },
  activeIcon: {
    backgroundColor: '#ffa726',
    zIndex: 3,
  },
  completedIcon: {
    backgroundColor: '#4CAF50',
  },
  disabledIcon: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Disable,
  },
  progressText: {
    fontSize: 12,
    color: '#f57c00',
    position: 'absolute',
    fontWeight: 'bold',
  },
  bird: {
    position: 'absolute',
    width: CONFIG.BIRD_SIZE,
    height: CONFIG.BIRD_SIZE,
    zIndex: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e3f6fd',
  },
  errorText: {
    fontSize: 16,
    color: '#d32f2f',
    textAlign: 'center',
  },
  title: {
    position: 'absolute',
    left: '12%',
    right: '12%',
    textAlign: 'center',
    color: ColorThemes.light.Neutral_Text_Color_Title,
    // top: 12
  },
  lessonTitleContainer: {
    position: 'absolute',
    top: CONFIG.STEP_SIZE + 5,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
    maxWidth: 250,
  },
  lessonTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: ColorThemes.light.Primary_Color_Main,
    textAlign: 'center',
  },
});

export default ProccessCourseDetail;
