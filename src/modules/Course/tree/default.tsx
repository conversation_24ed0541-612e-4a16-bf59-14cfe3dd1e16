import React, {useState} from 'react';
import {View, Text, FlatList, TouchableOpacity} from 'react-native';
import {ListTile, Winicon, Checkbox} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

export const ListTree = () => {
  const data = [
    {Id: '1', Name: 'Item 1', ParentId: null},
    {Id: '2', Name: 'Item 2', ParentId: null},
    {Id: '3', Name: 'Item 3', ParentId: null},
    {Id: '4', Name: 'Item 4', ParentId: null},
    {Id: '5', Name: 'Item 5', ParentId: '1'},
    {Id: '6', Name: 'Item 5', ParentId: '2'},
    {Id: '7', Name: 'Item 5', ParentId: '3'},
    {Id: '8', Name: 'Item 5', ParentId: '1'},
    {Id: '9', Name: 'Item 5', ParentId: '2'},
    {Id: '10', Name: 'Item 5', ParentId: '2'},
  ];

  const [selected, setSelected] = useState<Array<any>>([]);

  return (
    <View style={{flex: 1}}>
      <FlatList
        data={data.filter((e: any) => !e.ParentId)}
        renderItem={({item, index}) => {
          const children = data.filter((e: any) => e.ParentId === item.Id);
          return (
            <CateTile
              item={item}
              children={children}
              setSelected={setSelected}
              selected={selected}
            />
          );
        }}
        style={{width: '100%'}}
        keyExtractor={(item, index) => index.toString()}
        ListFooterComponent={() => <View style={{height: 100}} />}
      />
    </View>
  );
};

const CateTile = ({
  item,
  children,
  selected,
  setSelected,
}: {
  item: any;
  children?: Array<any>;
  selected: Array<any>;
  setSelected: any;
}) => {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <ListTile
      key={item.Id}
      onPress={
        item.ParentId
          ? undefined
          : () => {
              setIsOpen(!isOpen);
            }
      }
      leading={
        <View style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
          <Winicon
            src={
              isOpen
                ? 'fill/arrows/triangle-down'
                : 'fill/arrows/triangle-right'
            }
            size={16}
          />
        </View>
      }
      title={
        <TouchableOpacity
          onPress={() => {
            if (children) {
              if (!selected.includes(item.Id)) {
                setSelected([
                  ...selected,
                  item.Id,
                  ...children
                    .filter(e => !selected.includes(e.Id))
                    .map(e => e.Id),
                ]);
              } else {
                setSelected([
                  selected.filter(
                    id => id !== item.Id && !children.some(e => e.Id === id),
                  ),
                ]);
              }
            }
          }}
          style={{
            gap: 8,
            flexDirection: 'row',
            alignSelf: 'baseline',
            alignItems: 'center',
          }}>
          <Checkbox
            value={
              selected.includes(item?.Id) ||
              (children?.every(e => selected.includes(e.Id)) &&
                children?.length)
                ? true
                : false
            }
            onChange={(v: any) => {
              if (children) {
                if (v) {
                  setSelected([
                    ...selected,
                    item.Id,
                    ...children
                      .filter(e => !selected.includes(e.Id))
                      .map(e => e.Id),
                  ]);
                } else {
                  setSelected([
                    selected.filter(
                      id => id !== item.Id && !children.some(e => e.Id === id),
                    ),
                  ]);
                }
              }
            }}
          />
          <Text
            style={[
              TypoSkin.heading7,
              {color: ColorThemes.light.Neutral_Text_Color_Title},
            ]}>
            {item?.Name ?? '-'}
          </Text>
        </TouchableOpacity>
      }
      listtileStyle={{gap: 8}}
      style={{
        padding: 0,
        paddingVertical: 8,
        paddingLeft: item.ParentId ? 32 : 0,
      }}
      bottom={
        <View
          style={{
            marginTop: 8,
            alignContent: 'flex-start',
            width: '100%',
          }}>
          {isOpen
            ? children?.map((e: any) => {
                console.log(e);

                return (
                  <ListTile
                    key={e.Id}
                    title={
                      <TouchableOpacity
                        onPress={() => {
                          if (!selected.includes(e.Id))
                            setSelected([...selected, e.Id]);
                          else
                            setSelected(
                              selected.filter((id: any) => id !== e.Id),
                            );
                        }}
                        style={{
                          gap: 8,
                          flexDirection: 'row',
                          alignSelf: 'baseline',
                          alignItems: 'center',
                        }}>
                        <Checkbox
                          value={selected.includes(e.Id)}
                          onChange={(v: any) => {
                            if (v) setSelected([...selected, e.Id]);
                            else
                              setSelected(
                                selected.filter((id: any) => id !== e.Id),
                              );
                          }}
                        />
                        <Text
                          style={[
                            TypoSkin.heading7,
                            {color: ColorThemes.light.Neutral_Text_Color_Title},
                          ]}>{`${e?.Name ?? '-'} (${e?._count ?? 0})`}</Text>
                      </TouchableOpacity>
                    }
                    listtileStyle={{gap: 8}}
                    style={{
                      padding: 0,
                      paddingVertical: 8,
                      paddingLeft: 32,
                    }}
                  />
                );
              })
            : null}
        </View>
      }
    />
  );
};
