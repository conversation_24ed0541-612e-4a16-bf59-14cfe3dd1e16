import {Image, ScrollView, StyleSheet, Text, View} from 'react-native';
import ScreenHeader from '../../../Screen/Layout/header';
import {
  AppButton,
  ComponentStatus,
  FBottomSheet,
  FLoading,
  ListTile,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {navigateBack} from '../../../router/router';
import {ColorThemes} from '../../../assets/skin/colors';
import {useRoute} from '@react-navigation/native';
import {useEffect, useRef, useState} from 'react';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import {VideoPlayer} from '../../../utils/VideoPlayer';
import {LessonActions} from '../../../redux/reducers/proccessLessonReducer';
import {CourseDA} from '../da';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import {SafeAreaView} from 'react-native-safe-area-context';
import VideoPlayerWithFullscreen from '../components/VideoPlayerWithFullscreen';
import {BaseDA} from '../../../base/BaseDA';
import {useTranslation} from 'react-i18next';
// import VideoPlayer, {type VideoPlayerRef} from 'react-native-video-player';

export default function IntroductionLesson() {
  const {t} = useTranslation();
  const [loading, setLoading] = useState(true);
  const route = useRoute<any>();
  const {Step, name} = route.params;
  const [time, setTime] = useState(0);
  const [lessonData, setLessonData] = useState<any>();
  const dispatch: AppDispatch = useDispatch();
  const courseDA = new CourseDA();
  const [video, setVideo] = useState<any>();

  useEffect(() => {
    const getVideo = async () => {
      const rs = await BaseDA.getFilesInfor([lessonData?.Introduction]);
      if (rs) {
        setVideo(rs.data[0]);
      }
    };
    getVideo();
  }, [lessonData]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    fetchData();
  }, [Step]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // Fetch lesson data
      const lessonResult = await courseDA.getLessonDetail(Step.lessonId);
      if (lessonResult && lessonResult.code === 200) {
        setLessonData(lessonResult.data);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const dispatchedRef = useRef<Set<number>>(new Set());
  const handleVideoProgress = (percent: number) => {
    // Xử lý các mốc tiến độ
    const MILESTONES = [25, 50, 75, 90, 100];
    for (let milestone of MILESTONES) {
      if (percent >= milestone && !dispatchedRef.current.has(milestone)) {
        dispatchedRef.current.add(milestone);
        dispatch(
          LessonActions.updateProcess({
            Id: Step.lessonId,
            CourseId: Step.courseId,
            stepId: Step.stepId,
            stepOrder: Step.stepOrder,
            PercentCompleted: percent >= 90 ? 100 : milestone,
            isLastStep: Step.isLastStep,
            lessonIndex: Step.lessonIndex,
            courseCustomerId: Step.courseCustomerId,
            Type: 'Intro',
          }),
        );
      }
    }
  };
  const bottomSheetRef = useRef<any>(null);
  return (
    <ScrollView style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />
      <SafeAreaView edges={['top']} />

      {loading ? (
        <View style={styles.loadingContainer}>
          <FLoading
            visible={true}
            avt={require('../../../assets/appstore.png')}
          />
        </View>
      ) : (
        <View style={styles.videoContainer}>
          <AppButton
            prefixIcon={'outline/user interface/e-remove'}
            prefixIconSize={20}
            backgroundColor={
              ColorThemes.light.Neutral_Background_Color_Absolute
            }
            textColor={ColorThemes.light.Neutral_Text_Color_Title}
            borderColor="transparent"
            containerStyle={{
              position: 'absolute',
              top: 16,
              left: 16,
              zIndex: 111,
              paddingHorizontal: 12,
              borderRadius: 100,
              width: 40,
              height: 40,
            }}
            onPress={navigateBack}
          />
          {video?.Url ? (
            <VideoPlayerWithFullscreen
              key={video?.Url}
              source={ConfigAPI.url.replace('/api/', '') + video?.Url}
              onProgressPercent={handleVideoProgress}
              onLoad={time => {
                // Lưu thời lượng video
                console.log('Video loaded, duration:', time);
                setTime(time);
              }}
            />
          ) : (
            <View style={styles.placeholderContainer}>
              <Image
                style={styles.placeholderImage}
                source={require('../../../assets/appstore.png')}
              />
              <Text style={{...TypoSkin.title3}}>{t('course.noVideo')}</Text>
            </View>
          )}
        </View>
      )}
      {lessonData && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{name ?? ''}</Text>
          <View style={styles.lessonInfo}>
            <ListTile
              leading={<Winicon src="fill/technology/video-player" size={24} />}
              title={lessonData.Name || t('course.noTitle')}
              // subtitle={t('course.durationMinutes', {minutes: lessonData.Hours || '0'})}
              listtileStyle={styles.listTile}
              leadingContainer={styles.leadingContainer}
            />
          </View>
          {lessonData.Introduction && (
            <View style={styles.introSection}>
              <Text style={styles.introContent}>
                {lessonData.Description || t('course.noDescription')}
              </Text>
            </View>
          )}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    zIndex: 1,
    position: 'absolute',
  },
  loadingContainer: {
    height: 200,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoContainer: {
    height: 'auto',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  tabIndicator: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    height: 1.5,
  },
  tabStyle: {
    paddingHorizontal: 4,
    paddingTop: 0,
  },
  tabBar: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    height: 45,
    elevation: 0,
  },
  placeholderContainer: {
    height: 200,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },

  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
  section: {
    marginBottom: 24,
    padding: 16,
  },
  sectionTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 12,
  },
  lessonInfo: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    overflow: 'hidden',
  },
  courseInfo: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    overflow: 'hidden',
  },
  listTile: {
    padding: 12,
    gap: 16, // Add gap between elements
  },
  leadingContainer: {
    // marginRight: 12, // Add extra space between icon and text
  },
  introSection: {
    marginTop: 16,
    padding: 12,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  introTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 8,
  },
  introContent: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
});
