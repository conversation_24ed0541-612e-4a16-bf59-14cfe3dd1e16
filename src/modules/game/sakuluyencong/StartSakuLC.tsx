import {
  ImageBackground,
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
} from 'react-native';
import HeadGame from '../components/HeadGame';
import LineProgressBar from '../components/LineProgressBar';
import Lives from '../components/Lives';
import CountBadge from '../components/CountQuestions';
import {useEffect, useRef, useState} from 'react';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import {PanGestureHandler} from 'react-native-gesture-handler';
import {runOnJS} from 'react-native-reanimated';
import {Dimensions} from 'react-native';
import {BottomGame} from '../components/BottomGame';
import {SafeAreaView} from 'react-native-safe-area-context';
import {RootState} from '../../../redux/store/store';
import {useSelector} from 'react-redux';
import {Word} from '../../../redux/reducers/game/sakuLCReducer';
import GameOverModal from '../components/GameOverModel';
import {useGameHook} from '../../../redux/hook/gameHook';
import {useSakuLCHook} from '../../../redux/hook/game/sakuLCHook';
import React from 'react';

const StartSakuLC = () => {
  const {isGameOver, messageGameOver, totalLives, currentLives} = useSelector(
    (state: RootState) => state.gameStore,
  );
  const {availableWords, questionDone, totalQuestion, dropZoneLayout} =
    useSelector((state: RootState) => state.SakuLC);

  const [wordsInDropZone, setWordsInDropZone] = useState<Word[]>([]);
  const [isError, setIsError] = useState(false);

  const sakuLCHook = useSakuLCHook();
  const gameHook = useGameHook();

  const refDropZone = useRef<View>(null);

  useEffect(() => {
    restartGame();
    findPositionDropZone();
  }, []);

  useEffect(() => {
    if (currentLives < 1) {
      gameOver('Thất bại rồi, làm lại nào');
    }
  }, [currentLives]);

  const restartGame = () => {
    setIsError(false);
    setWordsInDropZone([]);
    sakuLCHook.startGame();
    gameHook.resetGame();
  };

  const gameOver = (message: string) => {
    gameHook.gameOver(message);
  };

  // xác định vị trí drop zone
  const findPositionDropZone = () => {
    refDropZone?.current?.measureInWindow((x, y, width, height) => {
      sakuLCHook.setData({
        stateName: 'dropZoneLayout',
        value: {x, y: y + 40, width, height},
      });
    });
  };

  // Kiểm tra đáp án
  const checkAnswer = () => {
    const sentence = wordsInDropZone
      .sort((a, b) => a.position - b.position)
      .map(w => w.text)
      .join(' ');
    if (sentence !== "I eat breakfast at 7 o'clock") {
      gameHook.setData({
        stateName: 'currentLives',
        value: currentLives - 1,
      });
      setIsError(true);
    } else {
      setIsError(false);
      gameHook.setData({stateName: 'isRunTime', value: false});
      Alert.alert('Câu trả lời đúng');
    }
  };

  // Xoá text khỏi drop zone
  const removeWordFromDropZone = (word: Word) => {
    sakuLCHook.setData({
      stateName: 'availableWords',
      value: [...availableWords, word],
    });
    setWordsInDropZone(prev => prev.filter(w => w.id !== word.id));
    // xác định lại vị trí drop zone
    findPositionDropZone();
  };

  // Xoá text khỏi available words
  const removeWordFromAvailableWords = (word: Word) => {
    sakuLCHook.setData({
      stateName: 'availableWords',
      value: availableWords.filter(w => w.id !== word.id),
    });
  };

  // Render text trong drop zone
  const renderWordsInDropZone = () => {
    return wordsInDropZone.map(word => (
      <TouchableOpacity
        key={word.id}
        style={styles.wordContainer}
        onPress={() => removeWordFromDropZone(word)}>
        <Text style={styles.wordText}>{word.text}</Text>
      </TouchableOpacity>
    ));
  };

  // Render text đáp án ở dưới drop zone
  const DraggableWord = ({word}: {word: Word}) => {
    const translateX = useSharedValue(0);
    const translateY = useSharedValue(0);
    const scale = useSharedValue(1);
    const zIndex = useSharedValue(0);

    const addWordToDropZone = (wordToAdd: Word) => {
      setWordsInDropZone(prev => {
        if (!prev.find(w => w.id === wordToAdd.id)) {
          removeWordFromAvailableWords(wordToAdd);
          return [...prev, wordToAdd];
        }
        return prev;
      });
      // xác định lại vị trí drop zone
      findPositionDropZone();
    };

    const gestureHandler = useAnimatedGestureHandler({
      onStart: () => {
        scale.value = withSpring(1.1);
        zIndex.value = 1000;
      },
      onActive: event => {
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      },
      onEnd: event => {
        const wordY = event.absoluteY;
        const wordX = event.absoluteX;

        const dropZoneX = dropZoneLayout.x;
        const dropZoneY = dropZoneLayout.y;
        const dropZoneWidth = dropZoneX + dropZoneLayout.width;
        const dropZoneHeight = dropZoneY + dropZoneLayout.height;

        // Check if word is dropped in drop zone
        if (
          wordY >= dropZoneY &&
          wordY <= dropZoneHeight &&
          wordX >= dropZoneX &&
          wordX <= dropZoneWidth
        ) {
          runOnJS(addWordToDropZone)(word);
        }

        // Reset position and scale
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        scale.value = withSpring(1);
        zIndex.value = 0;
      },
    });

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [
        {translateX: translateX.value},
        {translateY: translateY.value},
        {scale: scale.value},
      ],
      zIndex: zIndex.value,
    }));

    // Don't render if word is already in drop zone
    if (wordsInDropZone.find(w => w.id === word.id)) {
      return null;
    }

    return (
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={[styles.wordContainer, animatedStyle]}>
          <Text style={styles.wordText}>{word.text}</Text>
        </Animated.View>
      </PanGestureHandler>
    );
  };

  return (
    <SafeAreaView style={{flex: 1}}>
      <ImageBackground
        style={{flex: 1}}
        source={require('./assets/background.png')}
        resizeMode="cover">
        <View style={{flex: 1, marginVertical: 16, marginHorizontal: 12}}>
          {/* Header */}
          <HeadGame timeOut={() => gameOver('Hết giờ rồi, làm lại nào')} />
          <LineProgressBar
            progress={(questionDone / totalQuestion) * 100}></LineProgressBar>
          <View
            style={{
              width: '100%',
              marginBottom: 16,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}>
            <Lives totalLives={totalLives} currentLives={currentLives}></Lives>
            <CountBadge
              current={questionDone}
              total={totalQuestion}></CountBadge>
          </View>
          {/* Title question */}
          <View style={styles.header}>
            <View style={styles.instruction}>
              <Text style={styles.wordText}>🔊 Tôi ăn sáng lúc 7 giờ</Text>
            </View>
          </View>

          {/* Drop Zone */}
          <View style={styles.dropZone} ref={refDropZone}>
            <View style={styles.dropZoneContent}>
              {renderWordsInDropZone()}
            </View>
            {wordsInDropZone.length === 0 && (
              <Text style={styles.dropZoneHint}>Kéo các từ vào đây</Text>
            )}
            {isError && (
              <View
                style={{
                  alignItems: 'flex-start',
                  justifyContent: 'flex-end',
                }}>
                <Text style={styles.errorText}>
                  Đáp án sai rồi, hãy thử lại
                </Text>
              </View>
            )}
          </View>
          {/* Check Answer Button */}
          <TouchableOpacity style={styles.checkButton} onPress={checkAnswer}>
            <Text style={styles.checkButtonText}>Kiểm tra đáp án</Text>
          </TouchableOpacity>

          {/* Available Words */}
          <View style={styles.wordsContainer}>
            {React.useMemo(() => {
              return availableWords.map((word: Word) => (
                <DraggableWord key={word.id} word={word} />
              ));
            }, [availableWords])}
          </View>
          {/* Control Buttons */}
          <View style={{position: 'absolute', bottom: 0, left: 0}}>
            <BottomGame
              resetGame={restartGame}
              backGame={() => {}}
              pauseGame={() => {}}
              volumeGame={() => {}}
            />
          </View>
        </View>
      </ImageBackground>
      <View style={{zIndex: 1000}}>
        <GameOverModal
          visible={isGameOver}
          onClose={() => {}}
          restartGame={restartGame}
          message={messageGameOver}
          isTimeOut={false}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  gameInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 15,
  },
  lives: {
    fontSize: 16,
  },
  score: {
    backgroundColor: '#4CAF50',
    color: 'white',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontSize: 12,
    fontWeight: 'bold',
  },
  instruction: {
    backgroundColor: '#FCF8E8',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 12,
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
  dropZone: {
    backgroundColor: 'white',
    borderRadius: 10,
    minHeight: 80,
    marginBottom: 20,
    padding: 15,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  dropZoneContent: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
  },
  dropZoneHint: {
    color: '#999',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
    marginTop: 10,
  },
  checkButton: {
    backgroundColor: '#D32F2F',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    alignSelf: 'center',
    marginBottom: 30,
  },
  checkButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  wordsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 30,
  },
  wordContainer: {
    margin: 6,
    backgroundColor: '#FCF8E8',
    borderRadius: 10,
    shadowColor: '#32325D',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wordText: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#112164',
  },
  controlButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  controlButton: {
    backgroundColor: '#FF6B35',
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonText: {
    fontSize: 20,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    fontSize: 14,
    fontStyle: 'italic',
  },
});

export default StartSakuLC;
