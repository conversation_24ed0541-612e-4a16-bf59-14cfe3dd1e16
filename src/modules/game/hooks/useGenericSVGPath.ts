import { useState, useEffect, useMemo } from 'react';
import { Dimensions } from 'react-native';
import { getGamePathConfig, generateGamePath } from '../config/GamePathConfig';
import { SVGPathCalculator } from '../ailatrieuphu/utils/svgPathCalculator';

interface ContainerDimensions {
  width: number;
  height: number;
}

interface MilestonePosition {
  id: number;
  name: string;
  x: number;
  y: number;
  percentageX: number;
  percentageY: number;
  angle: number;
  pathProgress: number;
}

interface PathPoint {
  x: number;
  y: number;
}

// Generic SVG Path Hook for any game
export const useGenericSVGPath = (
  gameId: string,
  containerDimensions?: ContainerDimensions
) => {
  const [screenDimensions, setScreenDimensions] = useState(Dimensions.get('window'));
  const [pathCalculator, setPathCalculator] = useState<SVGPathCalculator | null>(null);

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenDimensions(window);
    });

    return () => subscription?.remove();
  }, []);

  // Initialize path calculator for the specific game
  useEffect(() => {
    try {
      const pathData = generateGamePath(gameId);
      const calculator = new SVGPathCalculator(pathData);
      setPathCalculator(calculator);
    } catch (error) {
      console.error(`Error initializing path calculator for game ${gameId}:`, error);
      setPathCalculator(null);
    }
  }, [gameId]);

  // Use container dimensions or screen dimensions
  const dimensions = useMemo(() => {
    return containerDimensions || screenDimensions;
  }, [containerDimensions, screenDimensions]);

  // Calculate milestone positions for the specific game
  const milestonePositions = useMemo(() => {
    try {
      const gameConfig = getGamePathConfig(gameId);
      
      return gameConfig.milestonePositions.map((milestone, index) => {
        const x = milestone.left * dimensions.width;
        const y = milestone.top * dimensions.height;

        // Calculate angle based on next milestone
        const nextMilestone = gameConfig.milestonePositions.find(m => m.id === milestone.id + 1);
        let angle = 0;
        if (nextMilestone) {
          const deltaX = (nextMilestone.left - milestone.left) * dimensions.width;
          const deltaY = (nextMilestone.top - milestone.top) * dimensions.height;
          angle = Math.atan2(deltaY, deltaX);
        }

        return {
          id: milestone.id,
          name: milestone.levelName || `Chặng ${milestone.id}`,
          x: x,
          y: y,
          percentageX: milestone.left,
          percentageY: milestone.top,
          angle: angle,
          pathProgress: index / (gameConfig.milestonePositions.length - 1),
        };
      });
    } catch (error) {
      console.error(`Error calculating milestone positions for game ${gameId}:`, error);
      return [];
    }
  }, [gameId, dimensions.width, dimensions.height]);

  // Calculate path trail for the specific game
  const pathTrail = useMemo(() => {
    if (!pathCalculator) return [];

    try {
      const points = pathCalculator.getEvenlySpacedPoints(100);
      return points.map(point => ({
        x: point.x * dimensions.width,
        y: point.y * dimensions.height,
      }));
    } catch (error) {
      console.warn(`Error generating path trail for game ${gameId}, using fallback:`, error);
      
      // Fallback: create simple trail from milestone positions
      const gameConfig = getGamePathConfig(gameId);
      const trail: PathPoint[] = [];
      for (let i = 0; i < gameConfig.milestonePositions.length - 1; i++) {
        const start = gameConfig.milestonePositions[i];
        const end = gameConfig.milestonePositions[i + 1];

        // Create intermediate points between milestones
        const segmentsPerMilestone = 10;
        for (let j = 0; j <= segmentsPerMilestone; j++) {
          const t = j / segmentsPerMilestone;
          const x = (start.left + (end.left - start.left) * t) * dimensions.width;
          const y = (start.top + (end.top - start.top) * t) * dimensions.height;
          trail.push({ x, y });
        }
      }

      return trail;
    }
  }, [pathCalculator, gameId, dimensions.width, dimensions.height]);

  // Get bird animation position between two milestones
  const getBirdAnimationPosition = (
    fromMilestoneId: number,
    toMilestoneId: number,
    progress: number
  ) => {
    if (!pathCalculator) return { x: 0, y: 0, angle: 0 };

    try {
      const fromMilestone = milestonePositions.find(m => m.id === fromMilestoneId);
      const toMilestone = milestonePositions.find(m => m.id === toMilestoneId);

      if (!fromMilestone || !toMilestone) {
        return { x: 0, y: 0, angle: 0 };
      }

      const startProgress = fromMilestone.pathProgress;
      const endProgress = toMilestone.pathProgress;
      const currentProgress = startProgress + (endProgress - startProgress) * progress;

      const point = pathCalculator.getPointAtPercentage(currentProgress);
      const angle = pathCalculator.getAngleAtPercentage(currentProgress);

      return {
        x: point.x * dimensions.width,
        y: point.y * dimensions.height,
        angle: angle,
      };
    } catch (error) {
      console.error(`Error calculating bird position for game ${gameId}:`, error);
      return { x: 0, y: 0, angle: 0 };
    }
  };

  // Utility functions
  const getMilestoneById = (id: number): MilestonePosition | undefined => {
    return milestonePositions.find(m => m.id === id);
  };

  const getNextMilestone = (currentId: number): MilestonePosition | undefined => {
    return milestonePositions.find(m => m.id === currentId + 1);
  };

  const getPreviousMilestone = (currentId: number): MilestonePosition | undefined => {
    return milestonePositions.find(m => m.id === currentId - 1);
  };

  return {
    milestonePositions,
    pathTrail,
    dimensions,
    getBirdAnimationPosition,
    getMilestoneById,
    getNextMilestone,
    getPreviousMilestone,
    gameConfig: getGamePathConfig(gameId),
  };
};

// Hook for path trail animation
export const useGenericPathTrail = (
  gameId: string,
  completedMilestones: number[],
  containerDimensions?: ContainerDimensions
) => {
  const { milestonePositions, pathTrail } = useGenericSVGPath(gameId, containerDimensions);

  const completedTrail = useMemo(() => {
    if (completedMilestones.length === 0) return [];

    const maxCompletedId = Math.max(...completedMilestones);
    const maxMilestone = milestonePositions.find(m => m.id === maxCompletedId);
    
    if (!maxMilestone) return [];

    // Get trail portion from start to completed milestone
    const maxProgress = maxMilestone.pathProgress;
    const totalPoints = pathTrail.length;
    const completedPoints = Math.floor(totalPoints * maxProgress);

    return pathTrail.slice(0, completedPoints + 1);
  }, [completedMilestones, milestonePositions, pathTrail]);

  return {
    completedTrail,
    fullTrail: pathTrail,
  };
};

// Hook for responsive scaling
export const useGenericPathScaling = (
  gameId: string,
  baseWidth: number = 375,
  baseHeight: number = 667,
  containerDimensions?: ContainerDimensions
) => {
  const { dimensions } = useGenericSVGPath(gameId, containerDimensions);

  const scaleFactor = useMemo(() => {
    const widthScale = dimensions.width / baseWidth;
    const heightScale = dimensions.height / baseHeight;
    return Math.min(widthScale, heightScale); // Maintain aspect ratio
  }, [dimensions.width, dimensions.height, baseWidth, baseHeight]);

  const scaleValue = (value: number) => value * scaleFactor;

  return {
    scaleFactor,
    scaleValue,
    dimensions,
  };
};
