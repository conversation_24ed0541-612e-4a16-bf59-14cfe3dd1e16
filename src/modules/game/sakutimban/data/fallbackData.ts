import { GameQuizQuestionAPI, FallbackData } from '../types/sakuTBTypes';

// Fallback questions data khi API fail
export const fallbackQuestions: GameQuizQuestionAPI[] = [
  {
    Id: 'fallback-question-1',
    GameId: 'sakutb-fallback',
    Stage: 1,
    Content: 'Ghép các từ tiếng Anh với nghĩa tiếng Việt tương ứng',
    Options: JSON.stringify([
      // Left items (1-5)
      'Compare',
      'Market', 
      'Promise',
      'Dog',
      'Warning',
      // Right items (6-10)
      'So sánh',
      'https://api.dictionaryapi.dev/media/pronunciations/en/market-us.mp3',
      'https://api.dictionaryapi.dev/media/pronunciations/en/promise-us.mp3',
      'https://cdn.shopify.com/s/files/1/0086/0795/7054/files/Golden-Retriever.jpg?v=1645179525',
      'Cảnh báo'
    ]),
    CorrectAnswerIndex: JSON.stringify([
      '1,6',  // Compare -> So sánh
      '2,7',  // Market -> audio
      '3,8',  // Promise -> audio  
      '4,9',  // Dog -> image
      '5,10'  // Warning -> Cảnh báo
    ]),
    Sort: 1,
    IsActive: true,
    CreatedAt: new Date().toISOString(),
    UpdatedAt: new Date().toISOString()
  },
  {
    Id: 'fallback-question-2',
    GameId: 'sakutb-fallback',
    Stage: 1,
    Content: 'Ghép các từ về động vật với hình ảnh tương ứng',
    Options: JSON.stringify([
      // Left items (1-5)
      'Cat',
      'Bird',
      'Fish',
      'Horse',
      'Elephant',
      // Right items (6-10)
      'https://example.com/images/cat.jpg',
      'https://example.com/images/bird.jpg',
      'https://example.com/images/fish.jpg',
      'https://example.com/images/horse.jpg',
      'https://example.com/images/elephant.jpg'
    ]),
    CorrectAnswerIndex: JSON.stringify([
      '1,6',  // Cat -> cat image
      '2,7',  // Bird -> bird image
      '3,8',  // Fish -> fish image
      '4,9',  // Horse -> horse image
      '5,10'  // Elephant -> elephant image
    ]),
    Sort: 2,
    IsActive: true,
    CreatedAt: new Date().toISOString(),
    UpdatedAt: new Date().toISOString()
  },
  {
    Id: 'fallback-question-3',
    GameId: 'sakutb-fallback',
    Stage: 1,
    Content: 'Ghép các từ về màu sắc với âm thanh phát âm',
    Options: JSON.stringify([
      // Left items (1-5)
      'Red',
      'Blue',
      'Green',
      'Yellow',
      'Purple',
      // Right items (6-10)
      'https://example.com/audio/red.mp3',
      'https://example.com/audio/blue.mp3',
      'https://example.com/audio/green.mp3',
      'https://example.com/audio/yellow.mp3',
      'https://example.com/audio/purple.mp3'
    ]),
    CorrectAnswerIndex: JSON.stringify([
      '1,6',  // Red -> red audio
      '2,7',  // Blue -> blue audio
      '3,8',  // Green -> green audio
      '4,9',  // Yellow -> yellow audio
      '5,10'  // Purple -> purple audio
    ]),
    Sort: 3,
    IsActive: true,
    CreatedAt: new Date().toISOString(),
    UpdatedAt: new Date().toISOString()
  },
  {
    Id: 'fallback-question-4',
    GameId: 'sakutb-fallback',
    Stage: 1,
    Content: 'Ghép các từ về thức ăn với nghĩa tiếng Việt',
    Options: JSON.stringify([
      // Left items (1-5)
      'Apple',
      'Bread',
      'Water',
      'Rice',
      'Chicken',
      // Right items (6-10)
      'Táo',
      'Bánh mì',
      'Nước',
      'Cơm',
      'Gà'
    ]),
    CorrectAnswerIndex: JSON.stringify([
      '1,6',  // Apple -> Táo
      '2,7',  // Bread -> Bánh mì
      '3,8',  // Water -> Nước
      '4,9',  // Rice -> Cơm
      '5,10'  // Chicken -> Gà
    ]),
    Sort: 4,
    IsActive: true,
    CreatedAt: new Date().toISOString(),
    UpdatedAt: new Date().toISOString()
  },
  {
    Id: 'fallback-question-5',
    GameId: 'sakutb-fallback',
    Stage: 1,
    Content: 'Ghép các từ về gia đình với âm thanh phát âm',
    Options: JSON.stringify([
      // Left items (1-5)
      'Father',
      'Mother',
      'Brother',
      'Sister',
      'Baby',
      // Right items (6-10)
      'https://example.com/audio/father.mp3',
      'https://example.com/audio/mother.mp3',
      'https://example.com/audio/brother.mp3',
      'https://example.com/audio/sister.mp3',
      'https://example.com/audio/baby.mp3'
    ]),
    CorrectAnswerIndex: JSON.stringify([
      '1,6',  // Father -> father audio
      '2,7',  // Mother -> mother audio
      '3,8',  // Brother -> brother audio
      '4,9',  // Sister -> sister audio
      '5,10'  // Baby -> baby audio
    ]),
    Sort: 5,
    IsActive: true,
    CreatedAt: new Date().toISOString(),
    UpdatedAt: new Date().toISOString()
  }
];

// Fallback config
export const fallbackConfig = {
  maxLives: 3,
  timeLimit: 300 // 5 minutes
};

// Complete fallback data
export const fallbackData: FallbackData = {
  questions: fallbackQuestions,
  config: fallbackConfig
};

// Function to get fallback questions by count
export const getFallbackQuestions = (count: number = 5): GameQuizQuestionAPI[] => {
  return fallbackQuestions.slice(0, Math.min(count, fallbackQuestions.length));
};

// Function to validate fallback data
export const validateFallbackData = (): boolean => {
  try {
    // Check if all questions have valid structure
    for (const question of fallbackQuestions) {
      // Parse Options
      const options = JSON.parse(question.Options);
      if (!Array.isArray(options) || options.length !== 10) {
        console.error(`Fallback question ${question.Id} has invalid Options`);
        return false;
      }

      // Parse CorrectAnswerIndex
      const correctAnswers = JSON.parse(question.CorrectAnswerIndex);
      if (!Array.isArray(correctAnswers) || correctAnswers.length !== 5) {
        console.error(`Fallback question ${question.Id} has invalid CorrectAnswerIndex`);
        return false;
      }

      // Validate each pair
      for (const pair of correctAnswers) {
        const [leftIndex, rightIndex] = pair.split(',').map(Number);
        if (isNaN(leftIndex) || isNaN(rightIndex) || 
            leftIndex < 1 || leftIndex > 5 || 
            rightIndex < 6 || rightIndex > 10) {
          console.error(`Fallback question ${question.Id} has invalid pair: ${pair}`);
          return false;
        }
      }
    }

    console.log('[Fallback Data] Validation passed');
    return true;
  } catch (error) {
    console.error('[Fallback Data] Validation failed:', error);
    return false;
  }
};

// Initialize and validate fallback data
export const initializeFallbackData = (): FallbackData => {
  const isValid = validateFallbackData();
  
  if (!isValid) {
    console.warn('[Fallback Data] Validation failed, using minimal fallback');
    // Return minimal working data if validation fails
    return {
      questions: [fallbackQuestions[0]], // Just one question
      config: fallbackConfig
    };
  }

  console.log('[Fallback Data] Successfully initialized with', fallbackQuestions.length, 'questions');
  return fallbackData;
};
