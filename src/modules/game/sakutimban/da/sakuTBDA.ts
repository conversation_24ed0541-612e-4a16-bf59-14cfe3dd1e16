import {DataController} from '../../../../base/baseController';
import {
  GameQuizQuestionAPI,
  GetQuestionsResponse,
  ApiResponse,
  SakuTBError,
  GameConfig,
} from '../types/sakuTBTypes';

export class SakuTBDA {
  private questionController: DataController;
  //GameAnswer
  private answerController: DataController;

  constructor() {
    this.questionController = new DataController('GameQuestion');
    this.answerController = new DataController('GameAnswer');
  }

  /**
   * Lấy danh sách câu hỏi từ bảng GameQuizQuestion
   * @param gameId ID của game SakuTB
   * @param stage Stage hiện tại (mặc định = 1)
   * @returns Promise<GameQuizQuestionAPI[]>
   */
  async getQuestionsByGameAndStage(
    gameId: string,
    stage: number = 1,
    competenceId: string,
  ): Promise<GameQuizQuestionAPI[]> {
    try {
      const response: GetQuestionsResponse =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} @Stage: [${stage}] @Purpose: [${competenceId}]`,
          sortby: {BY: 'Sort', DIRECTION: 'ASC'},
        });
      if (response.code === 200) {
        //lấy danh sách đáp an
        const answerResponse = await this.answerController.getListSimple({
          query: `@GameQuestionId: {${response.data.map((q: any) => q.Id).join(' | ')}}`,
        });
        if (answerResponse && answerResponse.data && answerResponse.data.length > 0) {
          // Map các câu trả lời vào câu hỏi
          response.data.forEach((question: any) => {
            //xử lý random đảo đáp án trả lời
            // const shuffledAnswers = shuffleArray(answerResponse.data.filter((answer: any) => answer.GameQuestionId === question.Id));
            question.Answers = answerResponse.data.filter((answer: any) => answer.GameQuestionId === question.Id);
          });
        }
        

        const questions = response.data || [];
        // Validate questions
        const validQuestions = this.validateQuestions(questions);
        return validQuestions;
      } else {
        throw new Error(
          `API Error: ${response.message} (Code: ${response.code})`,
        );
      }
    } catch (error) {
      console.error('[SakuTBDA] Error fetching questions:', error);
      throw this.createError(
        'API_ERROR',
        'Failed to fetch questions from API',
        error,
      );
    }
  }

  /**
   * Lấy câu hỏi theo ID cụ thể
   * @param questionId ID của câu hỏi
   * @returns Promise<GameQuizQuestionAPI | null>
   */
  async getQuestionById(
    questionId: string,
  ): Promise<GameQuizQuestionAPI | null> {
    try {
      console.log(`[SakuTBDA] Fetching question by ID: ${questionId}`);

      const response = await this.questionController.getById(questionId);

      if (response.code === 200 && response.data) {
        return response.data as GameQuizQuestionAPI;
      }

      return null;
    } catch (error) {
      console.error('[SakuTBDA] Error fetching question by ID:', error);
      throw this.createError(
        'API_ERROR',
        'Failed to fetch question by ID',
        error,
      );
    }
  }

  /**
   * Lấy danh sách tất cả stages có sẵn cho game
   * @param gameId ID của game
   * @returns Promise<number[]>
   */
  async getAvailableStages(gameId: string): Promise<number[]> {
    try {
      console.log(`[SakuTBDA] Fetching available stages for GameId: ${gameId}`);

      const response: ApiResponse<GameQuizQuestionAPI> =
        await this.questionController.getListSimple({
          query: `@GameId: {${gameId}} AND @IsActive: true`,
          sortby: {BY: 'Stage', DIRECTION: 'ASC'},
        });

      if (response.code === 200) {
        const questions = response.data || [];
        // Lấy danh sách stages unique
        const stages = [...new Set(questions.map(q => q.Stage))].sort(
          (a, b) => a - b,
        );
        console.log(`[SakuTBDA] Available stages:`, stages);
        return stages;
      }

      return [1]; // Default stage
    } catch (error) {
      console.error('[SakuTBDA] Error fetching stages:', error);
      return [1]; // Fallback to stage 1
    }
  }

  /**
   * Validate questions data (hỗ trợ cả logic cũ và mới)
   * @param questions Raw questions from API
   * @returns Valid questions
   */
  private validateQuestions(
    questions: GameQuizQuestionAPI[],
  ): GameQuizQuestionAPI[] {
    return questions.filter(question => {
      try {
        // Check required fields
        if (!question.Id || !question.Name) {
          console.warn(
            `[SakuTBDA] Question ${question.Id} missing required fields`,
          );
          return false;
        }

        // Kiểm tra xem có Answers (logic mới) hay Options (logic cũ)
        const hasAnswers = (question as any).Answers && Array.isArray((question as any).Answers);

        if (hasAnswers) {
          // Validate logic mới với GameAnswer
          const answers = (question as any).Answers;

          if (answers.length === 0) {
            console.warn(
              `[SakuTBDA] Question ${question.Id} has no answers`,
            );
            return false;
          }

          // Kiểm tra có left và right items
          const leftItems = answers.filter((a: any) => a.IsResult === true);
          const rightItems = answers.filter((a: any) => a.IsResult !== true);

          if (leftItems.length === 0 || rightItems.length === 0) {
            console.warn(
              `[SakuTBDA] Question ${question.Id} missing left or right items`,
            );
            return false;
          }

          // Kiểm tra Sort field
          const hasValidSort = answers.every((a: any) =>
            typeof a.Sort === 'number' && a.Sort > 0
          );

          if (!hasValidSort) {
            console.warn(
              `[SakuTBDA] Question ${question.Id} has invalid Sort fields`,
            );
            return false;
          }

          return true;
        } else {
          console.warn(
            `[SakuTBDA] Question ${question.Id} missing both Answers and Options/CorrectAnswerIndex`,
          );
          return false;
        }
      } catch (error) {
        console.warn(
          `[SakuTBDA] Question ${question.Id} validation failed:`,
          error,
        );
        return false;
      }
    });
  }

  /**
   * Create standardized error
   * @param type Error type
   * @param message Error message
   * @param details Additional details
   * @returns SakuTBError
   */
  private createError(
    type: SakuTBError['type'],
    message: string,
    details?: any,
  ): SakuTBError {
    return {
      type,
      message,
      details,
    };
  }
  static async getGameConfig(gameId: string): Promise<any> {
    try {
      const controller = new DataController('GameConfig');
      const response = await controller.getListSimple({
        query: `@GameId: {${gameId}}`,
      });
      if (
        response.code !== 200 ||
        !response.data ||
        response.data.length === 0
      ) {
        throw new Error(
          'No game config found or API returned unsuccessful response',
        );
      }
      const configData = response.data[0];
      return {
        scorePerLife: configData.Score,
        maxLives: configData.LifeCount,
        timeLimit: configData.Time,
        bonusScore: configData.Bonus,
        isActive: true,
      };
    } catch (error) {
      console.error('[GameConfigDA] Error loading game config:', error);
      throw error;
    }
  }
  static calculateScore(
    config: GameConfig,
    livesRemaining: number,
    totalLives: number,
  ): number {
    const baseScore = livesRemaining * config.scorePerLife;

    // Bonus nếu không mất mạng nào
    const bonus = livesRemaining === totalLives ? config.bonusScore : 0;

    const finalScore = baseScore + bonus;

    return finalScore;
  }
}
