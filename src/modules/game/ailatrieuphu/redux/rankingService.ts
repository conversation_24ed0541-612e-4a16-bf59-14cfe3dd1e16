import { DataController } from '../../../../base/baseController';
import { getDataToAsyncStorage } from '../../../../utils/AsyncStorage';
import { StorageContanst } from '../../../../Config/Contanst';

// Interface cho thông tin xếp hạng
export interface RankingInfo {
  rank: number;        // Thứ hạng của người chơi
  totalPlayers: number; // Tổng số người chơi
  score: number;       // Điểm số của người chơi
  previousRank?: number; // Thứ hạng trước đó (nếu có)
  gameScore?: number;  // Điểm số trong game hiện tại
}

// Hàm lấy xếp hạng của người chơi dựa trên điểm tổng
export const getUserRanking = async (gameScore: number): Promise<RankingInfo | null> => {
  try {
    // Lấy CustomerId từ AsyncStorage
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (!cusId) {
      console.error('Không tìm thấy CustomerId');
      return null;
    }

    // Lấy dữ liệu từ API - Bảng Customer để lấy điểm tổng
    const customerController = new DataController('Customer');

    // Lấy thông tin điểm tổng của người chơi hiện tại
    const userResponse = await customerController.getListSimple({
      query: `@Id: {${cusId}}`,
    });

    if (!userResponse.data || userResponse.data.length === 0) {
      console.error('Không tìm thấy thông tin người dùng');
      return null;
    }

    // Lấy điểm tổng hiện tại của người chơi
    const totalScore = userResponse.data[0].Rank || 0;

    // Lấy danh sách tất cả người dùng, sắp xếp theo điểm tổng
    const allUsersResponse = await customerController.getListSimple({
      query: '@Rank: [0 TO *]', // Lấy tất cả người dùng có điểm > 0
      sortby: { BY: 'Rank', DIRECTION: 'DESC' },
    });

    if (!allUsersResponse.data || allUsersResponse.data.length === 0) {
      return {
        rank: 1,
        totalPlayers: 1,
        score: totalScore,
        gameScore: gameScore,
      };
    }

    // Tính thứ hạng của người chơi
    const totalPlayers = allUsersResponse.data.length;
    let userRank = 0;

    for (let i = 0; i < allUsersResponse.data.length; i++) {
      if (allUsersResponse.data[i].Id === cusId) {
        userRank = i + 1;
        break;
      }
    }

    // Nếu không tìm thấy người chơi trong danh sách (trường hợp hiếm gặp)
    if (userRank === 0) {
      userRank = totalPlayers;
    }

    // Trả về thông tin xếp hạng
    return {
      rank: userRank,
      totalPlayers: totalPlayers,
      score: totalScore,
      gameScore: gameScore,
    };
  } catch (error) {
    console.error('Lỗi khi lấy xếp hạng:', error);
    return null;
  }
};

// Hàm lấy xếp hạng trước và sau khi cập nhật điểm
export const getBeforeAndAfterRanking = async (newGameScore: number, gameId: string): Promise<{
  beforeRanking: RankingInfo | null;
  afterRanking: RankingInfo | null;
}> => {
  try {
    // Lấy CustomerId từ AsyncStorage
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (!cusId) {
      console.error('Không tìm thấy CustomerId');
      return { beforeRanking: null, afterRanking: null };
    }

    // Lấy dữ liệu từ API - Bảng GameCustomer để lấy lịch sử chơi game
    const gameCustomerController = new DataController('GameCustomer');

    // Lấy tất cả lịch sử chơi game của game này
    const allGameRecordsResponse = await gameCustomerController.getListSimple({
      query: `@GameId: {${gameId}}`,
      sortby: { BY: 'Score', DIRECTION: 'DESC' },
    });

    if (!allGameRecordsResponse.data) {
      console.error('Không lấy được dữ liệu GameCustomer');
      return { beforeRanking: null, afterRanking: null };
    }

    // Tính tổng điểm của từng người chơi
    const playerScores = new Map<string, number>();

    allGameRecordsResponse.data.forEach((record: any) => {
      const customerId = record.CustomerId;
      const score = record.Score || 0;

      if (playerScores.has(customerId)) {
        // Nếu người chơi đã có trong map, cộng thêm điểm
        playerScores.set(customerId, playerScores.get(customerId)! + score);
      } else {
        // Nếu người chơi chưa có trong map, thêm mới
        playerScores.set(customerId, score);
      }
    });

    // Chuyển đổi Map thành array và sắp xếp theo điểm giảm dần
    const sortedPlayers = Array.from(playerScores.entries())
      .map(([customerId, totalScore]) => ({ customerId, totalScore }))
      .sort((a, b) => b.totalScore - a.totalScore);

    const totalPlayers = sortedPlayers.length;

    // Lấy điểm hiện tại của người chơi
    const currentPlayerScore = playerScores.get(cusId) || 0;

    // Tính xếp hạng trước khi cập nhật
    let beforeRanking: RankingInfo | null = null;
    let userRankBefore = 0;

    // Tìm xếp hạng hiện tại của người chơi
    for (let i = 0; i < sortedPlayers.length; i++) {
      if (sortedPlayers[i].customerId === cusId) {
        userRankBefore = i + 1;
        break;
      }
    }

    // Nếu không tìm thấy người chơi trong danh sách (chưa chơi game này)
    if (userRankBefore === 0) {
      userRankBefore = totalPlayers + 1;
    }

    beforeRanking = {
      rank: userRankBefore,
      totalPlayers: totalPlayers,
      score: currentPlayerScore,
      gameScore: 0, // Điểm game trước khi chơi
    };

    // Tính điểm mới sau khi cộng điểm game hiện tại
    const newTotalScore = currentPlayerScore + newGameScore;

    // Tính xếp hạng sau khi cập nhật
    let userRankAfter = 1; // Bắt đầu từ hạng 1

    // Đếm số người chơi có điểm cao hơn điểm mới
    for (const player of sortedPlayers) {
      if (player.customerId === cusId) {
        // Bỏ qua người chơi hiện tại, sẽ tính với điểm mới
        continue;
      }
      if (player.totalScore > newTotalScore) {
        userRankAfter++;
      }
    }

    const afterRanking: RankingInfo = {
      rank: userRankAfter,
      totalPlayers: Math.max(totalPlayers, 1), // Đảm bảo ít nhất có 1 người chơi
      score: newTotalScore,
      previousRank: beforeRanking.rank,
      gameScore: newGameScore, // Điểm game hiện tại
    };

    return {
      beforeRanking,
      afterRanking,
    };
  } catch (error) {
    console.error('Lỗi khi lấy xếp hạng trước và sau:', error);
    return { beforeRanking: null, afterRanking: null };
  }
};
