import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { ALTPQuestion } from '../data/questions';
import { formatMoney } from '../data/questionSets';

interface QuestionDisplayProps {
  question: ALTPQuestion;
  questionNumber: number;
}

const { width } = Dimensions.get('window');

const QuestionDisplay: React.FC<QuestionDisplayProps> = ({ question, questionNumber }) => {
  return (
    <View style={styles.container}>
      
      
      <View style={styles.questionContainer}>
        <Text style={styles.questionNumber}>Câu hỏi {questionNumber}</Text>
        <Text style={styles.questionText}>{question.question}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: width - 40,
    alignSelf: 'center',
    marginBottom: 20,
    
  },
  moneyContainer: {
    backgroundColor: '#1E3A8A',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  moneyText: {
    color: '#FFD700',
    fontWeight: 'bold',
    fontSize: 18,
  },
  questionContainer: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#FFD700',
    backgroundColor: '#FCF8E8', // Màu nền như trong ảnh
  },
  questionNumber: {
    color: '#FFD700',
    fontSize: 14,
    marginBottom: 8,
    fontWeight: 'bold',
  },
  questionText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default QuestionDisplay;
