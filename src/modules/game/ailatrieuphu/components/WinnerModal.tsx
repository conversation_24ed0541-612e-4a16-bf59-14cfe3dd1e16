import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  Image,
  ImageBackground,
  ActivityIndicator,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {gameAction, resetGame} from '../redux/gameSlice';
import store, {AppDispatch, RootState} from '../../../../redux/store/store';
import {GameStatus} from '../../../../Config/Contanst';
import ConfigAPI from '../../../../Config/ConfigAPI';
import {CustomerActions} from '../../../../redux/reducers/CustomerReducer';
import {getBeforeAndAfterRanking, RankingInfo} from '../redux/rankingService';
import {Winicon} from 'wini-mobile-components';
import { CustomerDA } from '../../../customer/da';

const {width, height} = Dimensions.get('window');

interface WinnerModalProps {
  visible: boolean;
  onClose: () => void;
  // score: number;
  isTimeOut?: boolean;
}

const WinnerModal = ({
  visible,
  onClose,
  // score,
  isTimeOut = false,
}: WinnerModalProps) => {
  const navigation = useNavigation<any>();
  const dispatch: AppDispatch = useDispatch();
  const gameState = useSelector((state: RootState) => state.game);
  const [score, setScore] = useState(0);

  // State để lưu trữ thông tin xếp hạng
  const [rankingInfo, setRankingInfo] = useState<{
    beforeRanking: RankingInfo | null;
    afterRanking: RankingInfo | null;
  }>({beforeRanking: null, afterRanking: null});

  // State để theo dõi trạng thái tải xếp hạng
  const [loadingRanking, setLoadingRanking] = useState(false);

  // State để hiển thị thông báo xếp hạng
  const [showRankingMessage, setShowRankingMessage] = useState(false);

  // Cập nhật trạng thái milestone khi modal hiển thị
  useEffect(() => {
    debugger
    if (visible) {
      // Lấy thông tin milestone hiện tại
      const currentMilestoneId = gameState.currentMilestoneId;
      if (!currentMilestoneId) {
        return;
      }

      // Tìm milestone hiện tại trong danh sách milestone
      const currentMilestone = gameState.Milestone.find(
        m => m.id === currentMilestoneId
      );

      // Xác định trạng thái mới - Đặt là Completed nếu đã hoàn thành tất cả câu hỏi
      // hoặc Pending nếu chưa hoàn thành
      const isCompleted = gameState.currentQuestionIndex >= 14;

      // Chỉ cập nhật trạng thái nếu milestone chưa hoàn thành trước đó
      // Nếu milestone đã là Completed, giữ nguyên trạng thái
      let newStatus = GameStatus.Pending;
      if (isCompleted) {
        newStatus = GameStatus.Completed;
      } else if (currentMilestone && currentMilestone.status === GameStatus.Completed) {
        // Nếu milestone đã hoàn thành trước đó, giữ nguyên trạng thái Completed
        newStatus = GameStatus.Completed;
      }

      // Cập nhật điểm cho người dùng
      dispatch(
        CustomerActions.updateRank(gameState.currentMoney, ConfigAPI.gameALTP),
      );
      dispatch(CustomerActions.getInfor());
      // Cập nhật trạng thái milestone
      // Chỉ cập nhật nếu milestone chưa hoàn thành hoặc người dùng vừa hoàn thành
      if (isCompleted || (currentMilestone && currentMilestone.status !== GameStatus.Completed)) {
        console.log(`Cập nhật milestone ${currentMilestoneId} với trạng thái ${newStatus}`);
        dispatch(
          gameAction.updateMilestoneStatus(
            currentMilestoneId,
            newStatus,
            gameState.currentMoney,
          ),
        );
      } else {
        console.log(`Không cập nhật milestone ${currentMilestoneId} vì đã hoàn thành trước đó`);
      }
    }
  }, [visible]);

  // Lấy thông tin xếp hạng khi modal hiển thị
  useEffect(() => {
    const fetchRanking = async () => {
      if (visible) {
        try {
          setLoadingRanking(true);

          // Lấy thông tin xếp hạng trước và sau khi cập nhật điểm
          // Sử dụng điểm tổng từ bảng Customer thay vì điểm từ GameCustomer
          const rankingData = await getBeforeAndAfterRanking(
            gameState.currentMoney,
          );

          setRankingInfo(rankingData);

          // Hiển thị thông báo xếp hạng sau 1 giây
          setTimeout(() => {
            setShowRankingMessage(true);
          }, 1000);
        } catch (error) {
          console.error('Lỗi khi lấy thông tin xếp hạng:', error);
        } finally {
          setLoadingRanking(false);
        }
      }
    };
    const fetchScore = async () => {
      const customerDa = new CustomerDA();
      if (visible) {
        try {
          // Lấy thông tin điểm từ bảng Customer
          const customer = await customerDa.getCustomerbyId(store.getState().customer.data.Id);
          setScore(customer?.data?.Rank || 0);
        } catch (error) {
          console.error('Lỗi khi lấy thông tin điểm:', error);
        }
      }
    };
    fetchScore();
    fetchRanking();
  }, [visible, gameState.currentMoney]);

  const handleNext = () => {
    // Đóng modal trước
    onClose();

    // Sử dụng setTimeout để đảm bảo setState không xảy ra trong quá trình render
    setTimeout(async () => {
      try {
        // Reset game state
        dispatch(resetGame());

        // Đảm bảo dữ liệu milestone được cập nhật khi quay lại màn hình Home
        // Gọi getMilestones trước khi điều hướng để đảm bảo dữ liệu được cập nhật
        await dispatch(gameAction.getMilestones(ConfigAPI.gameALTP));

        // Điều hướng đến màn hình Home
        navigation.goBack();
      } catch (error) {
        console.error('Error updating milestones:', error);
        // Vẫn điều hướng về màn hình Home ngay cả khi có lỗi
        navigation.goBack();
      }
    }, 0);
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* Tiêu đề Winner */}
          <Text style={styles.winnerText}>Winner</Text>
          {/* Hiển thị điểm số */}
          <ImageBackground
            source={require('../assets/bg_coin.png')}
            style={styles.scoreContainer}
            resizeMode="contain">
            {/* Điểm cơ bản + điểm thưởng */}
            <View style={styles.scoreItem}>
              <Image
                source={require('../assets/coin-icon.png')}
                style={styles.diamondIcon}
                // resizeMode="contain"
              />
              <Text style={styles.scoreText}>{score} + {gameState.currentMoney}</Text>
            </View>

            {/* Tổng điểm */}
            <View style={styles.scoreItem}>
              <Image
                source={require('../assets/rank.png')}
                style={styles.diamondIcon}
              />
              {loadingRanking ? (
                <ActivityIndicator size="small" color="#FFD700" />
              ) : (
                <>
                  <Text style={styles.scoreText}>
                    {rankingInfo?.afterRanking?.rank}
                  </Text>
                  {rankingInfo?.afterRanking?.previousRank &&
                    rankingInfo?.afterRanking?.rank <
                      rankingInfo?.afterRanking?.previousRank && (
                      <Text style={styles.rankUpText}>
                        <Winicon
                          src={'color/arrows/triangle-up'}
                          size={9}
                          color="#4CAF50"
                        />{' '}
                        {rankingInfo?.afterRanking?.previousRank -
                        rankingInfo?.afterRanking?.rank}
                      </Text>
                    )}
                </>
              )}
            </View>
          </ImageBackground>

          {/* Hình ảnh chim vui mừng */}
          <View style={styles.birdContainer}>
            {/* <View style={styles.birdImage} /> */}
            <Image
              source={require('../assets/result_icon.png')}
              style={styles.birdImage}
            />
          </View>

          {/* Nút Next */}
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Image
              source={require('../assets/next_btn.png')}
              style={styles.nextButton}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.69)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: width * 0.93,
    height: height * 0.76,
    backgroundColor: 'rgba(112, 90, 64, 0.96)', // Màu nền nâu như trong ảnh
    padding: 20,
    marginBottom: 20,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  winnerText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFD700', // Màu vàng
    textAlign: 'center',
    marginTop: 20,
    textShadowColor: '#000',
    textShadowOffset: {width: 2, height: 2},
    textShadowRadius: 5,
    fontFamily: 'BagelFatOne-Regular',
  },
  scoreContainer: {
    alignItems: 'center',
    marginTop: 20,
    width: '100%',
  },
  scoreItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 20,
    height: 30,
    justifyContent: 'center',
    position: 'relative',
    marginBottom: 20,
    paddingHorizontal: 10,
    paddingLeft: 20,
  },
  diamondIcon: {
    marginRight: 10,
    borderRadius: 15,
    width: 40,
    height: 47,
    position: 'absolute',
    left: -20,
    top: -10,
  },
  trophyIcon: {
    width: 30,
    height: 30,
    marginRight: 10,
    backgroundColor: '#FFD700',
    borderRadius: 15,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft:10
  },
  totalScoreText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  birdContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 20,
  },
  birdImage: {
    resizeMode: 'contain',
  },
  congratsText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginTop: 10,
  },
  nextButton: {
    width: 230,
    height: 90,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
  },
  rankingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
    minHeight: 50,
  },
  rankingText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 5,
  },
  rankUpText: {
    fontSize: 12,
    marginLeft: 10,
    fontWeight: 'bold',
    color: '#4CAF50', // Màu xanh lá cây
    textAlign: 'center',
  },
});

export default WinnerModal;
