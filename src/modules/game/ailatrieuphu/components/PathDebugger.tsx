import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Dimensions } from 'react-native';
import { milestonePositions } from '../data/milestonePositions';
import { getMilestonePositions, getPathTrail } from '../data/pathPositions';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const PathDebugger: React.FC = () => {
  const [containerDimensions, setContainerDimensions] = useState({
    width: screenWidth,
    height: screenHeight * 0.7,
  });

  const [showOriginal, setShowOriginal] = useState(true);
  const [showSVGPath, setShowSVGPath] = useState(true);
  const [showTrail, setShowTrail] = useState(true);

  // Lấy vị trí từ SVG path
  const svgMilestonePositions = getMilestonePositions(
    containerDimensions.width,
    containerDimensions.height
  );

  // Lấy trail points
  const trailPoints = getPathTrail(
    containerDimensions.width,
    containerDimensions.height,
    50
  );

  // Tính toán vị trí gốc từ milestonePositions
  const originalPositions = milestonePositions.map(milestone => ({
    id: milestone.id,
    x: milestone.left * containerDimensions.width,
    y: milestone.top * containerDimensions.height,
    name: milestone.levelName || `Chặng ${milestone.id}`,
  }));

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Path Debugger - Milestone Positions</Text>

      {/* Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.button, showOriginal && styles.activeButton]}
          onPress={() => setShowOriginal(!showOriginal)}
        >
          <Text style={[styles.buttonText, showOriginal && styles.activeButtonText]}>
            Original Positions
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, showSVGPath && styles.activeButton]}
          onPress={() => setShowSVGPath(!showSVGPath)}
        >
          <Text style={[styles.buttonText, showSVGPath && styles.activeButtonText]}>
            SVG Path Positions
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, showTrail && styles.activeButton]}
          onPress={() => setShowTrail(!showTrail)}
        >
          <Text style={[styles.buttonText, showTrail && styles.activeButtonText]}>
            Path Trail
          </Text>
        </TouchableOpacity>
      </View>

      {/* Game Area */}
      <View style={[styles.gameArea, containerDimensions]}>
        {/* Path Trail */}
        {showTrail && trailPoints.map((point, index) => (
          <View
            key={`trail-${index}`}
            style={[
              styles.trailPoint,
              {
                left: point.x - 1,
                top: point.y - 1,
              }
            ]}
          />
        ))}

        {/* Original Milestone Positions */}
        {showOriginal && originalPositions.map((milestone) => (
          <View
            key={`original-${milestone.id}`}
            style={[
              styles.originalMilestone,
              {
                left: milestone.x - 20,
                top: milestone.y - 20,
              }
            ]}
          >
            <Text style={styles.milestoneText}>{milestone.id}</Text>
          </View>
        ))}

        {/* SVG Path Milestone Positions */}
        {showSVGPath && svgMilestonePositions.map((milestone) => (
          <View
            key={`svg-${milestone.id}`}
            style={[
              styles.svgMilestone,
              {
                left: milestone.x - 20,
                top: milestone.y - 20,
              }
            ]}
          >
            <Text style={styles.milestoneText}>{milestone.id}</Text>
          </View>
        ))}
      </View>

      {/* Debug Info */}
      <View style={styles.debugInfo}>
        <Text style={styles.debugTitle}>Debug Information</Text>
        
        <Text style={styles.debugText}>
          Container: {containerDimensions.width}x{containerDimensions.height}
        </Text>
        
        <Text style={styles.debugText}>
          Original Milestones: {originalPositions.length}
        </Text>
        
        <Text style={styles.debugText}>
          SVG Milestones: {svgMilestonePositions.length}
        </Text>
        
        <Text style={styles.debugText}>
          Trail Points: {trailPoints.length}
        </Text>

        <Text style={styles.debugSubtitle}>Position Comparison:</Text>
        
        {originalPositions.map((original, index) => {
          const svg = svgMilestonePositions[index];
          const deltaX = svg ? Math.abs(original.x - svg.x) : 0;
          const deltaY = svg ? Math.abs(original.y - svg.y) : 0;
          
          return (
            <View key={original.id} style={styles.comparisonRow}>
              <Text style={styles.comparisonText}>
                Milestone {original.id}:
              </Text>
              <Text style={styles.comparisonText}>
                Original: ({original.x.toFixed(1)}, {original.y.toFixed(1)})
              </Text>
              {svg && (
                <>
                  <Text style={styles.comparisonText}>
                    SVG: ({svg.x.toFixed(1)}, {svg.y.toFixed(1)})
                  </Text>
                  <Text style={[
                    styles.comparisonText,
                    (deltaX > 5 || deltaY > 5) && styles.errorText
                  ]}>
                    Delta: ({deltaX.toFixed(1)}, {deltaY.toFixed(1)})
                  </Text>
                </>
              )}
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    padding: 16,
    backgroundColor: '#112164',
    color: '#fff',
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#ddd',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 4,
  },
  activeButton: {
    backgroundColor: '#112164',
  },
  buttonText: {
    fontSize: 12,
    color: '#333',
  },
  activeButtonText: {
    color: '#fff',
  },
  gameArea: {
    backgroundColor: '#e8f5e8',
    margin: 16,
    borderRadius: 8,
    position: 'relative',
    borderWidth: 2,
    borderColor: '#112164',
  },
  trailPoint: {
    position: 'absolute',
    width: 2,
    height: 2,
    borderRadius: 1,
    backgroundColor: '#4CAF50',
    opacity: 0.6,
  },
  originalMilestone: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF5722',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#D84315',
  },
  svgMilestone: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#1976D2',
    opacity: 0.8,
  },
  milestoneText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
  },
  debugInfo: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#112164',
  },
  debugSubtitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
    color: '#333',
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  comparisonRow: {
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  comparisonText: {
    fontSize: 11,
    color: '#666',
    marginBottom: 2,
  },
  errorText: {
    color: '#f44336',
    fontWeight: 'bold',
  },
});

export default PathDebugger;
