import React from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { moneyLevels } from '../data/questions';
import { formatMoney } from '../data/questionSets';

interface MoneyLadderProps {
  currentLevel: number;
  guaranteedLevel: number;
}

const { width } = Dimensions.get('window');

const MoneyLadder: React.FC<MoneyLadderProps> = ({ currentLevel, guaranteedLevel }) => {
  // Đảo ngược mảng để hiển thị từ cao xuống thấp
  const reversedLevels = [...moneyLevels].reverse();
  
  // Tính toán level hiện tại (đảo ngược)
  const currentReversedLevel = moneyLevels.length - 1 - currentLevel;
  
  // Tính toán level đảm bảo (đảo ngược)
  const guaranteedReversedLevel = moneyLevels.findIndex(money => money === guaranteedLevel);
  const reversedGuaranteedLevel = guaranteedReversedLevel !== -1 
    ? moneyLevels.length - 1 - guaranteedReversedLevel 
    : -1;

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {reversedLevels.map((money, index) => {
          const questionNumber = moneyLevels.length - index;
          const isCurrent = index === currentReversedLevel;
          const isGuaranteed = index === reversedGuaranteedLevel || 
                              (questionNumber === 5 || questionNumber === 10);
          
          return (
            <View 
              key={index} 
              style={[
                styles.moneyItem,
                isCurrent && styles.currentMoneyItem,
                isGuaranteed && styles.guaranteedMoneyItem
              ]}
            >
              <Text style={[
                styles.questionNumber,
                isCurrent && styles.currentText
              ]}>
                {questionNumber}
              </Text>
              <Text style={[
                styles.moneyText,
                isCurrent && styles.currentText
              ]}>
                {formatMoney(money)}
              </Text>
            </View>
          );
        })}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: width * 0.4,
    height: '100%',
    backgroundColor: 'rgba(30, 58, 138, 0.9)',
    borderLeftWidth: 2,
    borderColor: '#4C6EF5',
  },
  scrollView: {
    flex: 1,
  },
  moneyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  currentMoneyItem: {
    backgroundColor: '#4C6EF5',
  },
  guaranteedMoneyItem: {
    borderLeftWidth: 4,
    borderLeftColor: '#FFD700',
  },
  questionNumber: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 8,
    width: 20,
  },
  moneyText: {
    color: 'white',
    fontSize: 12,
    flex: 1,
  },
  currentText: {
    color: '#FFD700',
    fontWeight: 'bold',
  },
});

export default MoneyLadder;
