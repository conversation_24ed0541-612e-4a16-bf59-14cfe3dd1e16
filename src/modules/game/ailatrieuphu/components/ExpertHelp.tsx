import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import * as Animatable from 'react-native-animatable';

interface ExpertHelpProps {
  visible: boolean;
  onClose: () => void;
  answerIndex: number;
  isPhoneCall?: boolean;
}

const {width} = Dimensions.get('window');

const ExpertHelp: React.FC<ExpertHelpProps> = ({
  visible,
  onClose,
  answerIndex,
  isPhoneCall = false,
}) => {
  const optionLetters = ['A', 'B', 'C', 'D'];

  const getTitle = () => {
    return isPhoneCall
      ? 'Gọi điện thoại cho người thân'
      : 'Hỏi ý kiến chuyên gia';
  };

  const getMessage = () => {
    if (isPhoneCall) {
      return `Người thân của bạn nghĩ đáp án đúng là ${optionLetters[answerIndex]}.`;
    } else {
      return `Theo phân tích của tôi, đáp án đúng là ${optionLetters[answerIndex]}.`;
    }
  };

  const getImage = () => {
    if (isPhoneCall) {
      return require('../assets/phone-call.png');
    } else {
      return require('../assets/chuyengia.png');
    }
  };

  return (
    <Modal visible={visible} transparent={true} animationType="fade">
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.title}>{getTitle()}</Text>

          <View style={styles.expertContainer}>
            <Animatable.View
              animation="bounceIn"
              duration={1000}
              style={styles.imageContainer}>
              <Image
                source={getImage()}
                style={styles.expertImage}
                resizeMode="contain"
              />
            </Animatable.View>

            <Animatable.View
              animation="fadeIn"
              delay={500}
              duration={1000}
              style={styles.messageContainer}>
              <View style={styles.messageBubble}>
                <Text style={styles.messageText}>{getMessage()}</Text>
              </View>
            </Animatable.View>
          </View>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Đóng</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    width: width - 60,
    backgroundColor: '#F1D1A6', // Màu nền giống màn hình chính
    borderRadius: 15,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#112164', // Màu xanh đậm như trong ảnh
    textAlign: 'center',
    marginBottom: 20,
  },
  expertContainer: {
    alignItems: 'center',
    marginVertical: 15,
  },
  imageContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#FCF8E8', // Màu nền nhạt giống câu hỏi
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  expertImage: {
    width: 60,
    height: 60,
    tintColor: '#112164', // Màu xanh đậm như trong ảnh
  },
  messageContainer: {
    width: '100%',
  },
  messageBubble: {
    backgroundColor: '#FCF8E8', // Màu nền nhạt giống câu hỏi
    borderRadius: 15,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageText: {
    color: '#112164', // Màu xanh đậm như trong ảnh
    fontSize: 18,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: '#FF5757', // Màu đỏ cam như header
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 25,
    alignSelf: 'center',
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 3,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
});

export default ExpertHelp;
