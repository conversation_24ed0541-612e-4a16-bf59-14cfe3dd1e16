import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
  Pressable,
} from 'react-native';

const {width} = Dimensions.get('window');

interface ChooseLevelModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectLevel: (level: string) => void;
  selectedLevel?: string;
  unlockedLevels?: string[]; // Các level đã mở khóa
  allLevels?: string[]; // Các level
}

const ChooseLevelModal: React.FC<ChooseLevelModalProps> = ({
  visible,
  onClose,
  onSelectLevel,
  selectedLevel = 'N5',
  unlockedLevels = ['N5'], // Mặc định N1-N4 đã mở khóa
  allLevels = ['N1', 'N2', 'N3', 'N4', 'N5'], // Mặc định N1-N4 đã mở khóa
}) => {
  const levels = [...allLevels];

  const handleLevelPress = (level: string) => {
    if (unlockedLevels.includes(level)) {
      onSelectLevel(level);
      onClose();
    }
  };

  const renderLevelButton = (level: string) => {
    const isSelected = selectedLevel === level;
    const isUnlocked = unlockedLevels.includes(level);

    const buttonStyle = [
      styles.levelButton,
      isSelected && styles.selectedLevelButton,
      !isUnlocked && styles.lockedLevelButton,
    ];

    const textStyle = [
      styles.levelButtonText,
      isSelected && styles.selectedLevelButtonText,
      !isUnlocked && styles.lockedLevelButtonText,
    ];

    return (
      <TouchableOpacity
        key={level}
        style={buttonStyle}
        onPress={() => handleLevelPress(level)}
        disabled={!isUnlocked}
        activeOpacity={isUnlocked ? 0.7 : 1}>
        <Text style={textStyle}>{level}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onDismiss={onClose}
      onRequestClose={onClose}>
      <Pressable onPress={onClose} style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.headerContainer}>
            <Text style={styles.headerText}>TRÌNH ĐỘ</Text>
          </View>

          {/* Description */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>
              Bạn chỉ chọn được các trình độ đã phá đảo
            </Text>
          </View>

          {/* Level Buttons */}
          <View style={styles.levelsContainer}>
            {levels.map(level => renderLevelButton(level))}
          </View>

          {/* Close button area - tap outside to close */}
          <TouchableOpacity
            style={styles.closeArea}
            onPress={onClose}
            activeOpacity={1}
          />
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: width * 0.85,
    backgroundColor: '#D4B896', // Màu nền be/nâu nhạt như trong hình
    borderRadius: 20,
    paddingVertical: 30,
    paddingHorizontal: 25,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContainer: {
    marginBottom: 20,
  },
  headerText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#112164', // Màu xanh đậm
    textAlign: 'center',
    letterSpacing: 2,
  },
  descriptionContainer: {
    marginBottom: 30,
    paddingHorizontal: 10,
  },
  descriptionText: {
    fontSize: 16,
    color: '#112164', // Màu xanh đậm
    textAlign: 'center',
    lineHeight: 22,
  },
  levelsContainer: {
    width: '100%',
    alignItems: 'center',
  },
  levelButton: {
    width: '80%',
    height: 50,
    backgroundColor: '#E8E8E8', // Màu xám nhạt cho button mặc định
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 8,
    borderWidth: 2,
    borderColor: '#D0D0D0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedLevelButton: {
    backgroundColor: '#4CAF50', // Màu xanh lá cây cho level được chọn
    borderColor: '#45A049',
  },
  lockedLevelButton: {
    backgroundColor: '#F0F0F0',
    borderColor: '#E0E0E0',
    opacity: 0.6,
  },
  levelButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
  },
  selectedLevelButtonText: {
    color: '#FFFFFF',
  },
  lockedLevelButtonText: {
    color: '#999999',
  },
  closeArea: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
});

export default ChooseLevelModal;
