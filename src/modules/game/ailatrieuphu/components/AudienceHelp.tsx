import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import * as Animatable from 'react-native-animatable';

interface AudienceHelpProps {
  visible: boolean;
  onClose: () => void;
  percentages: number[];
}

const {width} = Dimensions.get('window');

const AudienceHelp: React.FC<AudienceHelpProps> = ({
  visible,
  onClose,
  percentages,
}) => {
  const optionLetters = ['A', 'B', 'C', 'D'];

  return (
    <Modal visible={visible} transparent={true} animationType="fade">
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.title}>Ý kiến khán giả</Text>

          <View style={styles.chartContainer}>
            {percentages.map((percentage, index) => (
              <View key={index} style={styles.chartItemContainer}>
                <Text style={styles.optionLabel}>{optionLetters[index]}</Text>
                <View style={styles.barContainer}>
                  <Animatable.View
                    animation="slideInLeft"
                    duration={1000}
                    style={[
                      styles.bar,
                      {
                        width: `${percentage}%`,
                        backgroundColor: getBarColor(index),
                      },
                    ]}
                  />
                </View>
                <Text style={styles.percentageText}>{percentage}%</Text>
              </View>
            ))}
          </View>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Đóng</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

// Hàm lấy màu cho từng thanh
const getBarColor = (index: number) => {
  // Sử dụng màu sắc đồng bộ với màn hình chính
  const colors = ['#1BDB55', '#FF5757', '#FFA500', '#4C6EF5'];
  return colors[index % colors.length];
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  modalContent: {
    width: width - 60,
    backgroundColor: '#F1D1A6', // Màu nền giống màn hình chính
    borderRadius: 15,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#112164', // Màu xanh đậm như trong ảnh
    textAlign: 'center',
    marginBottom: 20,
  },
  chartContainer: {
    marginVertical: 15,
  },
  chartItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  optionLabel: {
    color: '#112164', // Màu xanh đậm như trong ảnh
    fontWeight: 'bold',
    fontSize: 18,
    width: 30,
    textAlign: 'center',
  },
  barContainer: {
    flex: 1,
    height: 30,
    backgroundColor: '#FCF8E8', // Màu nền nhạt giống câu hỏi
    borderRadius: 15,
    overflow: 'hidden',
    marginHorizontal: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  bar: {
    height: '100%',
    borderRadius: 15,
  },
  percentageText: {
    color: '#112164', // Màu xanh đậm như trong ảnh
    fontWeight: 'bold',
    fontSize: 16,
    width: 50,
    textAlign: 'right',
  },
  closeButton: {
    backgroundColor: '#FF5757', // Màu đỏ cam như header
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 25,
    alignSelf: 'center',
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 3,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
});

export default AudienceHelp;
