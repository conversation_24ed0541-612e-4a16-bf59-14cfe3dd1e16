import { Dimensions } from 'react-native';

// L<PERSON><PERSON> kích thước màn hình
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

import { SVGPathCalculator } from '../utils/svgPathCalculator';
import { milestonePositions } from './milestonePositions';

// Function để tạo SVG path từ milestonePositions hiện có
const generatePathFromMilestones = () => {
  if (milestonePositions.length === 0) return "";

  // Sắp xếp milestones theo thứ tự ID để đảm bảo đường đi đúng
  const sortedMilestones = [...milestonePositions].sort((a, b) => a.id - b.id);

  let pathData = "";

  sortedMilestones.forEach((milestone, index) => {
    if (index === 0) {
      // Điểm đầu tiên - MoveTo
      pathData += `M ${milestone.left} ${milestone.top}`;
    } else if (index === 1) {
      // Đo<PERSON>n đầu tiên - sử dụng Quadratic Bezier đơn giản
      const prevMilestone = sortedMilestones[index - 1];
      const currentMilestone = milestone;

      // Control point ở giữa 2 điểm, hơi lệch để tạo đường cong tự nhiên
      const midX = (prevMilestone.left + currentMilestone.left) / 2;
      const midY = (prevMilestone.top + currentMilestone.top) / 2;

      // Thêm một chút variation để đường cong tự nhiên hơn
      const offsetX = (currentMilestone.left - prevMilestone.left) * 0.2;
      const offsetY = (currentMilestone.top - prevMilestone.top) * 0.1;

      const controlX = midX + offsetX;
      const controlY = midY - Math.abs(offsetY); // Hơi cong lên trên

      pathData += ` Q ${controlX} ${controlY} ${currentMilestone.left} ${currentMilestone.top}`;
    } else {
      // Các đoạn tiếp theo - sử dụng Smooth Cubic Bezier
      const prevMilestone = sortedMilestones[index - 1];
      const currentMilestone = milestone;
      const nextMilestone = sortedMilestones[index + 1];

      // Tính toán control points dựa trên hướng của đường đi
      const deltaX = currentMilestone.left - prevMilestone.left;
      const deltaY = currentMilestone.top - prevMilestone.top;

      // Control point 1: từ điểm trước, theo hướng tự nhiên
      const cp1X = prevMilestone.left + deltaX * 0.4;
      const cp1Y = prevMilestone.top + deltaY * 0.2;

      // Control point 2: đến điểm hiện tại, xem xét điểm tiếp theo nếu có
      let cp2X, cp2Y;
      if (nextMilestone) {
        // Có điểm tiếp theo - tạo đường cong mượt hướng đến điểm đó
        const nextDeltaX = nextMilestone.left - currentMilestone.left;
        const nextDeltaY = nextMilestone.top - currentMilestone.top;

        cp2X = currentMilestone.left - nextDeltaX * 0.3;
        cp2Y = currentMilestone.top - nextDeltaY * 0.2;
      } else {
        // Điểm cuối - đường cong đơn giản
        cp2X = currentMilestone.left - deltaX * 0.3;
        cp2Y = currentMilestone.top - deltaY * 0.1;
      }

      pathData += ` C ${cp1X} ${cp1Y} ${cp2X} ${cp2Y} ${currentMilestone.left} ${currentMilestone.top}`;
    }
  });

  return pathData;
};

// Định nghĩa đường đi game "Ai là triệu phú" dựa trên milestonePositions
export const gamePathConfig = {
  // Đường đi được tạo từ milestonePositions hiện có
  pathData: generatePathFromMilestones(),

  // Các điểm milestone được tạo từ milestonePositions
  milestonePoints: milestonePositions.map((milestone, index) => ({
    id: milestone.id,
    pathProgress: index / (milestonePositions.length - 1), // Phân bố đều từ 0 đến 1
    name: milestone.levelName || `Chặng ${milestone.id}`,
    originalPosition: { top: milestone.top, left: milestone.left }
  })),

  // Cấu hình animation cho bird di chuyển
  birdAnimation: {
    speed: 2000, // ms để di chuyển giữa 2 milestone
    easing: 'ease-in-out',
  },

  // Cấu hình hiệu ứng
  effects: {
    trailOpacity: 0.3,
    glowRadius: 10,
    milestoneScale: 1.2,
  }
};

// Singleton instance của path calculator
let pathCalculatorInstance: SVGPathCalculator | null = null;

export const getGamePathCalculator = (): SVGPathCalculator => {
  if (!pathCalculatorInstance) {
    pathCalculatorInstance = new SVGPathCalculator(gamePathConfig.pathData);
  }
  return pathCalculatorInstance;
};

// Hàm tiện ích để lấy vị trí milestone - sử dụng vị trí gốc từ milestonePositions
export const getMilestonePositions = (containerWidth: number, containerHeight: number) => {
  // Trả về vị trí chính xác từ milestonePositions gốc
  return milestonePositions.map(milestone => {
    const x = milestone.left * containerWidth;
    const y = milestone.top * containerHeight;

    // Tính góc dựa trên vị trí với milestone tiếp theo (nếu có)
    const nextMilestone = milestonePositions.find(m => m.id === milestone.id + 1);
    let angle = 0;
    if (nextMilestone) {
      const deltaX = (nextMilestone.left - milestone.left) * containerWidth;
      const deltaY = (nextMilestone.top - milestone.top) * containerHeight;
      angle = Math.atan2(deltaY, deltaX);
    }

    return {
      id: milestone.id,
      name: milestone.levelName || `Chặng ${milestone.id}`,
      x: x,
      y: y,
      percentageX: milestone.left,
      percentageY: milestone.top,
      angle: angle,
      pathProgress: (milestone.id - 1) / (milestonePositions.length - 1), // 0 đến 1
    };
  });
};

// Hàm lấy đường path để vẽ trail - sử dụng SVG path từ milestonePositions
export const getPathTrail = (containerWidth: number, containerHeight: number, segments: number = 100) => {
  try {
    const calculator = getGamePathCalculator();
    const points = calculator.getEvenlySpacedPoints(segments);

    return points.map(point => ({
      x: point.x * containerWidth,
      y: point.y * containerHeight,
    }));
  } catch (error) {
    console.warn('Error generating path trail, using fallback:', error);

    // Fallback: tạo trail đơn giản từ milestonePositions
    const trail = [];
    for (let i = 0; i < milestonePositions.length - 1; i++) {
      const start = milestonePositions[i];
      const end = milestonePositions[i + 1];

      // Tạo các điểm trung gian giữa 2 milestones
      const segmentsPerMilestone = Math.floor(segments / (milestonePositions.length - 1));
      for (let j = 0; j <= segmentsPerMilestone; j++) {
        const t = j / segmentsPerMilestone;
        const x = (start.left + (end.left - start.left) * t) * containerWidth;
        const y = (start.top + (end.top - start.top) * t) * containerHeight;
        trail.push({ x, y });
      }
    }

    return trail;
  }
};

// Hàm tính vị trí bird animation giữa 2 milestone
export const getBirdPosition = (
  fromMilestoneId: number,
  toMilestoneId: number,
  progress: number, // 0-1
  containerWidth: number,
  containerHeight: number
) => {
  const fromMilestone = gamePathConfig.milestonePoints.find(m => m.id === fromMilestoneId);
  const toMilestone = gamePathConfig.milestonePoints.find(m => m.id === toMilestoneId);

  if (!fromMilestone || !toMilestone) {
    return { x: 0, y: 0, angle: 0 };
  }

  const startProgress = fromMilestone.pathProgress;
  const endProgress = toMilestone.pathProgress;
  const currentProgress = startProgress + (endProgress - startProgress) * progress;

  const calculator = getGamePathCalculator();
  const point = calculator.getPointAtPercentage(currentProgress);
  const angle = calculator.getAngleAtPercentage(currentProgress);

  return {
    x: point.x * containerWidth,
    y: point.y * containerHeight,
    angle: angle,
  };
};

// Hàm tính toán vị trí thực tế từ SVG path
export const calculatePositionFromPath = (pathProgress: number, containerWidth: number, containerHeight: number) => {
  // Simplified calculation - trong thực tế có thể dùng thư viện như react-native-svg
  const path = gamePath.pathData;

  // Tạm thời sử dụng interpolation đơn giản
  // Trong production nên dùng thư viện SVG path calculation
  const positions = [
    { x: 0.15, y: 0.85 },
    { x: 0.25, y: 0.7 },
    { x: 0.4, y: 0.55 },
    { x: 0.6, y: 0.4 },
    { x: 0.75, y: 0.25 },
    { x: 0.55, y: 0.15 },
    { x: 0.3, y: 0.05 },
  ];

  const index = Math.floor(pathProgress * (positions.length - 1));
  const position = positions[index] || positions[0];

  return {
    x: position.x * containerWidth,
    y: position.y * containerHeight,
  };
};

// Giải pháp 2: Responsive breakpoints
export const responsivePositions = {
  // Định nghĩa breakpoints
  breakpoints: {
    small: { width: 0, height: 0 },      // < 375px width
    medium: { width: 375, height: 667 }, // iPhone 6/7/8
    large: { width: 414, height: 896 },  // iPhone 11/XR
    xlarge: { width: 768, height: 1024 }, // iPad
  },

  // Vị trí cho từng breakpoint
  positions: {
    small: [
      { id: 1, top: 0.8, left: 0.1 },
      { id: 2, top: 0.65, left: 0.2 },
      { id: 3, top: 0.5, left: 0.35 },
      { id: 4, top: 0.35, left: 0.55 },
      { id: 5, top: 0.2, left: 0.7 },
      { id: 6, top: 0.1, left: 0.5 },
      { id: 7, top: 0.05, left: 0.25 },
    ],
    medium: [
      { id: 1, top: 0.75, left: 0.15 },
      { id: 2, top: 0.6, left: 0.25 },
      { id: 3, top: 0.45, left: 0.4 },
      { id: 4, top: 0.3, left: 0.6 },
      { id: 5, top: 0.15, left: 0.75 },
      { id: 6, top: 0.1, left: 0.55 },
      { id: 7, top: 0.05, left: 0.3 },
    ],
    large: [
      { id: 1, top: 0.75, left: 0.15 },
      { id: 2, top: 0.6, left: 0.25 },
      { id: 3, top: 0.45, left: 0.4 },
      { id: 4, top: 0.3, left: 0.6 },
      { id: 5, top: 0.15, left: 0.75 },
      { id: 6, top: 0.1, left: 0.55 },
      { id: 7, top: 0.05, left: 0.3 },
    ],
    xlarge: [
      { id: 1, top: 0.7, left: 0.2 },
      { id: 2, top: 0.55, left: 0.3 },
      { id: 3, top: 0.4, left: 0.45 },
      { id: 4, top: 0.25, left: 0.65 },
      { id: 5, top: 0.1, left: 0.8 },
      { id: 6, top: 0.05, left: 0.6 },
      { id: 7, top: 0.02, left: 0.35 },
    ],
  }
};

// Hàm lấy breakpoint hiện tại
export const getCurrentBreakpoint = () => {
  const { width, height } = Dimensions.get('window');

  if (width >= responsivePositions.breakpoints.xlarge.width) {
    return 'xlarge';
  } else if (width >= responsivePositions.breakpoints.large.width) {
    return 'large';
  } else if (width >= responsivePositions.breakpoints.medium.width) {
    return 'medium';
  } else {
    return 'small';
  }
};

// Giải pháp 3: Aspect ratio based positioning
export const aspectRatioPositions = {
  // Tính toán vị trí dựa trên tỷ lệ khung hình
  calculatePositions: (containerWidth: number, containerHeight: number) => {
    const aspectRatio = containerWidth / containerHeight;

    // Điều chỉnh vị trí dựa trên aspect ratio
    let basePositions = [
      { id: 1, top: 0.75, left: 0.15 },
      { id: 2, top: 0.6, left: 0.25 },
      { id: 3, top: 0.45, left: 0.4 },
      { id: 4, top: 0.3, left: 0.6 },
      { id: 5, top: 0.15, left: 0.75 },
      { id: 6, top: 0.1, left: 0.55 },
      { id: 7, top: 0.05, left: 0.3 },
    ];

    // Điều chỉnh dựa trên aspect ratio
    if (aspectRatio > 0.7) { // Màn hình rộng (tablet landscape)
      basePositions = basePositions.map(pos => ({
        ...pos,
        left: pos.left * 0.8 + 0.1, // Thu hẹp lại và căn giữa
      }));
    } else if (aspectRatio < 0.5) { // Màn hình dài (phone portrait)
      basePositions = basePositions.map(pos => ({
        ...pos,
        top: pos.top * 0.9 + 0.05, // Nén lại một chút
      }));
    }

    return basePositions;
  }
};

// Giải pháp 4: Grid-based positioning
export const gridPositions = {
  // Chia màn hình thành lưới và đặt milestone theo grid
  gridSize: { rows: 10, cols: 10 },

  milestoneGridPositions: [
    { id: 1, row: 8, col: 1 },  // Hàng 8, cột 1
    { id: 2, row: 6, col: 2 },  // Hàng 6, cột 2
    { id: 3, row: 4, col: 4 },  // Hàng 4, cột 4
    { id: 4, row: 3, col: 6 },  // Hàng 3, cột 6
    { id: 5, row: 1, col: 7 },  // Hàng 1, cột 7
    { id: 6, row: 0, col: 5 },  // Hàng 0, cột 5
    { id: 7, row: 0, col: 3 },  // Hàng 0, cột 3
  ],

  calculateGridPosition: (row: number, col: number, containerWidth: number, containerHeight: number) => {
    const cellWidth = containerWidth / gridPositions.gridSize.cols;
    const cellHeight = containerHeight / gridPositions.gridSize.rows;

    return {
      x: col * cellWidth + cellWidth / 2, // Căn giữa cell
      y: row * cellHeight + cellHeight / 2,
    };
  }
};
