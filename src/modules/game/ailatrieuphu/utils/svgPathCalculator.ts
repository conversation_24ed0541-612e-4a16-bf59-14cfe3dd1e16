// SVG Path Calculator - <PERSON><PERSON>h to<PERSON> vị trí trên đường đi SVG
export interface Point {
  x: number;
  y: number;
}

export interface PathSegment {
  type: 'M' | 'L' | 'Q' | 'C';
  points: Point[];
  controlPoints?: Point[];
}

export class SVGPathCalculator {
  private segments: PathSegment[] = [];
  private totalLength: number = 0;
  private lengthCache: number[] = [];

  constructor(pathData: string) {
    this.parsePathData(pathData);
    this.calculateLengths();
  }

  // Parse SVG path data thành các segments
  private parsePathData(pathData: string) {
    // Simplified parser cho các command cơ bản: M, L, Q, C
    const commands = pathData.match(/[MLQC][^MLQC]*/g) || [];
    
    commands.forEach(command => {
      const type = command[0] as 'M' | 'L' | 'Q' | 'C';
      const coords = command.slice(1).trim().split(/[\s,]+/).map(Number);
      
      switch (type) {
        case 'M': // MoveTo
          this.segments.push({
            type: 'M',
            points: [{ x: coords[0], y: coords[1] }]
          });
          break;
          
        case 'L': // LineTo
          this.segments.push({
            type: 'L',
            points: [{ x: coords[0], y: coords[1] }]
          });
          break;
          
        case 'Q': // Quadratic Bezier
          this.segments.push({
            type: 'Q',
            points: [{ x: coords[2], y: coords[3] }],
            controlPoints: [{ x: coords[0], y: coords[1] }]
          });
          break;
          
        case 'C': // Cubic Bezier
          this.segments.push({
            type: 'C',
            points: [{ x: coords[4], y: coords[5] }],
            controlPoints: [
              { x: coords[0], y: coords[1] },
              { x: coords[2], y: coords[3] }
            ]
          });
          break;
      }
    });
  }

  // Tính toán độ dài của từng segment
  private calculateLengths() {
    let currentPoint: Point = { x: 0, y: 0 };
    this.lengthCache = [];
    this.totalLength = 0;

    this.segments.forEach((segment, index) => {
      let segmentLength = 0;

      switch (segment.type) {
        case 'M':
          currentPoint = segment.points[0];
          break;
          
        case 'L':
          segmentLength = this.calculateLineLength(currentPoint, segment.points[0]);
          currentPoint = segment.points[0];
          break;
          
        case 'Q':
          segmentLength = this.calculateQuadraticLength(
            currentPoint, 
            segment.controlPoints![0], 
            segment.points[0]
          );
          currentPoint = segment.points[0];
          break;
          
        case 'C':
          segmentLength = this.calculateCubicLength(
            currentPoint,
            segment.controlPoints![0],
            segment.controlPoints![1],
            segment.points[0]
          );
          currentPoint = segment.points[0];
          break;
      }

      this.lengthCache.push(segmentLength);
      this.totalLength += segmentLength;
    });
  }

  // Tính độ dài đường thẳng
  private calculateLineLength(p1: Point, p2: Point): number {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  }

  // Tính độ dài Quadratic Bezier (xấp xỉ)
  private calculateQuadraticLength(start: Point, control: Point, end: Point): number {
    const steps = 20;
    let length = 0;
    let prevPoint = start;

    for (let i = 1; i <= steps; i++) {
      const t = i / steps;
      const point = this.getQuadraticPoint(start, control, end, t);
      length += this.calculateLineLength(prevPoint, point);
      prevPoint = point;
    }

    return length;
  }

  // Tính độ dài Cubic Bezier (xấp xỉ)
  private calculateCubicLength(start: Point, control1: Point, control2: Point, end: Point): number {
    const steps = 20;
    let length = 0;
    let prevPoint = start;

    for (let i = 1; i <= steps; i++) {
      const t = i / steps;
      const point = this.getCubicPoint(start, control1, control2, end, t);
      length += this.calculateLineLength(prevPoint, point);
      prevPoint = point;
    }

    return length;
  }

  // Lấy điểm trên Quadratic Bezier curve
  private getQuadraticPoint(start: Point, control: Point, end: Point, t: number): Point {
    const mt = 1 - t;
    return {
      x: mt * mt * start.x + 2 * mt * t * control.x + t * t * end.x,
      y: mt * mt * start.y + 2 * mt * t * control.y + t * t * end.y
    };
  }

  // Lấy điểm trên Cubic Bezier curve
  private getCubicPoint(start: Point, control1: Point, control2: Point, end: Point, t: number): Point {
    const mt = 1 - t;
    const mt2 = mt * mt;
    const mt3 = mt2 * mt;
    const t2 = t * t;
    const t3 = t2 * t;

    return {
      x: mt3 * start.x + 3 * mt2 * t * control1.x + 3 * mt * t2 * control2.x + t3 * end.x,
      y: mt3 * start.y + 3 * mt2 * t * control1.y + 3 * mt * t2 * control2.y + t3 * end.y
    };
  }

  // Lấy vị trí tại % cụ thể trên đường path
  public getPointAtPercentage(percentage: number): Point {
    if (percentage <= 0) return this.segments[0]?.points[0] || { x: 0, y: 0 };
    if (percentage >= 1) return this.segments[this.segments.length - 1]?.points[0] || { x: 0, y: 0 };

    const targetLength = this.totalLength * percentage;
    let currentLength = 0;
    let currentPoint: Point = { x: 0, y: 0 };

    for (let i = 0; i < this.segments.length; i++) {
      const segment = this.segments[i];
      const segmentLength = this.lengthCache[i];

      if (currentLength + segmentLength >= targetLength) {
        // Điểm nằm trong segment này
        const segmentProgress = (targetLength - currentLength) / segmentLength;
        return this.getPointInSegment(segment, currentPoint, segmentProgress);
      }

      currentLength += segmentLength;
      if (segment.type !== 'M') {
        currentPoint = segment.points[0];
      }
    }

    return currentPoint;
  }

  // Lấy điểm trong một segment cụ thể
  private getPointInSegment(segment: PathSegment, startPoint: Point, progress: number): Point {
    switch (segment.type) {
      case 'M':
        return segment.points[0];
        
      case 'L':
        return {
          x: startPoint.x + (segment.points[0].x - startPoint.x) * progress,
          y: startPoint.y + (segment.points[0].y - startPoint.y) * progress
        };
        
      case 'Q':
        return this.getQuadraticPoint(
          startPoint,
          segment.controlPoints![0],
          segment.points[0],
          progress
        );
        
      case 'C':
        return this.getCubicPoint(
          startPoint,
          segment.controlPoints![0],
          segment.controlPoints![1],
          segment.points[0],
          progress
        );
        
      default:
        return startPoint;
    }
  }

  // Lấy hướng (angle) tại % cụ thể trên đường path
  public getAngleAtPercentage(percentage: number): number {
    const delta = 0.01; // 1% để tính gradient
    const point1 = this.getPointAtPercentage(Math.max(0, percentage - delta));
    const point2 = this.getPointAtPercentage(Math.min(1, percentage + delta));
    
    return Math.atan2(point2.y - point1.y, point2.x - point1.x);
  }

  // Lấy nhiều điểm đều đặn trên path
  public getEvenlySpacedPoints(count: number): Point[] {
    const points: Point[] = [];
    for (let i = 0; i < count; i++) {
      const percentage = i / (count - 1);
      points.push(this.getPointAtPercentage(percentage));
    }
    return points;
  }
}
