import { useState, useEffect, useMemo } from 'react';
import { Dimensions } from 'react-native';
import { getMilestonePositions, getPathTrail, getBirdPosition } from '../data/pathPositions';

interface ContainerDimensions {
  width: number;
  height: number;
}

interface MilestonePosition {
  id: number;
  name: string;
  x: number;
  y: number;
  percentageX: number;
  percentageY: number;
  angle: number;
  pathProgress: number;
}

interface PathPoint {
  x: number;
  y: number;
}

export const useSVGPath = (containerDimensions?: ContainerDimensions) => {
  const [screenDimensions, setScreenDimensions] = useState(Dimensions.get('window'));

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenDimensions(window);
    });

    return () => subscription?.remove();
  }, []);

  // Sử dụng container dimensions hoặc screen dimensions
  const dimensions = useMemo(() => {
    return containerDimensions || screenDimensions;
  }, [containerDimensions, screenDimensions]);

  // Tính toán vị trí milestones
  const milestonePositions = useMemo(() => {
    return getMilestonePositions(dimensions.width, dimensions.height);
  }, [dimensions.width, dimensions.height]);

  // Tính toán đường path trail
  const pathTrail = useMemo(() => {
    return getPathTrail(dimensions.width, dimensions.height, 100); // 100 segments cho đường mượt
  }, [dimensions.width, dimensions.height]);

  // Hàm lấy vị trí bird animation
  const getBirdAnimationPosition = (
    fromMilestoneId: number,
    toMilestoneId: number,
    progress: number
  ) => {
    return getBirdPosition(
      fromMilestoneId,
      toMilestoneId,
      progress,
      dimensions.width,
      dimensions.height
    );
  };

  // Hàm lấy milestone theo ID
  const getMilestoneById = (id: number): MilestonePosition | undefined => {
    return milestonePositions.find(m => m.id === id);
  };

  // Hàm lấy milestone tiếp theo
  const getNextMilestone = (currentId: number): MilestonePosition | undefined => {
    return milestonePositions.find(m => m.id === currentId + 1);
  };

  // Hàm lấy milestone trước đó
  const getPreviousMilestone = (currentId: number): MilestonePosition | undefined => {
    return milestonePositions.find(m => m.id === currentId - 1);
  };

  return {
    milestonePositions,
    pathTrail,
    dimensions,
    getBirdAnimationPosition,
    getMilestoneById,
    getNextMilestone,
    getPreviousMilestone,
  };
};

// Hook cho animation bird
export const useBirdAnimation = (
  fromMilestoneId: number,
  toMilestoneId: number,
  duration: number = 2000,
  containerDimensions?: ContainerDimensions
) => {
  const [progress, setProgress] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const { getBirdAnimationPosition } = useSVGPath(containerDimensions);

  const birdPosition = useMemo(() => {
    return getBirdAnimationPosition(fromMilestoneId, toMilestoneId, progress);
  }, [fromMilestoneId, toMilestoneId, progress, getBirdAnimationPosition]);

  const startAnimation = () => {
    setIsAnimating(true);
    setProgress(0);

    const startTime = Date.now();
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min(elapsed / duration, 1);
      
      setProgress(newProgress);

      if (newProgress < 1) {
        requestAnimationFrame(animate);
      } else {
        setIsAnimating(false);
      }
    };

    requestAnimationFrame(animate);
  };

  const stopAnimation = () => {
    setIsAnimating(false);
  };

  const resetAnimation = () => {
    setProgress(0);
    setIsAnimating(false);
  };

  return {
    birdPosition,
    progress,
    isAnimating,
    startAnimation,
    stopAnimation,
    resetAnimation,
  };
};

// Hook để tạo hiệu ứng trail (đường đi đã đi qua)
export const usePathTrail = (
  completedMilestones: number[],
  containerDimensions?: ContainerDimensions
) => {
  const { milestonePositions, pathTrail } = useSVGPath(containerDimensions);

  const completedTrail = useMemo(() => {
    if (completedMilestones.length === 0) return [];

    const maxCompletedId = Math.max(...completedMilestones);
    const maxMilestone = milestonePositions.find(m => m.id === maxCompletedId);
    
    if (!maxMilestone) return [];

    // Lấy phần trail từ đầu đến milestone đã hoàn thành
    const maxProgress = maxMilestone.pathProgress;
    const totalPoints = pathTrail.length;
    const completedPoints = Math.floor(totalPoints * maxProgress);

    return pathTrail.slice(0, completedPoints + 1);
  }, [completedMilestones, milestonePositions, pathTrail]);

  return {
    completedTrail,
    fullTrail: pathTrail,
  };
};

// Hook để responsive scaling
export const usePathScaling = (
  baseWidth: number = 375,
  baseHeight: number = 667,
  containerDimensions?: ContainerDimensions
) => {
  const { dimensions } = useSVGPath(containerDimensions);

  const scaleFactor = useMemo(() => {
    const widthScale = dimensions.width / baseWidth;
    const heightScale = dimensions.height / baseHeight;
    return Math.min(widthScale, heightScale); // Giữ tỷ lệ
  }, [dimensions.width, dimensions.height, baseWidth, baseHeight]);

  const scaleValue = (value: number) => value * scaleFactor;

  return {
    scaleFactor,
    scaleValue,
    dimensions,
  };
};
