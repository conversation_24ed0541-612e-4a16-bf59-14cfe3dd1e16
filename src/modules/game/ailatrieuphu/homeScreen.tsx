import React, {useRef, useState, useEffect, useMemo} from 'react';
import {
  Image,
  ImageBackground,
  StyleSheet,
  TouchableWithoutFeedback,
  TouchableOpacity,
  View,
  Dimensions,
  Animated,
  Alert,
  PixelRatio,
  Platform,
} from 'react-native';
import {Text} from 'react-native-paper';
import {Winicon} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {gameAction, setCurrentMilestoneId} from './redux/gameSlice';
import {GameStatus} from '../../../Config/Contanst';
import ConfigAPI from '../../../Config/ConfigAPI';
import {milestonePositions} from './data/milestonePositions';
import StartGameModal from './components/StartGameModal';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Ult<PERSON>} from '../../../utils/Utils';
import FastImage from 'react-native-fast-image';
import {LoadingUI} from '../../../features/loading';
import ChooseLevelModal from './components/ChooseLevelModal';

// Responsive utilities
const {width: SCREEN_WIDTH, height: SCREEN_HEIGHT} = Dimensions.get('window');

// Scale based on screen size
const scale = SCREEN_WIDTH / 375; // 375 is standard width

// Normalize font size for different screen densities
export function normalize(size: number): number {
  const newSize = size * scale;
  if (Platform.OS === 'ios') {
    return Math.round(PixelRatio.roundToNearestPixel(newSize));
  }
  return Math.round(PixelRatio.roundToNearestPixel(newSize)) - 2;
}

// Responsive width and height
export function wp(percentage: number): number {
  return (percentage * SCREEN_WIDTH) / 100;
}

export function hp(percentage: number): number {
  return (percentage * SCREEN_HEIGHT) / 100;
}

// Định nghĩa interface cho milestone
// interface Milestone {
//   id: number;
//   top: number;
//   left: number;
//   status: 'completed' | 'in-progress' | 'locked';
//   levelName?: string;
// }

// Milestone config: tọa độ và trạng thái
// Điều chỉnh vị trí để các cọc nằm chính xác trên đường đi như trong ảnh
// Giá trị top bây giờn là tỷ lệ trong khu vực hiển thị (không bao gồm header và footer)
// Mảng này chỉ chứa thông tin vị trí, trạng thái sẽ được lấy từ Redux
// Đã chuyển định nghĩa này sang file data/milestonePositions.ts

// Trả về ảnh milestone theo trạng thái
const getMilestoneImage = (status: string) => {
  switch (status) {
    case 'completed':
      return require('./assets/pass.png');
    case 'in-progress':
      return require('./assets/Inpro.png');
    default:
      return require('./assets/New.png');
  }
};

// Tính scale theo vị trí top: càng xa thì càng nhỏ
const calculateScale = (top: number) => {
  const minTop = 0.55; // xa nhất (trên cao) - điều chỉnh theo milestone mới
  const maxTop = 0.85; // gần nhất (dưới màn hình) - điều chỉnh theo milestone mới

  // Điều chỉnh scale dựa trên kích thước màn hình
  const baseScale = SCREEN_WIDTH / 375; // Tỷ lệ so với màn hình chuẩn
  const minScale = 0.6 * baseScale; // Tăng minScale để cọc xa không quá nhỏ
  const maxScale = 1.1 * baseScale; // Giảm maxScale để cọc gần không quá lớn

  const normalized = (top - minTop) / (maxTop - minTop);
  return minScale + normalized * (maxScale - minScale); // top lớn => scale lớn
};

// Định nghĩa interface cho props của Milestone component
interface MilestoneProps {
  status: 'completed' | 'in-progress' | 'locked';
  number: number;
  levelName?: string;
  onPress: () => void;
  scaleFactor: number;
}

// Milestone component
const MilestoneComponent: React.FC<MilestoneProps> = ({
  status,
  // Không sử dụng number nên thêm dấu gạch dưới
  number: _number,
  onPress,
  scaleFactor,
}) => {
  const scaleAnim = useRef(new Animated.Value(scaleFactor)).current;
  const [isAnimating, setIsAnimating] = useState(false);

  const handlePress = () => {
    if (isAnimating) {
      return; // Ngăn chặn nhấn nhiều lần liên tiếp
    }

    setIsAnimating(true);
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: scaleFactor * 1.2,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: scaleFactor,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsAnimating(false);
      onPress();
    });
  };

  return (
    <View style={{position: 'relative'}}>
      <TouchableWithoutFeedback onPress={handlePress}>
        <Animated.View
          style={[styles.milestoneContent, {transform: [{scale: scaleAnim}]}]}>
          {/* Hiển thị ảnh milestone */}
          <FastImage
            source={getMilestoneImage(status)}
            style={styles.milestoneImage}
            resizeMode={FastImage.resizeMode.contain}
          />
          {/* Hiển thị số trên milestone */}
          <Text style={styles.milestoneNumber}>{_number}</Text>
          {/* {levelName && (
          <View style={styles.levelName}>
            <Text style={styles.levelNameText}>{levelName}</Text>
          </View>
        )} */}
        </Animated.View>
      </TouchableWithoutFeedback>
    </View>
  );
};

const precalculatedScaleFactors = milestonePositions.map(m =>
  calculateScale(m.top),
);

const HomeScreen = () => {
  const dispatch: AppDispatch = useDispatch();
  const gameState = useSelector((state: RootState) => state.game);

  // Lấy dữ liệu milestone từ API khi component mount hoặc khi focus
  useEffect(() => {
    dispatch(gameAction.getMilestones(ConfigAPI.gameALTP)); // 'ALTP' là ID của game
  }, [dispatch]);

  // Chuyển đổi dữ liệu từ Redux sang định dạng UI
  const milestones = useMemo(() => {
    if (gameState.loading) {
      return milestonePositions.map(pos => ({
        ...pos,
        status: 'locked' as const,
      }));
    }

    if (!gameState.Milestone || gameState.Milestone.length === 0) {
      return milestonePositions.map(pos => ({
        ...pos,
        status: pos.id === 1 ? ('in-progress' as const) : ('locked' as const),
      }));
    }

    return milestonePositions.map(pos => {
      const milestone = gameState.Milestone.find(m => m.id === pos.id);
      return {
        ...pos,
        status:
          milestone?.status === GameStatus.Completed
            ? ('completed' as const)
            : milestone?.status === GameStatus.Pending
            ? ('in-progress' as const)
            : ('locked' as const),
      };
    });
  }, [gameState.Milestone, gameState.loading]);

  const [orientation, setOrientation] = useState({
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
  });

  // State để quản lý hiển thị modal
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMilestone, setSelectedMilestone] = useState<{
    number: number;
    levelName?: string;
  } | null>(null);

  // State để quản lý hiển thị ChooseLevelModal
  const [showLevelModal, setShowLevelModal] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState('N5');
  const [unlockedLevels] = useState(['N5']); // Có thể lấy từ API hoặc Redux

  // Xử lý thay đổi kích thước màn hình (xoay màn hình)
  useEffect(() => {
    // Tối ưu hóa hàm updateLayout bằng cách sử dụng useCallback
    const updateLayout = () => {
      const {width, height} = Dimensions.get('window');
      // Chỉ cập nhật state nếu kích thước thực sự thay đổi
      if (width !== orientation.width || height !== orientation.height) {
        setOrientation({width, height});
      }
    };

    const dimensionsHandler = Dimensions.addEventListener(
      'change',
      updateLayout,
    );

    return () => {
      // Cleanup
      dimensionsHandler.remove();
    };
  }, [orientation.width, orientation.height]);

  // Xử lý khi nhấn vào milestone - sử dụng useMemo để tránh tạo lại hàm này mỗi khi render
  const handleMilestonePress = useMemo(() => {
    return (status: string, number: number, levelName?: string) => {
      if (status === 'locked') {
        Alert.alert(
          `Chặng ${number}: ${levelName}`,
          'Bạn cần hoàn thành các chặng trước để mở khóa chặng này.',
        );
      } else {
        // Nếu là 'completed' hoặc 'in-progress', hiển thị modal
        setSelectedMilestone({number, levelName});

        // Đặt milestone hiện tại trong Redux
        dispatch(setCurrentMilestoneId(number));

        setModalVisible(true);
      }
    };
  }, [dispatch]);

  // Hàm đóng modal
  const closeModal = () => {
    setModalVisible(false);
  };

  // Hàm xử lý khi bấm vào nút "Trình độ"
  const showLevelSelection = () => {
    setShowLevelModal(true);
  };

  // Hàm xử lý khi chọn level
  const handleSelectLevel = (level: string) => {
    setSelectedLevel(level);
    console.log('Selected level:', level);
    // TODO: Có thể thêm logic lưu level đã chọn vào AsyncStorage hoặc Redux
    // TODO: Có thể thêm logic chuyển đến màn hình game với level đã chọn
  };

  return (
    <View style={styles.container}>
      <View style={styles.mainContainer}>
        <ImageBackground
          source={require('./assets/backgroundGame.png')}
          style={styles.backgroundImage}
          resizeMode="cover">
          <SafeAreaView style={styles.contentContainer}>
            <Header selectedLevel={selectedLevel} />

            {/* Sử dụng đường đi có sẵn trong background */}
            <View style={styles.milestonesContainer}>
              {gameState.loading ? (
                <LoadingUI isLoading={gameState.loading} />
              ) : (
                milestones.map((m, index) => {
                  // Sử dụng scaleFactor đã được tính trước
                  const scaleFactor = precalculatedScaleFactors[index];

                  // Tính toán vị trí responsive - chỉ trong phạm vi của ImageBackground
                  // Lấy kích thước của ImageBackground thay vì toàn màn hình
                  const footerHeight = hp(-4); // Ước tính chiều cao của footer
                  const headerHeight = hp(6); // Ước tính chiều cao của header

                  // Chiều cao thực tế của khu vực hiển thị milestone
                  const backgroundHeight = orientation.height - footerHeight;
                  const backgroundWidth = orientation.width;

                  // Điều chỉnh vị trí top để tính từ sau header
                  const adjustedTop =
                    headerHeight + m.top * (backgroundHeight - headerHeight);
                  const leftPosition = m.left * backgroundWidth;

                  return (
                    <View key={m.id}>
                      {/* Hiển thị bird trên milestone đang in-progress */}
                      {m.status === 'in-progress' && (
                        <View
                          style={{
                            position: 'absolute',
                            top: adjustedTop - hp(9),
                            left: leftPosition - wp(5.5),
                            zIndex: 999, // Đảm bảo hiển thị phía trên
                          }}>
                          <Image
                            source={require('./assets/bird-step.png')}
                            style={{
                              width: wp(46) * scaleFactor,
                              height: wp(46) * scaleFactor,
                              aspectRatio: 1.7,
                              position: 'absolute',
                              top: -wp(43) * scaleFactor,
                              left: -wp(12) * scaleFactor,
                            }}
                            resizeMode="contain"
                          />
                        </View>
                      )}
                      <View
                        style={[
                          styles.milestone,
                          {
                            top: adjustedTop,
                            left: leftPosition,
                            // Điều chỉnh scale và vị trí để milestone nằm đúng trên đường đi
                            transform: [
                              {translateX: -wp(6)},
                              {translateY: -hp(7.5)},
                              {scale: scaleFactor},
                            ],
                          },
                        ]}>
                        <MilestoneComponent
                          status={m.status}
                          number={m.id}
                          levelName={m.levelName}
                          onPress={() =>
                            handleMilestonePress(m.status, m.id, m.levelName)
                          }
                          scaleFactor={scaleFactor}
                        />
                      </View>
                    </View>
                  );
                })
              )}
            </View>
          </SafeAreaView>
        </ImageBackground>

        {/* Footer tách riêng ra khỏi background */}
        <Footer onShowLevelModal={showLevelSelection} />

        {/* Modal hiển thị khi nhấn vào milestone */}
        <StartGameModal
          visible={modalVisible}
          onClose={closeModal}
          selectedMilestone={selectedMilestone}
        />

        {/* ChooseLevelModal */}
        <ChooseLevelModal
          visible={showLevelModal}
          onClose={() => setShowLevelModal(false)}
          onSelectLevel={handleSelectLevel}
          selectedLevel={selectedLevel}
          unlockedLevels={unlockedLevels}
        />
      </View>
    </View>
  );
};

// Header
const Header = ({selectedLevel}: {selectedLevel: string}) => {
  const navigation = useNavigation<any>();

  return (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <TouchableOpacity
          style={{flexDirection: 'row', alignItems: 'center'}}
          onPress={() => navigation.goBack()}>
          <Winicon src="outline/arrows/left-arrow" size={20} color="#fff" />
          <Text style={styles.title}>Ai là triệu phú</Text>
        </TouchableOpacity>

        {/* Level Display */}
        <View style={styles.levelDisplay}>
          <Image
            source={require('./assets/level.png')}
            style={styles.levelIcon}
          />
          <Text style={styles.levelText}>{selectedLevel}</Text>
        </View>
      </View>
    </View>
  );
};

// Footer

const Footer = ({onShowLevelModal}: {onShowLevelModal: () => void}) => {
  const customer = useSelector((state: RootState) => state.customer.data);

  const navigation = useNavigation<any>();

  const showRanking = () => {
    navigation.navigate('GameRanking');
  };

  const showHelp = () => {
    Alert.alert(
      'Hướng dẫn',
      'Chọn một chặng để bắt đầu chơi. Trả lời đúng các câu hỏi để mở khóa các chặng tiếp theo.',
    );
  };

  return (
    <View style={styles.footer}>
      <View style={styles.gem}>
        <Image
          source={require('./assets/coin-icon.png')}
          style={styles.gemIcon}
        />
        <Text style={styles.gemText}>{Ultis.money(customer.Rank || 0)}</Text>
      </View>

      <TouchableOpacity onPress={showRanking} style={styles.rankContainer}>
        <Image
          source={require('./assets/rank.png')}
          style={styles.rankIcon}
          // Thay bằng icon thực tế
        />
        <Text style={styles.footerText}>Xếp hạng</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={onShowLevelModal} style={styles.rankContainer}>
        <Image
          source={require('./assets/level.png')}
          style={styles.rankIcon}
          // Thay bằng icon thực tế
        />
        <Text style={styles.footerText}>Trình độ</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={showHelp} style={styles.rankContainer}>
        <Image
          source={require('./assets/hd.png')}
          style={styles.rankIcon}
          // Thay bằng icon thực tế
        />
        <Text style={styles.footerText}>Hướng dẫn</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  mainContainer: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
  },
  contentContainer: {
    flex: 1,
    position: 'relative',
  },
  milestonesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    // Đảm bảo container này chỉ nằm trong phạm vi của ImageBackground
  },
  milestoneContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  milestoneImage: {
    width: wp(18), // Tăng kích thước
    height: hp(18), // Tăng kích thước
    resizeMode: 'contain',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(4),
    paddingTop: hp(1.5),
    paddingBottom: hp(1),
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'column',
  },
  title: {
    fontSize: normalize(18),
    fontWeight: 'bold',
    marginLeft: wp(3),
    color: '#fff',
  },
  levelDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FCF8E8',
    borderRadius: wp(5),
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.5),
    marginTop: hp(2),
    marginLeft: wp(1),
    alignSelf: 'flex-start',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  levelIcon: {
    width: wp(5),
    height: wp(5),
    marginRight: wp(1.5),
    tintColor: '#FF6B35', // Màu cam/đỏ như trong hình
  },
  levelText: {
    fontSize: normalize(16),
    fontWeight: 'bold',
    color: '#333333',
  },

  milestone: {
    position: 'absolute',
    width: wp(12), // Kích thước phù hợp
    height: hp(15),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10, // Đảm bảo milestone hiển thị trên đường đi
    // Transform được áp dụng trong inline style
  },
  // Style cho các trạng thái khác nhau của milestone
  completedMilestone: {
    opacity: 1,
  },
  activeMilestone: {
    opacity: 1,
    // Có thể thêm hiệu ứng nổi bật cho milestone đang active
  },
  lockedMilestone: {
    opacity: 1,
  },
  loader: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{translateX: -wp(5)}, {translateY: -wp(5)}],
  },
  // Thêm style cho số trên milestone
  milestoneNumber: {
    fontSize: wp(6),
    fontWeight: 'bold',
    color: '#112164',
    textAlign: 'center',
    position: 'absolute',
    zIndex: 15,
    // Adjust position based on the milestone image
    top: Platform.OS === 'ios' ? '20%' : '15%',
    left: Platform.OS === 'ios' ? '47%' : '45%',
    transform: [{translateX: -wp(2)}, {translateY: -hp(0.5)}],
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: '#FF5A5F',
    paddingVertical: hp(1.5),
    paddingBottom: 32,
    width: '100%',
  },
  gem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: wp(2.5),
    paddingVertical: hp(0.6),
    borderRadius: wp(4),
  },
  gemIcon: {
    width: wp(6),
    height: wp(6),
    marginRight: wp(1.5),
  },
  gemText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: normalize(14),
  },
  footerText: {
    color: '#fff',
    fontWeight: 'bold',
    paddingHorizontal: wp(2.5),
    paddingVertical: hp(0.6),
    fontSize: normalize(14),
  },
  levelName: {
    position: 'absolute',
    bottom: hp(-2.5),
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: wp(2),
    paddingVertical: hp(0.3),
    borderRadius: wp(2.5),
  },
  levelNameText: {
    color: '#fff',
    fontSize: normalize(10),
    fontWeight: 'bold',
  },
  rankContainer: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  rankIcon: {
    width: 20,
    height: 20,
  },
});

export default HomeScreen;
