import React from 'react';
import {View, Text} from 'react-native';
import {Image} from 'react-native';

const Lives = ({
  totalLives,
  currentLives,
}: {
  totalLives: number;
  currentLives: number;
}) => {
  return (
    <View style={{flexDirection: 'row'}}>
      {Array.from({length: totalLives}, (_, i) => (
        <View key={i}>
          {i < currentLives ? (
            <Image source={require('../assets/heart.png')}></Image>
          ) : (
            <Image source={require('../assets/heart_empty.png')}></Image>
          )}
        </View>
      ))}
    </View>
  );
};

export default Lives;
