import {Text, View, StyleSheet} from 'react-native';

export const CardTitleGame = ({title}: {title: string}) => {
  return (
    <View style={styles.container}>
      <View style={styles.instruction}>
        <Text style={styles.wordText}>{title || ''}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  instruction: {
    backgroundColor: '#FCF8E8',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
  },
  wordText: {
    fontSize: 16,
    color: '#333',
    fontWeight: 'bold',
    textAlign: 'center',
  },
});
