// Background-Path Mapping System
// <PERSON><PERSON><PERSON><PERSON> pháp cho việc đồng bộ background image với path positions

export interface BackgroundPathMapping {
  gameId: string;
  backgroundImage: any;
  pathExtractionMethod: 'manual' | 'traced' | 'generated';

  // Manual method: Define positions manually
  manualPositions?: Array<{
    id: number;
    top: number;
    left: number;
    levelName?: string;
  }>;

  // Traced method: Extract from background image
  tracedPath?: {
    // SVG path data extracted from background
    pathData: string;
    // Reference points on the background
    referencePoints: Array<{
      id: number;
      pathProgress: number; // 0-1 position on the path
      levelName?: string;
    }>;
  };

  // Generated method: Algorithmic generation
  generatedPath?: {
    style: 'spiral' | 'zigzag' | 'wave' | 'mountain';
    startPoint: { x: number; y: number };
    endPoint: { x: number; y: number };
    milestoneCount: number;
    parameters: Record<string, any>;
  };
}

// Background-Path mappings for different games
export const BACKGROUND_PATH_MAPPINGS: Record<string, BackgroundPathMapping> = {
  'ALTP': {
    gameId: 'ALTP',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'),
    pathExtractionMethod: 'manual',
    manualPositions: [
      {id: 1, top: 0.75, left: 0.5, levelName: 'Cơ bản'},
      {id: 2, top: 0.68, left: 0.7, levelName: 'Dễ'},
      {id: 3, top: 0.59, left: 0.55, levelName: 'Trung bình'},
      {id: 4, top: 0.57, left: 0.37, levelName: 'Khó'},
      {id: 5, top: 0.50, left: 0.55, levelName: 'Rất khó'},
      {id: 6, top: 0.48, left: 0.4, levelName: 'Chuyên gia'},
      {id: 7, top: 0.44, left: 0.5, levelName: 'Bậc thầy'},
    ],
  },

  'SAKUTB': {
    gameId: 'SAKUTB',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'), // Tạm dùng ALTP background
    pathExtractionMethod: 'manual', // Chuyển sang manual để dễ test
    manualPositions: [
      {id: 1, top: 0.8, left: 0.2, levelName: 'Khởi đầu'},
      {id: 2, top: 0.65, left: 0.8, levelName: 'Cơ bản'},
      {id: 3, top: 0.5, left: 0.3, levelName: 'Nâng cao'},
      {id: 4, top: 0.35, left: 0.7, levelName: 'Khó'},
      {id: 5, top: 0.2, left: 0.4, levelName: 'Chuyên gia'},
      {id: 6, top: 0.1, left: 0.6, levelName: 'Bậc thầy'},
    ],
  },

  'DHBC': {
    gameId: 'DHBC',
    backgroundImage: require('../ailatrieuphu/assets/backgroundGame.png'), // Tạm dùng ALTP background
    pathExtractionMethod: 'generated',
    generatedPath: {
      style: 'spiral',
      startPoint: { x: 0.5, y: 0.9 },
      endPoint: { x: 0.5, y: 0.1 },
      milestoneCount: 7,
      parameters: {
        spiralTurns: 2,
        radiusDecay: 0.8,
        direction: 'clockwise',
      },
    },
  },
};

// Path extraction utilities
export class BackgroundPathExtractor {
  // Extract positions using manual method
  static extractManualPositions(mapping: BackgroundPathMapping) {
    if (mapping.pathExtractionMethod !== 'manual' || !mapping.manualPositions) {
      throw new Error('Invalid mapping for manual extraction');
    }
    return mapping.manualPositions;
  }

  // Extract positions using traced method
  static extractTracedPositions(
    mapping: BackgroundPathMapping,
    containerWidth: number,
    containerHeight: number
  ) {
    if (mapping.pathExtractionMethod !== 'traced' || !mapping.tracedPath) {
      throw new Error('Invalid mapping for traced extraction');
    }

    const { pathData, referencePoints } = mapping.tracedPath;

    // Use SVG path calculator to get positions
    try {
      const calculator = new (require('../ailatrieuphu/utils/svgPathCalculator').SVGPathCalculator)(pathData);

      return referencePoints.map(ref => {
        const point = calculator.getPointAtPercentage(ref.pathProgress);
        return {
          id: ref.id,
          top: point.y,
          left: point.x,
          levelName: ref.levelName,
          // Absolute positions
          x: point.x * containerWidth,
          y: point.y * containerHeight,
        };
      });
    } catch (error) {
      console.error('Error extracting traced positions:', error);
      // Fallback to evenly distributed positions
      return referencePoints.map((ref, index) => ({
        id: ref.id,
        top: 0.9 - (index * 0.8) / (referencePoints.length - 1),
        left: 0.5,
        levelName: ref.levelName,
        x: 0.5 * containerWidth,
        y: (0.9 - (index * 0.8) / (referencePoints.length - 1)) * containerHeight,
      }));
    }
  }

  // Generate positions using algorithmic method
  static generateAlgorithmicPositions(
    mapping: BackgroundPathMapping,
    containerWidth: number,
    containerHeight: number
  ) {
    if (mapping.pathExtractionMethod !== 'generated' || !mapping.generatedPath) {
      throw new Error('Invalid mapping for generated extraction');
    }

    const { style, startPoint, endPoint, milestoneCount, parameters } = mapping.generatedPath;

    switch (style) {
      case 'spiral':
        return this.generateSpiralPositions(startPoint, endPoint, milestoneCount, parameters, containerWidth, containerHeight);
      case 'zigzag':
        return this.generateZigzagPositions(startPoint, endPoint, milestoneCount, parameters, containerWidth, containerHeight);
      case 'wave':
        return this.generateWavePositions(startPoint, endPoint, milestoneCount, parameters, containerWidth, containerHeight);
      case 'mountain':
        return this.generateMountainPositions(startPoint, endPoint, milestoneCount, parameters, containerWidth, containerHeight);
      default:
        throw new Error(`Unknown generation style: ${style}`);
    }
  }

  private static generateSpiralPositions(
    startPoint: { x: number; y: number },
    endPoint: { x: number; y: number },
    milestoneCount: number,
    parameters: any,
    containerWidth: number,
    containerHeight: number
  ) {
    const { spiralTurns = 2, radiusDecay = 0.8, direction = 'clockwise' } = parameters;
    const positions = [];

    for (let i = 0; i < milestoneCount; i++) {
      const progress = i / (milestoneCount - 1);
      const angle = (direction === 'clockwise' ? 1 : -1) * spiralTurns * 2 * Math.PI * progress;
      const radius = (1 - progress * radiusDecay) * 0.3; // Max radius 30% of screen

      const centerX = startPoint.x + (endPoint.x - startPoint.x) * progress;
      const centerY = startPoint.y + (endPoint.y - startPoint.y) * progress;

      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);

      positions.push({
        id: i + 1,
        top: y,
        left: x,
        levelName: `Chặng ${i + 1}`,
        x: x * containerWidth,
        y: y * containerHeight,
      });
    }

    return positions;
  }

  private static generateZigzagPositions(
    startPoint: { x: number; y: number },
    endPoint: { x: number; y: number },
    milestoneCount: number,
    parameters: any,
    containerWidth: number,
    containerHeight: number
  ) {
    const { amplitude = 0.3 } = parameters;
    const positions = [];

    for (let i = 0; i < milestoneCount; i++) {
      const progress = i / (milestoneCount - 1);
      const zigzagOffset = Math.sin(progress * Math.PI * 2) * amplitude;

      const x = startPoint.x + (endPoint.x - startPoint.x) * progress + zigzagOffset;
      const y = startPoint.y + (endPoint.y - startPoint.y) * progress;

      positions.push({
        id: i + 1,
        top: y,
        left: Math.max(0.1, Math.min(0.9, x)), // Clamp to screen bounds
        levelName: `Chặng ${i + 1}`,
        x: Math.max(0.1, Math.min(0.9, x)) * containerWidth,
        y: y * containerHeight,
      });
    }

    return positions;
  }

  private static generateWavePositions(
    startPoint: { x: number; y: number },
    endPoint: { x: number; y: number },
    milestoneCount: number,
    parameters: any,
    containerWidth: number,
    containerHeight: number
  ) {
    // Similar to zigzag but with smoother curves
    return this.generateZigzagPositions(startPoint, endPoint, milestoneCount, parameters, containerWidth, containerHeight);
  }

  private static generateMountainPositions(
    startPoint: { x: number; y: number },
    endPoint: { x: number; y: number },
    milestoneCount: number,
    parameters: any,
    containerWidth: number,
    containerHeight: number
  ) {
    const { peakHeight = 0.3 } = parameters;
    const positions = [];

    for (let i = 0; i < milestoneCount; i++) {
      const progress = i / (milestoneCount - 1);

      // Create mountain-like curve (parabola)
      const mountainCurve = 4 * peakHeight * progress * (1 - progress);

      const x = startPoint.x + (endPoint.x - startPoint.x) * progress;
      const y = startPoint.y + (endPoint.y - startPoint.y) * progress - mountainCurve;

      positions.push({
        id: i + 1,
        top: Math.max(0.05, y), // Don't go above screen
        left: x,
        levelName: `Chặng ${i + 1}`,
        x: x * containerWidth,
        y: Math.max(0.05, y) * containerHeight,
      });
    }

    return positions;
  }
}

// Utility to get background-path mapping
export const getBackgroundPathMapping = (gameId: string): BackgroundPathMapping => {
  const mapping = BACKGROUND_PATH_MAPPINGS[gameId];
  if (!mapping) {
    throw new Error(`Background-path mapping not found for gameId: ${gameId}`);
  }
  return mapping;
};

// Extract positions for a specific game
export const extractGamePositions = (
  gameId: string,
  containerWidth: number,
  containerHeight: number
) => {
  const mapping = getBackgroundPathMapping(gameId);

  switch (mapping.pathExtractionMethod) {
    case 'manual':
      return BackgroundPathExtractor.extractManualPositions(mapping);
    case 'traced':
      return BackgroundPathExtractor.extractTracedPositions(mapping, containerWidth, containerHeight);
    case 'generated':
      return BackgroundPathExtractor.generateAlgorithmicPositions(mapping, containerWidth, containerHeight);
    default:
      throw new Error(`Unknown extraction method: ${mapping.pathExtractionMethod}`);
  }
};
