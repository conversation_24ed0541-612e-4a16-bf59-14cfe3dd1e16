/* eslint-disable react-native/no-inline-styles */
import React, {
  useState,
  useRef,
  useCallback,
  useImperativeHandle,
  forwardRef,
  useEffect,
} from 'react';
import {
  View,
  Text,
  TextInput,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  Image,
  FlatList,
  Dimensions,
  Alert,
} from 'react-native';
import {Winicon} from 'wini-mobile-components';
import ImagePicker from 'react-native-image-crop-picker';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import FastImage from 'react-native-fast-image';

type TextSegment = {
  text: string;
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
};
interface TextFormat {
  bold: boolean;
  italic: boolean;
  underline: boolean;
}
type ImageItem = {
  id: string;
  uri: string;
  path: string;
  mime: string;
  filename?: string;
  width?: number;
  height?: number;
  existingId?: string;
};

export type PostData = {
  text: string;
  segments: TextSegment[];
  images: ImageItem[];
  html?: string;
  existingImageIds?: string[]; // Thêm trường này
};

type RichTextComposerProps = {
  onTextChange?: (text: string) => void;
  onImagesChange?: (images: ImageItem[]) => void;
  onDataChange?: (data: PostData) => void;
  initialText?: string;
  initialImages?: ImageItem[];
  initialHtml?: string;
  maxImages?: number;
  // Thêm prop để nhận danh sách ID ảnh từ bài post cần edit
  initialImageIds?: string[];
};

export type RichTextComposerRef = {
  getPostData: () => PostData;
  clearContent: () => void;
  setContent: (
    text: string,
    images?: ImageItem[],
    html?: string,
    imageIds?: string[],
  ) => void;
  focus: () => void;
};

const RichTextComposer = forwardRef<RichTextComposerRef, RichTextComposerProps>(
  (props, ref) => {
    const {
      onTextChange,
      onImagesChange,
      onDataChange,
      initialText = '',
      initialImages = [],
      initialHtml = '',
      initialImageIds = [], // Thêm prop này
      maxImages = 10,
    } = props;

    // Khởi tạo segments từ initialHtml nếu có, nếu không thì từ initialText
    const [segments, setSegments] = useState<TextSegment[]>(() => {
      if (initialHtml) {
        return htmlToSegments(initialHtml);
      }
      return [
        {text: initialText, bold: false, italic: false, underline: false},
      ];
    });

    // Khởi tạo selectedImages từ initialImages hoặc từ initialImageIds
    const [selectedImages, setSelectedImages] = useState<ImageItem[]>(() => {
      if (initialImages.length > 0) {
        return initialImages;
      }

      // Chuyển đổi initialImageIds thành ImageItem[]
      if (initialImageIds.length > 0) {
        return initialImageIds.map((id, index) => ({
          id: `existing-${index}`,
          uri: `${ConfigAPI.urlImg}${id}`,
          path: `${ConfigAPI.urlImg}${id}`,
          mime: 'image/jpeg', // Giả định mime type
          filename: `image-${index}.jpg`,
          // Thêm trường để đánh dấu đây là ảnh đã tồn tại
          existingId: id,
        }));
      }

      return [];
    });

    const [format, setFormat] = useState({
      bold: false,
      italic: false,
      underline: false,
    });
    const screenWidth = Dimensions.get('window').width;
    const inputRef = useRef<TextInput>(null);
    const segmentsToHTML = (segments: TextSegment[]): string => {
      return segments
        .map(segment => {
          let text = segment.text;
          // Escape HTML characters
          text = text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');

          // Thêm các thẻ định dạng
          if (segment.bold) text = `<b>${text}</b>`;
          if (segment.italic) text = `<i>${text}</i>`;
          if (segment.underline) text = `<u>${text}</u>`;

          return text;
        })
        .join('');
    };
    // Hàm chuyển đổi HTML thành segments sử dụng regex
    const htmlToSegments = (html: string): TextSegment[] => {
      if (!html)
        return [{text: '', bold: false, italic: false, underline: false}];

      // Loại bỏ các thẻ không liên quan đến định dạng văn bản
      let cleanHtml = html
        .replace(/<(?!\/?(b|i|u|strong|em)\b)[^>]+>/gi, '')
        .replace(/\n/g, ' ')
        .trim();

      // Nếu không có HTML, trả về segment trống
      if (!cleanHtml) {
        return [{text: '', bold: false, italic: false, underline: false}];
      }

      const segments: TextSegment[] = [];
      let currentIndex = 0;

      // Regex để tìm các thẻ định dạng
      const tagRegex = /<\/?(?:b|i|u|strong|em)>/gi;
      let match;
      let lastIndex = 0;

      // Stack để theo dõi các thẻ đang mở
      const formatStack: {tag: string; index: number}[] = [];
      let currentFormat = {bold: false, italic: false, underline: false};

      // Tìm tất cả các thẻ trong HTML
      while ((match = tagRegex.exec(cleanHtml)) !== null) {
        const tag = match[0].toLowerCase();
        const isClosingTag = tag.startsWith('</');
        const tagName = isClosingTag ? tag.slice(2, -1) : tag.slice(1, -1);
        const normalizedTag =
          tagName === 'strong' ? 'b' : tagName === 'em' ? 'i' : tagName;

        // Xử lý text trước tag hiện tại
        if (match.index > lastIndex) {
          const text = cleanHtml.slice(lastIndex, match.index);
          // Giải mã các ký tự HTML đặc biệt
          const decodedText = text
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#039;/g, "'");

          if (decodedText) {
            segments.push({
              text: decodedText,
              bold: currentFormat.bold,
              italic: currentFormat.italic,
              underline: currentFormat.underline,
            });
          }
        }

        // Cập nhật định dạng hiện tại
        if (isClosingTag) {
          // Tìm thẻ mở tương ứng trong stack
          const openTagIndex = formatStack.findIndex(
            item => item.tag === normalizedTag,
          );
          if (openTagIndex !== -1) {
            // Xóa thẻ khỏi stack
            formatStack.splice(openTagIndex, 1);

            // Cập nhật định dạng
            switch (normalizedTag) {
              case 'b':
                currentFormat.bold = formatStack.some(item => item.tag === 'b');
                break;
              case 'i':
                currentFormat.italic = formatStack.some(
                  item => item.tag === 'i',
                );
                break;
              case 'u':
                currentFormat.underline = formatStack.some(
                  item => item.tag === 'u',
                );
                break;
            }
          }
        } else {
          // Thêm thẻ mở vào stack
          formatStack.push({tag: normalizedTag, index: currentIndex});

          // Cập nhật định dạng
          switch (normalizedTag) {
            case 'b':
              currentFormat.bold = true;
              break;
            case 'i':
              currentFormat.italic = true;
              break;
            case 'u':
              currentFormat.underline = true;
              break;
          }
        }

        lastIndex = match.index + match[0].length;
        currentIndex++;
      }

      // Xử lý text còn lại sau tag cuối cùng
      if (lastIndex < cleanHtml.length) {
        const text = cleanHtml.slice(lastIndex);
        // Giải mã các ký tự HTML đặc biệt
        const decodedText = text
          .replace(/&amp;/g, '&')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"')
          .replace(/&#039;/g, "'");

        if (decodedText) {
          segments.push({
            text: decodedText,
            bold: currentFormat.bold,
            italic: currentFormat.italic,
            underline: currentFormat.underline,
          });
        }
      }

      // Nếu không có segments nào, trả về segment trống
      if (segments.length === 0) {
        return [{text: '', bold: false, italic: false, underline: false}];
      }

      return segments;
    };
    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      getPostData: () => ({
        text: getFullText(),
        segments: segments,
        images: selectedImages,
        html: segmentsToHTML(segments),
        // Thêm trường để lưu danh sách ID ảnh đã tồn tại
        existingImageIds: selectedImages
          .filter(img => img.existingId)
          .map(img => img.existingId as string),
      }),
      clearContent: () => {
        setSegments([{text: '', bold: false, italic: false, underline: false}]);
        setSelectedImages([]);
      },
      setContent: (
        text: string,
        images: ImageItem[] = [],
        html?: string,
        imageIds?: string[],
      ) => {
        if (html) {
          // Nếu có HTML, ưu tiên sử dụng HTML
          setSegments(htmlToSegments(html));
        } else {
          // Nếu không có HTML, sử dụng text
          setSegments([{text, bold: false, italic: false, underline: false}]);
        }

        if (images.length > 0) {
          // Nếu có images, ưu tiên sử dụng images
          setSelectedImages(images);
        } else if (imageIds && imageIds.length > 0) {
          // Nếu có imageIds, chuyển đổi thành ImageItem[]
          setSelectedImages(
            imageIds.map((id, index) => ({
              id: `existing-${index}`,
              uri: `${ConfigAPI.urlImg}${id}`,
              path: `${ConfigAPI.urlImg}${id}`,
              mime: 'image/jpeg',
              filename: `image-${index}.jpg`,
              existingId: id,
            })),
          );
        } else {
          setSelectedImages([]);
        }
      },
      focus: () => {
        inputRef.current?.focus();
      },
    }));

    const getFullText = () => {
      return segments.reduce((text, segment) => text + segment.text, '');
    };

    // Thêm useRef để theo dõi dữ liệu trước đó
    const prevDataRef = useRef<PostData | null>(null);

    // Notify parent component when text or images change
    useEffect(() => {
      const fullText = getFullText();

      // Tạo dữ liệu mới
      const newData = {
        text: fullText,
        segments: segments,
        images: selectedImages,
      };

      // Kiểm tra xem dữ liệu có thay đổi không
      const prevData = prevDataRef.current;
      const hasChanged =
        !prevData ||
        prevData.text !== newData.text ||
        prevData.images !== newData.images;

      // Chỉ gọi callbacks khi dữ liệu thực sự thay đổi
      if (hasChanged) {
        if (onTextChange) {
          onTextChange(fullText);
        }

        if (onImagesChange) {
          onImagesChange(selectedImages);
        }

        if (onDataChange) {
          onDataChange(newData);
        }

        // Cập nhật ref
        prevDataRef.current = newData;
      }
    }, [segments, selectedImages, onTextChange, onImagesChange, onDataChange]);

    // Kiểm tra xem có phải đang composing không (nhập tiếng Việt)
    const isVietnameseComposing = (text: string, position: number): boolean => {
      // Kiểm tra cả trường hợp đang nhập ký tự đầu tiên
      if (position <= text.length) {
        // Kiểm tra 15 ký tự cuối cùng có chứa dấu tiếng Việt không
        const lastChars = text.slice(Math.max(0, position - 15), position);
        // Bao gồm cả chữ cái hoa có dấu
        return /[áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ]/.test(
          lastChars,
        );
      }
      return false;
    };

    // Hàm kiểm tra xem một ký tự có phải là ký tự Latin cơ bản không dấu
    const isBasicLatinChar = (char: string): boolean => {
      return /^[a-zA-Z]$/.test(char);
    };

    // Hàm kiểm tra xem một ký tự có phải là ký tự tiếng Việt có dấu
    const isVietnameseChar = (char: string): boolean => {
      return /^[áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ]$/.test(
        char,
      );
    };

    // Hàm kiểm tra xem một ký tự tiếng Việt có dấu có thể thay thế cho ký tự Latin không dấu
    const canReplaceLatinChar = (
      latinChar: string,
      vietnameseChar: string,
    ): boolean => {
      const latinToVietnamese: {[key: string]: RegExp} = {
        a: /^[áàảãạăắằẳẵặâấầẩẫậ]$/i,
        e: /^[éèẻẽẹêếềểễệ]$/i,
        i: /^[íìỉĩị]$/i,
        o: /^[óòỏõọôốồổỗộơớờởỡợ]$/i,
        u: /^[úùủũụưứừửữự]$/i,
        y: /^[ýỳỷỹỵ]$/i,
        d: /^[đ]$/i,
      };

      const lowerLatinChar = latinChar.toLowerCase();
      if (latinToVietnamese[lowerLatinChar]) {
        return latinToVietnamese[lowerLatinChar].test(vietnameseChar);
      }
      return false;
    };

    // Thêm state để theo dõi trạng thái nhập liệu
    const [lastInputChar, setLastInputChar] = useState<string>('');
    const [isComposingVietnamese, setIsComposingVietnamese] =
      useState<boolean>(false);

    const onChangeText = (newText: string) => {
      const fullText = getFullText();

      // Nếu không có thay đổi, không cần xử lý
      if (newText === fullText) return;

      // Kiểm tra xem có phải đang nhập chữ tiếng Việt đầu tiên không
      const isFirstVietnameseChar =
        newText.length === 1 &&
        /^[áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ]$/.test(
          newText,
        );

      // Nếu là chữ tiếng Việt đầu tiên, thay thế hoàn toàn
      if (isFirstVietnameseChar) {
        setSegments([
          {
            text: newText,
            bold: format.bold,
            italic: format.italic,
            underline: format.underline,
          },
        ]);
        setLastInputChar(newText);
        setIsComposingVietnamese(true);
        return;
      }

      // Kiểm tra xem có phải đang thay thế chữ cái Latin bằng chữ cái tiếng Việt không
      if (
        fullText.length === 1 &&
        newText.length === 1 &&
        /^[a-zA-Z]$/.test(fullText) &&
        /^[áàảãạăắằẳẵặâấầẩẫậéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐ]$/.test(
          newText,
        )
      ) {
        // Kiểm tra xem chữ cái tiếng Việt có thể thay thế chữ cái Latin không
        const latinChar = fullText.charAt(0);
        const vietnameseChar = newText.charAt(0);

        const canReplace = (function () {
          const map: {[key: string]: RegExp} = {
            a: /^[áàảãạăắằẳẵặâấầẩẫậÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬ]$/,
            e: /^[éèẻẽẹêếềểễệÉÈẺẼẸÊẾỀỂỄỆ]$/,
            i: /^[íìỉĩịÍÌỈĨỊ]$/,
            o: /^[óòỏõọôốồổỗộơớờởỡợÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢ]$/,
            u: /^[úùủũụưứừửữựÚÙỦŨỤƯỨỪỬỮỰ]$/,
            y: /^[ýỳỷỹỵÝỲỶỸỴ]$/,
            d: /^[đĐ]$/,
          };

          const lowerLatinChar = latinChar.toLowerCase();
          return (
            map[lowerLatinChar] && map[lowerLatinChar].test(vietnameseChar)
          );
        })();

        if (canReplace) {
          setSegments([
            {
              text: newText,
              bold: format.bold,
              italic: format.italic,
              underline: format.underline,
            },
          ]);
          setLastInputChar(newText);
          setIsComposingVietnamese(true);
          return;
        }
      }

      setSegments(prev => {
        // Tạo bản sao mới của mảng segments
        const newSegs = [...prev];

        // Kiểm tra mảng rỗng và khởi tạo nếu cần
        if (newSegs.length === 0) {
          return [
            {
              text: newText,
              bold: format.bold,
              italic: format.italic,
              underline: format.underline,
            },
          ];
        }

        // Trường hợp 1: Xóa văn bản
        if (newText.length < fullText.length) {
          // Tìm vị trí bắt đầu khác biệt
          let diffIndex = 0;
          while (
            diffIndex < newText.length &&
            newText[diffIndex] === fullText[diffIndex]
          ) {
            diffIndex++;
          }

          // Số ký tự bị xóa
          const deletedCharCount = fullText.length - newText.length;

          // Tìm segment chứa vị trí cần xóa
          let charCount = 0;
          let targetSegmentIndex = -1;
          let positionInSegment = -1;

          for (let i = 0; i < newSegs.length; i++) {
            const segmentLength = newSegs[i].text.length;
            if (
              charCount <= diffIndex &&
              diffIndex < charCount + segmentLength
            ) {
              targetSegmentIndex = i;
              positionInSegment = diffIndex - charCount;
              break;
            }
            charCount += segmentLength;
          }

          // Nếu không tìm thấy segment (hiếm khi xảy ra)
          if (targetSegmentIndex === -1) {
            console.warn('Cannot find segment to delete from');
            return newSegs;
          }

          // Xóa văn bản từ vị trí đã xác định
          const segment = newSegs[targetSegmentIndex];
          const beforeText = segment.text.substring(0, positionInSegment);
          const afterText = segment.text.substring(
            positionInSegment + deletedCharCount,
          );

          // Cập nhật segment hiện tại
          newSegs[targetSegmentIndex] = {
            ...segment,
            text: beforeText + afterText,
          };

          // Xóa các segment trống
          const filteredSegs = newSegs.filter(seg => seg.text.length > 0);

          // Đảm bảo luôn có ít nhất một segment
          if (filteredSegs.length === 0) {
            return [
              {
                text: '',
                bold: format.bold,
                italic: format.italic,
                underline: format.underline,
              },
            ];
          }

          return filteredSegs;
        }

        // Trường hợp 2: Thêm hoặc thay đổi văn bản

        // Tìm điểm khác biệt đầu tiên giữa hai chuỗi
        let diffIndex = 0;
        const minLength = Math.min(fullText.length, newText.length);

        while (
          diffIndex < minLength &&
          fullText[diffIndex] === newText[diffIndex]
        ) {
          diffIndex++;
        }

        // Xác định phần văn bản được thêm vào
        const addedText = newText.substring(diffIndex);

        // Tìm segment chứa điểm khác biệt
        let charCount = 0;
        let targetSegmentIndex = -1;

        for (let i = 0; i < newSegs.length; i++) {
          charCount += newSegs[i].text.length;
          if (charCount > diffIndex) {
            targetSegmentIndex = i;
            break;
          }
        }

        // Nếu không tìm thấy segment (điểm thay đổi ở cuối văn bản)
        if (targetSegmentIndex === -1) {
          // Thêm segment mới với định dạng hiện tại
          newSegs.push({
            text: addedText,
            bold: format.bold,
            italic: format.italic,
            underline: format.underline,
          });
          return mergeAdjacentSegments(newSegs);
        }

        // Xác định vị trí trong segment
        const segmentStartIndex =
          charCount - newSegs[targetSegmentIndex].text.length;
        const positionInSegment = diffIndex - segmentStartIndex;

        const currentSegment = newSegs[targetSegmentIndex];

        // Cải thiện kiểm tra composing cho tiếng Việt
        // Kiểm tra nếu đang thay đổi ký tự cuối cùng hoặc gần cuối của segment
        const isComposing =
          positionInSegment > 0 &&
          (positionInSegment >= currentSegment.text.length - 15 ||
            isVietnameseComposing(currentSegment.text, positionInSegment));

        // Xác định độ dài thay đổi
        const changeLength = newText.length - fullText.length;

        // Nếu thay đổi chỉ 1-2 ký tự và ở cuối segment, có thể là đang gõ tiếng Việt
        const isSmallChange =
          changeLength <= 2 && positionInSegment === currentSegment.text.length;

        if (isComposing || isSmallChange) {
          const beforeComposing = currentSegment.text.substring(
            0,
            positionInSegment,
          );
          const afterComposing = newText.substring(diffIndex);

          // Trường hợp 1: Ký tự đầu tiên có dấu
          if (
            positionInSegment === 0 &&
            afterComposing.length === 1 &&
            isVietnameseChar(afterComposing)
          ) {
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: afterComposing,
            };
          }
          // Trường hợp 2: Thay thế ký tự không dấu bằng ký tự có dấu
          else if (
            currentSegment.text.length >= 1 &&
            positionInSegment === currentSegment.text.length &&
            afterComposing.length === 1 &&
            isVietnameseChar(afterComposing) &&
            isBasicLatinChar(
              currentSegment.text[currentSegment.text.length - 1],
            ) &&
            canReplaceLatinChar(
              currentSegment.text[currentSegment.text.length - 1],
              afterComposing,
            )
          ) {
            // Thay thế ký tự cuối cùng bằng ký tự có dấu
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: currentSegment.text.slice(0, -1) + afterComposing,
            };
          }
          // Trường hợp 3: Thay thế ký tự có dấu bằng ký tự có dấu khác (ví dụ: ă -> â)
          else if (
            currentSegment.text.length >= 1 &&
            positionInSegment === currentSegment.text.length &&
            afterComposing.length === 1 &&
            isVietnameseChar(afterComposing) &&
            isVietnameseChar(
              currentSegment.text[currentSegment.text.length - 1],
            )
          ) {
            // Thay thế ký tự cuối cùng bằng ký tự có dấu mới
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: currentSegment.text.slice(0, -1) + afterComposing,
            };
          } else {
            newSegs[targetSegmentIndex] = {
              ...currentSegment,
              text: beforeComposing + afterComposing,
            };
          }
        } else {
          // Trường hợp thêm mới: Tách segment hiện tại và thêm segment mới

          // Cắt segment hiện tại tại điểm thay đổi
          const firstPart = currentSegment.text.substring(0, positionInSegment);
          const lastPart = currentSegment.text.substring(positionInSegment);

          // Cập nhật segment hiện tại với phần đầu
          currentSegment.text = firstPart;

          // Thêm segment mới với văn bản được thêm vào và định dạng hiện tại
          newSegs.splice(targetSegmentIndex + 1, 0, {
            text: addedText,
            bold: format.bold,
            italic: format.italic,
            underline: format.underline,
          });

          // Thêm segment với phần cuối nếu có
          if (lastPart) {
            newSegs.splice(targetSegmentIndex + 2, 0, {
              ...currentSegment,
              text: lastPart,
            });
          }
        }

        // Hợp nhất các segment liền kề có cùng định dạng
        return mergeAdjacentSegments(newSegs);
      });

      // Cập nhật trạng thái
      if (newText.length > 0) {
        setLastInputChar(newText.charAt(newText.length - 1));
      } else {
        setLastInputChar('');
      }
      setIsComposingVietnamese(false);
    };

    // Hàm hợp nhất các segment liền kề có cùng định dạng
    const mergeAdjacentSegments = (segments: TextSegment[]): TextSegment[] => {
      if (segments.length <= 1) return segments;

      const result: TextSegment[] = [segments[0]];

      for (let i = 1; i < segments.length; i++) {
        const current = segments[i];
        const previous = result[result.length - 1];

        if (
          previous.bold === current.bold &&
          previous.italic === current.italic &&
          previous.underline === current.underline
        ) {
          // Nếu định dạng giống nhau, hợp nhất
          previous.text += current.text;
        } else {
          // Nếu định dạng khác nhau, thêm segment mới
          result.push(current);
        }
      }

      return result;
    };

    // Hàm xử lý khi nhấn nút định dạng
    const toggleFormat = (key: 'bold' | 'italic' | 'underline') => {
      setFormat(prev => ({
        ...prev,
        [key]: !prev[key],
      }));

      // Áp dụng định dạng cho văn bản đã chọn
      applyFormatToSelection(key);

      // Focus lại vào input sau khi thay đổi định dạng
      setTimeout(() => {
        inputRef.current?.focus();
      }, 0);
    };

    // Hàm áp dụng định dạng cho văn bản đã chọn
    const applyFormatToSelection = (
      formatKey: 'bold' | 'italic' | 'underline',
    ) => {
      // Lấy vị trí selection từ TextInput
      const input = inputRef.current;
      if (!input) return;

      // Sử dụng state selection thay vì lấy từ input.props.selection
      if (!selection || selection.start === selection.end) {
        // Nếu không có văn bản được chọn, chỉ thay đổi định dạng cho văn bản mới
        return;
      }

      const selectionStart = selection.start;
      const selectionEnd = selection.end;

      setSegments(prev => {
        // Tạo bản sao mới của mảng segments
        const newSegs = [...prev];

        // Tìm các segment chứa vùng chọn
        let charCount = 0;
        let startSegmentIndex = -1;
        let startPositionInSegment = -1;
        let endSegmentIndex = -1;
        let endPositionInSegment = -1;

        // Tìm segment chứa điểm bắt đầu
        for (let i = 0; i < newSegs.length; i++) {
          const segmentLength = newSegs[i].text.length;
          if (
            charCount <= selectionStart &&
            selectionStart < charCount + segmentLength
          ) {
            startSegmentIndex = i;
            startPositionInSegment = selectionStart - charCount;
          }
          if (
            charCount <= selectionEnd &&
            selectionEnd <= charCount + segmentLength
          ) {
            endSegmentIndex = i;
            endPositionInSegment = selectionEnd - charCount;
            break;
          }
          charCount += segmentLength;
        }

        // Nếu không tìm thấy segment
        if (startSegmentIndex === -1 || endSegmentIndex === -1) {
          console.warn('Cannot find segments for selection');
          return newSegs;
        }

        // Trường hợp selection nằm trong một segment
        if (startSegmentIndex === endSegmentIndex) {
          const segment = newSegs[startSegmentIndex];
          const beforeText = segment.text.substring(0, startPositionInSegment);
          const selectedText = segment.text.substring(
            startPositionInSegment,
            endPositionInSegment,
          );
          const afterText = segment.text.substring(endPositionInSegment);

          // Tạo segment mới cho phần được chọn với định dạng đã toggle
          const newFormat = {
            bold: formatKey === 'bold' ? !segment.bold : segment.bold,
            italic: formatKey === 'italic' ? !segment.italic : segment.italic,
            underline:
              formatKey === 'underline'
                ? !segment.underline
                : segment.underline,
          };

          // Thay thế segment hiện tại bằng 3 segment: trước, đã chọn, sau
          const newSegments = [];

          if (beforeText) {
            newSegments.push({...segment, text: beforeText});
          }

          newSegments.push({
            text: selectedText,
            bold: newFormat.bold,
            italic: newFormat.italic,
            underline: newFormat.underline,
          });

          if (afterText) {
            newSegments.push({...segment, text: afterText});
          }

          // Thay thế segment cũ bằng các segment mới
          newSegs.splice(startSegmentIndex, 1, ...newSegments);
        }
        // Trường hợp selection trải qua nhiều segment
        else {
          // Xử lý segment đầu tiên
          const firstSegment = newSegs[startSegmentIndex];
          const beforeFirstText = firstSegment.text.substring(
            0,
            startPositionInSegment,
          );
          const selectedFirstText = firstSegment.text.substring(
            startPositionInSegment,
          );

          // Xử lý segment cuối cùng
          const lastSegment = newSegs[endSegmentIndex];
          const selectedLastText = lastSegment.text.substring(
            0,
            endPositionInSegment,
          );
          const afterLastText =
            lastSegment.text.substring(endPositionInSegment);

          // Tạo mảng segments mới
          const resultSegments = [];

          // Thêm phần trước selection của segment đầu tiên
          if (beforeFirstText) {
            resultSegments.push({...firstSegment, text: beforeFirstText});
          }

          // Thêm phần được chọn của segment đầu tiên với định dạng mới
          resultSegments.push({
            text: selectedFirstText,
            bold: formatKey === 'bold' ? !firstSegment.bold : firstSegment.bold,
            italic:
              formatKey === 'italic'
                ? !firstSegment.italic
                : firstSegment.italic,
            underline:
              formatKey === 'underline'
                ? !firstSegment.underline
                : firstSegment.underline,
          });

          // Xử lý các segment ở giữa
          for (let i = startSegmentIndex + 1; i < endSegmentIndex; i++) {
            const midSegment = newSegs[i];
            resultSegments.push({
              text: midSegment.text,
              bold: formatKey === 'bold' ? !midSegment.bold : midSegment.bold,
              italic:
                formatKey === 'italic' ? !midSegment.italic : midSegment.italic,
              underline:
                formatKey === 'underline'
                  ? !midSegment.underline
                  : midSegment.underline,
            });
          }

          // Thêm phần được chọn của segment cuối cùng với định dạng mới
          resultSegments.push({
            text: selectedLastText,
            bold: formatKey === 'bold' ? !lastSegment.bold : lastSegment.bold,
            italic:
              formatKey === 'italic' ? !lastSegment.italic : lastSegment.italic,
            underline:
              formatKey === 'underline'
                ? !lastSegment.underline
                : lastSegment.underline,
          });

          // Thêm phần sau selection của segment cuối cùng
          if (afterLastText) {
            resultSegments.push({...lastSegment, text: afterLastText});
          }

          // Thay thế các segment cũ bằng các segment mới
          newSegs.splice(
            startSegmentIndex,
            endSegmentIndex - startSegmentIndex + 1,
            ...resultSegments,
          );
        }

        // Hợp nhất các segment liền kề có cùng định dạng
        return mergeAdjacentSegments(newSegs);
      });
    };

    // Hàm chọn nhiều ảnh
    const handlePickImages = useCallback(async () => {
      try {
        if (selectedImages.length >= maxImages) {
          Alert.alert(
            'Thông báo',
            `Bạn chỉ có thể chọn tối đa ${maxImages} ảnh.`,
          );
          return;
        }

        const remainingSlots = maxImages - selectedImages.length;

        const images = await ImagePicker.openPicker({
          multiple: true,
          mediaType: 'photo',
          maxFiles: remainingSlots,
          compressImageQuality: 0.8,
        });

        const newImages = images.map(img => ({
          id: Math.random().toString(36).substring(2, 15),
          uri: Platform.OS === 'ios' ? img.sourceURL || img.path : img.path,
          path: img.path,
          mime: img.mime,
          filename: img.filename || `image-${Date.now()}.jpg`,
          width: img.width,
          height: img.height,
        }));

        setSelectedImages(prev => [...prev, ...newImages]);
      } catch (error) {
        if (
          error instanceof Error &&
          error.message !== 'User cancelled image selection'
        ) {
          console.error('Error picking images:', error);
          Alert.alert('Lỗi', 'Không thể chọn ảnh. Vui lòng thử lại.');
        }
      }
    }, [selectedImages, maxImages]);

    // Hàm xóa ảnh
    const handleRemoveImage = useCallback((imageId: string) => {
      Alert.alert(
        'Xác nhận',
        'Bạn có chắc muốn xóa ảnh này?',
        [
          {
            text: 'Hủy',
            style: 'cancel',
          },
          {
            text: 'Xóa',
            onPress: () => {
              setSelectedImages(prev => prev.filter(img => img.id !== imageId));
            },
            style: 'destructive',
          },
        ],
        {cancelable: true},
      );
    }, []);

    // Component hiển thị preview ảnh
    const ImagePreview = useCallback(() => {
      if (selectedImages.length === 0) return null;

      return (
        <View style={styles.imagePreviewContainer}>
          <Text style={styles.imagePreviewTitle}>
            Ảnh đã chọn ({selectedImages.length})
          </Text>
          <FlatList
            data={selectedImages}
            horizontal
            showsHorizontalScrollIndicator={false}
            keyExtractor={item => item.id}
            renderItem={({item}) => (
              <View style={styles.imageContainer}>
                <FastImage
                  source={{uri: item.uri}}
                  style={styles.previewImage}
                />
                <TouchableOpacity
                  style={styles.removeImageButton}
                  onPress={() => handleRemoveImage(item.id)}>
                  <FontAwesomeIcon
                    icon={faMinusCircle}
                    size={20}
                    color="#667994"
                    style={{backgroundColor: '#fff', borderRadius: 20}}
                  />
                </TouchableOpacity>
              </View>
            )}
            ListFooterComponent={
              selectedImages.length < maxImages ? (
                <TouchableOpacity
                  style={styles.addMoreImagesButton}
                  onPress={handlePickImages}>
                  <Text style={styles.addMoreImagesText}>+</Text>
                </TouchableOpacity>
              ) : null
            }
          />
        </View>
      );
    }, [selectedImages, handleRemoveImage, handlePickImages, maxImages]);

    // Thêm state để theo dõi vị trí hiện tại của con trỏ
    const [selection, setSelection] = useState<{start: number; end: number}>({
      start: 0,
      end: 0,
    });

    // Thêm hàm xử lý sự kiện selection change
    const handleSelectionChange = (event: any) => {
      const newSelection = event.nativeEvent.selection;
      if (newSelection) {
        setSelection(newSelection);
      }
    };

    // Thêm style cho TextInput để đảm bảo nó khớp chính xác với Text hiển thị
    const hiddenInputStyle = {
      ...styles.hiddenInput,
      fontSize: 14,
      lineHeight: 20,
      paddingTop: 0,
      paddingBottom: 0,
      textAlignVertical: 'top',
    };

    return (
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          style={{flex: 1}}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 120 : 0}>
          <View style={styles.editorContainer}>
            {/* Phần editor chính */}
            <ScrollView
              style={styles.scrollArea}
              keyboardShouldPersistTaps="always">
              <Text style={styles.placeholder}>
                {getFullText().length === 0 ? 'Bạn đang nghĩ gì ?' : ''}
              </Text>
              <View style={{position: 'relative'}}>
                <Text style={styles.richText}>
                  {segments.map((seg, idx) => (
                    <Text
                      key={idx}
                      style={{
                        fontWeight: seg.bold ? 'bold' : 'normal',
                        fontStyle: seg.italic ? 'italic' : 'normal',
                        textDecorationLine: seg.underline
                          ? 'underline'
                          : 'none',
                        fontSize: 16,
                        lineHeight: 24,
                      }}>
                      {seg.text}
                    </Text>
                  ))}
                </Text>
                <TextInput
                  ref={inputRef}
                  value={getFullText()}
                  onChangeText={onChangeText}
                  onSelectionChange={handleSelectionChange}
                  multiline
                  selection={selection}
                  cursorColor={ColorThemes.light.Neutral_Text_Color_Body}
                  style={[styles.richText, styles.hiddenInput]}
                  caretHidden={false}
                  autoCorrect={false}
                  autoCapitalize="none"
                  keyboardType="default"
                />
              </View>
            </ScrollView>
          </View>

          {/* Image Preview Section */}
          <ImagePreview />

          {/* Toolbar */}
          <View style={styles.toolbar}>
            <View style={styles.formatButtons}>
              <FormatIcon
                label="B"
                active={format.bold}
                onPress={() => toggleFormat('bold')}
              />
              <FormatIcon
                label="I"
                active={format.italic}
                onPress={() => toggleFormat('italic')}
              />
              <FormatIcon
                label="U"
                active={format.underline}
                onPress={() => toggleFormat('underline')}
              />
            </View>

            <TouchableOpacity
              style={styles.imagePickerButton}
              onPress={handlePickImages}>
              <Winicon
                src="fill/development/image"
                size={20}
                color={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
              {/* <Text style={styles.imagePickerText}>
              {selectedImages.length > 0 
                ? `Ảnh (${selectedImages.length})` 
                : 'Thêm ảnh'}
            </Text> */}
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    );
  },
);
export default RichTextComposer;

const FormatIcon = ({
  label,
  active,
  onPress,
}: {
  label: string;
  active: boolean;
  onPress: () => void;
}) => (
  <TouchableOpacity
    onPress={onPress}
    style={[styles.iconButton, active && styles.iconActive]}>
    <Text
      style={[
        styles.iconText,
        {
          fontWeight: label === 'B' ? 'bold' : 'normal',
          fontStyle: label === 'I' ? 'italic' : 'normal',
          textDecorationLine: label === 'U' ? 'underline' : 'none',
          color: active ? '#000' : '#666',
        },
      ]}>
      {label}
    </Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    height: '100%',
    width: '100%',
  },
  editorContainer: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  scrollArea: {
    flex: 1,
  },
  placeholder: {
    position: 'absolute',
    top: 4,
    left: 0,
    color: '#8E8E93',
    fontSize: 14,
  },
  richText: {
    fontSize: 16,
    color: '#1c1c1e',
    lineHeight: 24,
    minHeight: 100, // Đảm bảo có đủ không gian cho văn bản
    paddingTop: 0,
    paddingBottom: 0,
  },
  hiddenInput: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    color: 'transparent',
    backgroundColor: 'transparent',
    fontSize: 16, // Phải giống với richText
    lineHeight: 24, // Phải giống với richText
    paddingTop: 0,
    paddingBottom: 0,
    textAlignVertical: 'top',
  },
  imagePreviewContainer: {
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    paddingTop: 8,
    paddingBottom: 8,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  imagePreviewTitle: {
    ...TypoSkin.subtitle3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginBottom: 8,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 8,
    borderRadius: 8,
    overflow: 'visible',
  },
  previewImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    zIndex: 10,
    padding: 4,
  },
  addMoreImagesButton: {
    width: 80,
    height: 80,
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addMoreImagesText: {
    fontSize: 24,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  toolbar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderColor: '#E5E5EA',
    backgroundColor: '#fff',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  formatButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 16,
    width: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    fontSize: 18,
    color: '#666',
  },
  iconActive: {
    borderBottomWidth: 2,
    borderColor: '#000',
  },
  imagePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  imagePickerText: {
    ...TypoSkin.buttonText4,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginLeft: 4,
  },
});
