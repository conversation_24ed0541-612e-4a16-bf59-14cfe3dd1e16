import React, {forwardRef, useImperativeHandle, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Platform,
  Dimensions,
} from 'react-native';
import {Modalize} from 'react-native-modalize';
import {ColorThemes} from '../../../assets/skin/colors';
import {hideBottomSheet, ListTile} from 'wini-mobile-components';
import {TypoSkin} from '../../../assets/skin/typography';

export type ActionSheetOption = {
  key: string;
  label: string;
  icon?: React.ReactNode;
  payload?: any;
};

type Props = {
  actions: ActionSheetOption[];
  onSelect: (key: string, payload?: any) => void;
};

const ActionSheet = forwardRef<any, Props>(({actions, onSelect}, ref) => {
  const handlePress = (key: string, payload: any) => {
    if (payload) {
      onSelect(key, payload);
    }
    onSelect(key);
  };

  return (
    <View
      style={{
        width: Dimensions.get('window').width,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      {actions.map((item, idx) => (
        <ListTile
          key={item.key}
          onPress={() => handlePress(item.key, item.payload)}
          leading={
            item.icon && <View style={styles.iconWrapper}>{item.icon}</View>
          }
          title={
            <Text
              style={{
                ...TypoSkin.buttonText3,
                color: ColorThemes.light.Neutral_Text_Color_Title,
              }}>
              {item.label}
            </Text>
          }
        />
      ))}
    </View>
  );

  // return (
  //   <Modalize
  //     ref={modalRef}
  //     adjustToContentHeight
  //     modalStyle={styles.modal}
  //     overlayStyle={styles.overlay}
  //     handleStyle={styles.handle}
  //     panGestureEnabled
  //     closeOnOverlayTap
  //     scrollViewProps={{showsVerticalScrollIndicator: false}}>
  //     <View style={styles.wrapper}>
  //       <View style={styles.actionGroup}>
  //         {actions.map((item, idx) => (
  //           <TouchableOpacity
  //             key={item.key}
  //             onPress={() => handlePress(item.key, item.payload)}
  //             style={[styles.actionButton, idx !== 0 && styles.divider]}
  //             activeOpacity={0.8}>
  //             <View style={styles.row}>
  //               {item.icon && (
  //                 <View style={styles.iconWrapper}>{item.icon}</View>
  //               )}
  //               <Text
  //                 style={[
  //                   styles.actionText,
  //                   item.destructive && styles.destructive,
  //                 ]}>
  //                 {item.label}
  //               </Text>
  //             </View>
  //           </TouchableOpacity>
  //         ))}
  //       </View>
  //     </View>
  //   </Modalize>
  // );
});

export default ActionSheet;

const styles = StyleSheet.create({
  modal: {
    backgroundColor: 'transparent',
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  handle: {
    width: 40,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#ccc',
    alignSelf: 'center',
    marginVertical: 10,
  },
  wrapper: {
    width: '100%',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconWrapper: {
    marginRight: 12,
  },
  actionText: {
    fontSize: 14,
    color: '#000',
    textAlign: 'left',
  },
  destructive: {
    color: '#FF3B30',
  },
  cancelText: {
    fontWeight: '600',
    textAlign: 'center',
  },
  divider: {
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: '#eee',
  },
});
