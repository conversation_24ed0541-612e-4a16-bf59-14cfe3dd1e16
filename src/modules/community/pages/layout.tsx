import {StyleSheet, View} from 'react-native';
import {Text} from 'react-native-paper';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {
  ComponentStatus,
  FDialog,
  showDialog,
  Winicon,
} from 'wini-mobile-components';
import {useRoute} from '@react-navigation/native';
import {useRef} from 'react';
import Home from './Home';
import Explore from './Explore';
import GroupIndex from '../groups';
import Groups from './groupIndex';
import Chat from './Chat';
import Notif from './Notif';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useTranslation} from 'react-i18next';

const Tab = createBottomTabNavigator();

const getBottomNavigateData = (t: any) => [
  {
    id: 0,
    // name: t('community.tabs.home'),
    name: 'Trang chủ',
    component: Home,
    activeIcon: 'fill/user interface/home',
    inActiveIcon: 'outline/user interface/home',
  },
  {
    id: 1,
    // name: t('community.tabs.explore'),
    name: 'Khám phá',
    component: Explore,
    activeIcon: 'outline/user interface/search',
    inActiveIcon: 'outline/user interface/search',
  },
  {
    id: 2,
    // name: t('community.tabs.group'),
    name: 'Nhóm',
    component: Groups,
    activeIcon: 'fill/users/team',
    inActiveIcon: 'outline/users/team',
  },
  // {
  //   id: 3,
  //   name: t('community.tabs.chat'),
  //   component: Chat,
  //   activeIcon: 'fill/user interface/f-chat',
  //   inActiveIcon: 'outline/user interface/f-chat',
  // },
  {
    id: 4,
    // name: t('community.tabs.notify'),
    name: 'Thông báo',
    component: Notif,
    activeIcon: 'fill/user interface/bell',
    inActiveIcon: 'outline/user interface/bell',
  },
];

export default function CommunityLayout() {
  const {t} = useTranslation();
  const route = useRoute<any>();
  const dialogCheckAccRef = useRef<any>(null);
  const user = useSelectorCustomerState().data;
  const bottomNavigateData = getBottomNavigateData(t);

  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FDialog ref={dialogCheckAccRef} />
      <Tab.Navigator
        initialRouteName={route.params?.rootName ?? undefined}
        screenOptions={{
          tabBarStyle: styles.bar,
          tabBarActiveTintColor: ColorThemes.light.Primary_Color_Main,
          tabBarInactiveTintColor:
            ColorThemes.light.Neutral_Text_Color_Subtitle,
          headerShown: false,
        }}>
        {bottomNavigateData.map((item, index) => {
          return (
            <Tab.Screen
              listeners={{
                tabPress: (e: any) => {
                  if (
                    !user &&
                    item.id != 0 &&
                    item.id != 4 &&
                    item.id != 3 &&
                    item.id != 2
                  ) {
                    // dialogCheckAcc(dialogCheckAccRef, undefined);
                    // Prevent default action
                    e.preventDefault();
                    return;
                  }
                  //Any custom code here
                },
              }}
              key={`${index}`}
              name={item.name}
              options={() => {
                return {
                  tabBarLabel: ({color, focused}: any) => (
                    <Text
                      style={[
                        TypoSkin.buttonText6,
                        {color: color, fontWeight: focused ? 'bold' : '400'},
                      ]}>
                      {item.name}
                    </Text>
                  ),
                  tabBarIcon: ({color, focused}: any) => (
                    <Winicon
                      src={focused ? item.activeIcon : item.inActiveIcon}
                      size={20}
                      color={color}
                    />
                  ),
                };
              }}
              component={item.component}
            />
          );
        })}
      </Tab.Navigator>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  bar: {
    backgroundColor: '#ffffff',
    borderStyle: 'solid',
    borderTopColor: '#F5F5F5',
  },
  tabBarButton: {
    // width: 64,
    height: '100%',
    // gap: 4,
    transform: [{translateY: -14}],
    alignItems: 'center',
  },
});
