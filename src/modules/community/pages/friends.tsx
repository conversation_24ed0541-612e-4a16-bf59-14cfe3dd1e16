import {
  Dimensions,
  FlatList,
  Linking,
  Pressable,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useEffect, useRef, useState} from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {
  ComponentStatus,
  AppButton,
  ListTile,
  showSnackbar,
  SkeletonImage,
  TextField,
  Winicon,
  showBottomSheet,
  FBottomSheet,
  hideBottomSheet,
} from 'wini-mobile-components';
import {GroupDA} from '../groups/da';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {navigate, RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import {CustomerDA} from '../../customer/da';
import {FollowStatus} from '../../../Config/Contanst';
import ActionSheet, {ActionSheetOption} from '../components/ActionSheet';

export default function Friends(pros: any) {
  const [searchValue, setSearchValue] = useState('');
  // const currentUser = useSelectorCustomerState().data;
  const [listFriend, setlistFriend] = useState<Array<any>>([]);

  const customerDA = new CustomerDA();
  const currentUser = useSelectorCustomerState().data;
  const bottomSheetRef = useRef<any>(null);
  const [currentActions, setCurrentActions] = useState<ActionSheetOption[]>([]);
  useEffect(() => {
    if (searchValue && Array.isArray(listFriend)) {
      // Lọc danh sách thành viên theo tên
      const filteredMembers = listFriend.filter((member: any) =>
        member.Name.toLowerCase().includes(searchValue.toLowerCase()),
      );
      setlistFriend(filteredMembers);
    }
  }, [searchValue]);

  useEffect(() => {
    getListFriend();
  }, []);

  const getListFriend = async () => {
    if (!pros.profile) return;
    const result = await customerDA.getListFriend(pros.profile.Id);
    if (result) {
      setlistFriend(result);
    }
  };

  const openSheetWithActions = (actions: ActionSheetOption[]) => {
    setCurrentActions(actions);
    // // Delay 1 frame để chắc chắn actions được cập nhật trước khi mở
    // requestAnimationFrame(() => {
    //   SheetRef.current?.open();
    // });
    showBottomSheet({
      ref: bottomSheetRef,
      enableDismiss: true,
      title: 'Actions',
      children: (
        <View
          style={{
            height: Dimensions.get('window').height / 5,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          <ActionSheet actions={actions} onSelect={handleSelect} />
        </View>
      ),
    });
  };
  const handleSelect = (key: string, payload: any) => {
    console.log('Selected:', key);
    switch (key) {
      case 'addfriend':
        customerDA.Acceptfollow(payload.Id).then((res: any) => {
          if (res) {
            setlistFriend(
              listFriend.map((a: any) => {
                if (a.Id === payload.Id)
                  return {...a, Status: FollowStatus.Accept};
                return a;
              }),
            );
          }
        });
        break;
      case 'cancelfriend':
        customerDA.unfollow(payload.Id).then((res: any) => {
          if (res) {
            setlistFriend(listFriend.filter((a: any) => a.Id !== payload.Id));
          }
        });
        break;
      default:
        break;
    }
    hideBottomSheet(bottomSheetRef);
  };
  return (
    <View
      style={{
        height: Dimensions.get('window').height - 200,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      {/* <ActionSheet
        ref={SheetRef}
        actions={currentActions}
        onSelect={handleSelect}
      /> */}
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          height: 56,
          gap: 8,
          padding: 16,
        }}>
        <TextField
          style={{paddingHorizontal: 16, flex: 1, height: 40}}
          onChange={async (vl: string) => {
            setSearchValue(vl.trim());
          }}
          value={searchValue}
          placeholder="Tìm kiếm..."
          prefix={
            <Winicon
              src="outline/development/zoom"
              size={14}
              color={ColorThemes.light.Neutral_Text_Color_Subtitle}
            />
          }
        />
      </View>
      <View
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        <FlatList
          data={listFriend}
          scrollEnabled={pros.scrollEnabled}
          style={{
            height: '100%',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}
          keyExtractor={(item, index) => index.toString()}
          ListEmptyComponent={() => {
            return <EmptyPage title="Không có dữ liệu" />;
          }}
          renderItem={({item}) => {
            return (
              <ListTile
                key={item?.Id}
                onPress={() => {
                  navigate(RootScreen.ProfileCommunity, {Id: item?.Id});
                }}
                listtileStyle={{gap: 8}}
                leading={
                  item?.AvatarUrl ? (
                    <SkeletonImage
                      key={item?.AvatarUrl}
                      source={{
                        uri: item?.AvatarUrl
                          ? item?.AvatarUrl.includes('http')
                            ? item?.AvatarUrl
                            : `${ConfigAPI.urlImg + item?.AvatarUrl}`
                          : 'https://placehold.co/48/FFFFFF/000000/png',
                      }}
                      style={{
                        width: 48,
                        height: 48,
                        borderRadius: 50,
                        backgroundColor: '#f0f0f0',
                      }}
                    />
                  ) : (
                    <View
                      style={{
                        width: 48,
                        height: 48,
                        borderRadius: 50,
                        backgroundColor: ColorThemes.light.Primary_Color_Main,
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.heading7,
                          color:
                            ColorThemes.light.Neutral_Background_Color_Absolute,
                        }}>
                        {item?.Name ? item.Name.charAt(0).toUpperCase() : 'U'}
                      </Text>
                    </View>
                  )
                }
                title={
                  <Text
                    style={{
                      ...TypoSkin.heading7,
                      color: ColorThemes.light.Neutral_Text_Color_Title,
                    }}
                    numberOfLines={1}>
                    {item?.Name}
                  </Text>
                }
                subTitleStyle={{
                  ...TypoSkin.subtitle3,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}
                trailing={
                  <View
                    style={{
                      alignItems: 'center',
                      flexDirection: 'row',
                      gap: 8,
                    }}>
                    {item.Status === FollowStatus.Pending &&
                    pros.profile.Id === currentUser.Id ? (
                      <>
                        <TouchableOpacity
                          style={{padding: 4}}
                          onPress={() => {
                            customerDA
                              .Acceptfollow(item?.Id)
                              .then((res: any) => {
                                if (res) {
                                  setlistFriend(
                                    listFriend.map((a: any) => {
                                      if (a.Id === item.Id)
                                        return {
                                          ...a,
                                          Status: FollowStatus.Accept,
                                        };
                                      return a;
                                    }),
                                  );
                                }
                              });
                          }}>
                          <Winicon
                            src="fill/layout/circle-half-dotted-check"
                            color={ColorThemes.light.Success_Color_Main}
                            size={20}
                          />
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={{padding: 4}}
                          onPress={() => {
                            customerDA.unfollow(item?.Id).then((res: any) => {
                              if (res) {
                                setlistFriend(
                                  listFriend.filter(
                                    (a: any) => a.Id !== item.Id,
                                  ),
                                );
                              }
                            });
                          }}>
                          <Winicon
                            src="outline/layout/circle-xmark"
                            color={ColorThemes.light.Error_Color_Main}
                            size={20}
                          />
                        </TouchableOpacity>
                      </>
                    ) : null}
                    {item.Status === FollowStatus.Accept &&
                    pros.profile.Id === currentUser.Id ? (
                      <TouchableOpacity
                        style={{padding: 4}}
                        onPress={() => {
                          openSheetWithActions([
                            {
                              key: 'cancelfriend',
                              label: 'Hủy kết bạn',
                              payload: item,
                              icon: (
                                <Winicon
                                  src="fill/users/user-delete-line"
                                  size={16}
                                  color={ColorThemes.light.Error_Color_Main}
                                />
                              ),
                            },
                          ]);
                        }}>
                        <Winicon
                          src="fill/editing/dots"
                          color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                          size={16}
                        />
                      </TouchableOpacity>
                    ) : null}
                  </View>
                }
              />
            );
          }}
        />
      </View>
    </View>
  );
}

export const SkeletonFriendCard = () => {
  return (
    <SkeletonPlaceholder
      backgroundColor="#f0f0f0"
      highlightColor="#e0e0e0"
      speed={800}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        {/* Avatar placeholder */}
        <View
          style={{
            width: 48,
            height: 48,
            borderRadius: 50,
            marginRight: 12,
          }}
        />

        {/* Title and subtitle container */}
        <View style={{flex: 1, gap: 8}}>
          {/* Title placeholder */}
          <View
            style={{
              width: '60%',
              height: 16,
              borderRadius: 4,
            }}
          />

          {/* Subtitle placeholder */}
          <View
            style={{
              width: '40%',
              height: 12,
              borderRadius: 4,
            }}
          />
        </View>

        {/* Icons container */}
        <View style={{flexDirection: 'row', gap: 8}}>
          {/* Phone icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />

          {/* Chat icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};
