import {DrawerActions, useNavigation} from '@react-navigation/native';
import {useState, useRef, useEffect, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Dimensions,
  FlatList,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  FBottomSheet,
  FDialog,
  hideBottomSheet,
  Winicon,
  TextField,
  AppButton,
  showBottomSheet,
  ListTile,
  showSnackbar,
  ComponentStatus,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {navigate, RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {ProfileView} from './Chat';
import {DefaultPost, SkeletonPlacePostCard} from '../card/defaultPost';
import {Ultis} from '../../../utils/Utils';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import {useNewsFeedData} from '../hook/newsFeedHook';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {onShare} from '../../../features/share';
import React from 'react';
import {LogoImg} from '../../../Screen/Page/Home';

export default function Explore() {
  const [searchValue, setSearchValue] = useState('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState('');
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const flatListRef = useRef<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const dispatch: AppDispatch = useDispatch();

  // Debounce search value to prevent excessive filtering
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchValue(searchValue);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchValue]);

  // Search function to filter posts
  const searchPosts = useCallback((posts: any[], term: string) => {
    if (!term) return posts;

    const searchTerm = term.toLowerCase();
    return posts.filter((item: any) => {
      // Check post title/name
      const nameMatch = item?.Name?.toLowerCase().includes(searchTerm);
      // Check post content
      const contentMatch = item?.Content?.toLowerCase().includes(searchTerm);
      // Check author name if available
      const authorMatch = item?.relativeUser?.title
        ?.toLowerCase()
        .includes(searchTerm);

      return nameMatch || contentMatch || authorMatch;
    });
  }, []);

  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const user = useSelectorCustomerState().data;
  const size = 50;
  const {data, loading, page} = useNewsFeedData(1, size);
  const [hasMore, setHasMore] = useState(true); // Thêm state để kiểm tra còn data không

  const handleRefresh = async () => {
    if (!loading) {
      // Thêm check này để tránh gọi refresh khi đang loading
      setIsRefreshing(true);
      setHasMore(true);
      try {
        await dispatch(newsFeedActions.getNewFeedPopular(1, size));
      } catch (error) {
        console.error('Refresh error:', error);
      } finally {
      }
      setTimeout(() => {
        setIsRefreshing(false);
      }, 1000);
    }
  };
  const handleLoadMore = async () => {
    // Kiểm tra các điều kiện để loadmore
    if (!loading && !isRefreshing && hasMore) {
      try {
        const result = newsFeedActions.getNewFeedPopular(page + 1, size);

        // Kiểm tra kết quả trả về
        if ((result as any)?.payload?.length < size) {
          setHasMore(false); // Nếu số lượng data nhỏ hơn size, đánh dấu là hết data
        } else if ((result as any)?.payload?.length === 0) {
          setHasMore(false); // Nếu không có data trả về, đánh dấu là hết data
        }
      } catch (error) {
        console.error('Load more error:', error);
        setHasMore(false); // Nếu có lỗi, đánh dấu là hết data
      }
    }
  };

  const PostItem = React.memo(
    ({item, user, dialogRef}: {item: any; user: any; dialogRef: any}) => {
      return (
        <DefaultPost
          data={{
            ...item,
            relativeUser:
              item.CustomerId === user?.Id
                ? {
                    image: user?.AvatarUrl,
                    title: user?.Name,
                    subtitle: Ultis.getDiffrentTime(item.DateCreated),
                  }
                : item.relativeUser,
          }}
          containerStyle={{paddingHorizontal: 0}}
          onPressDetail={() => navigate(RootScreen.PostDetail, {item: item})}
          actionView={
            <View
              style={{
                flexDirection: 'row',
                paddingTop: 16,
                alignItems: 'center',
                gap: 8,
              }}>
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                containerStyle={{
                  padding: 4,
                  height: 24,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                }}
                onPress={async () => {
                  if (user) {
                    await dispatch(
                      newsFeedActions.updateLike(item.Id, item.IsLike === true),
                    );
                  } else {
                    ///TODO: check chưa login thì confirm ra trang login
                    dialogCheckAcc(dialogRef);
                  }
                }}
                title={
                  <Text
                    style={{
                      ...TypoSkin.buttonText5,
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}>
                    {item.Likes ?? 0}
                  </Text>
                }
                textColor={
                  item.IsLike === true
                    ? ColorThemes.light.Error_Color_Main
                    : ColorThemes.light.Neutral_Text_Color_Subtitle
                }
                prefixIconSize={12}
                prefixIcon={
                  item.IsLike === true
                    ? 'fill/emoticons/heart'
                    : 'outline/emoticons/heart'
                }
              />
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                containerStyle={{
                  padding: 4,
                  height: 24,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                }}
                onPress={async () => {
                  navigate(RootScreen.PostDetail, {item: item});
                }}
                prefixIcon={'outline/user interface/b-comment'}
                prefixIconSize={12}
                textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                title={
                  <Text
                    style={{
                      ...TypoSkin.buttonText5,
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}>
                    {item.Comment ?? 0}
                  </Text>
                }
              />
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                containerStyle={{
                  padding: 4,
                  height: 24,
                  paddingVertical: 0,
                  paddingHorizontal: 8,
                }}
                onPress={async () => {
                  onShare({content: 'Hello world'});
                }}
                prefixIcon={'fill/arrows/social-sharing'}
                prefixIconSize={12}
                textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
              />
            </View>
          }
          onPressHeader={() => {
            if (item.GroupId) {
              navigate(RootScreen.GroupIndex, {Id: item.GroupId});
            } else {
              navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
            }
          }}
          trailingView={
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 4,
              }}>
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                onPress={async () => {
                  if (user) {
                    await dispatch(
                      newsFeedActions.addBookmark(
                        item.Id,
                        item.IsBookmark === true,
                      ),
                    );
                  } else {
                    ///TODO: check chưa login thì confirm ra trang login
                    dialogCheckAcc(dialogRef);
                  }
                }}
                containerStyle={{
                  borderRadius: 100,
                  padding: 6,
                  height: 24,
                  width: 24,
                }}
                title={
                  <Winicon
                    src={
                      item.IsBookmark === true
                        ? 'fill/user interface/bookmark'
                        : 'outline/user interface/bookmark'
                    }
                    size={14}
                    color={
                      item.IsBookmark === true
                        ? ColorThemes.light.Warning_Color_Main
                        : ColorThemes.light.Neutral_Text_Color_Subtitle
                    }
                  />
                }
              />
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                onPress={() => {
                  showBottomSheet({
                    ref: bottomSheetRef,
                    title: t('community.actions'),
                    suffixAction: <View />,
                    prefixAction: (
                      <TouchableOpacity
                        onPress={() => hideBottomSheet(bottomSheetRef)}
                        style={{padding: 6, alignItems: 'center'}}>
                        <Winicon
                          src="outline/layout/xmark"
                          size={20}
                          color={ColorThemes.light.Neutral_Text_Color_Body}
                        />
                      </TouchableOpacity>
                    ),
                    children: (
                      <View
                        style={{
                          gap: 8,
                          height: Dimensions.get('window').height / 4,
                          width: '100%',
                          backgroundColor:
                            ColorThemes.light.Neutral_Background_Color_Absolute,
                        }}>
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);

                            showSnackbar({
                              message: t('community.featureInDevelopment'),
                              status: ComponentStatus.WARNING,
                            });
                          }}
                          title={t('community.reportPost')}
                          titleStyle={{...TypoSkin.body3}}
                        />
                        {item.CustomerId === user.Id && (
                          <ListTile
                            onPress={() => {
                              hideBottomSheet(bottomSheetRef);

                              navigation.push(RootScreen.createPost, {
                                editPost: item,
                                groupId: null,
                              });
                              // showSnackbar({
                              //   message: 'Chức năng đang được phát triển',
                              //   status: ComponentStatus.WARNING,
                              // });
                            }}
                            title={t('community.editPost')}
                            titleStyle={{...TypoSkin.body3}}
                          />
                        )}
                        {item.CustomerId === user.Id && (
                          <ListTile
                            onPress={() => {
                              hideBottomSheet(bottomSheetRef);
                              dispatch(newsFeedActions.deletePost(item));
                            }}
                            title={t('community.deletePost')}
                            titleStyle={{...TypoSkin.body3}}
                          />
                        )}
                      </View>
                    ),
                  });
                }}
                containerStyle={{
                  borderRadius: 100,
                  padding: 6,
                  height: 24,
                  width: 24,
                }}
                title={
                  <Winicon
                    src={'fill/user interface/menu-dots'}
                    size={14}
                    color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                  />
                }
              />
            </View>
          }
          showContent={true}
        />
      );
    },
  );

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      {/* header */}
      <ListTile
        style={{
          padding: 0,
          paddingBottom: 8,
          paddingHorizontal: 16,
        }}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={{padding: 4}}>
            <LogoImg />
          </TouchableOpacity>
        }
        title={t('community.tabs.explore')}
        trailing={<ProfileView />}
        bottom={
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              height: 56,
              gap: 8,
              paddingTop: 16,
              paddingBottom: 16,
            }}>
            <TextField
              style={{paddingHorizontal: 16, flex: 1, height: 40}}
              onChange={(vl: string) => {
                setSearchValue(vl.trim());
              }}
              value={searchValue}
              placeholder={t('common.search')}
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
              }
              suffix={
                searchValue ? (
                  <TouchableOpacity
                    onPress={() => setSearchValue('')}
                    style={{padding: 4}}>
                    <Winicon
                      src="outline/layout/xmark"
                      size={14}
                      color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                    />
                  </TouchableOpacity>
                ) : null
              }
            />
          </View>
        }
      />

      <FlatList
        ref={flatListRef}
        data={searchPosts(data, debouncedSearchValue)}
        renderItem={({item}) => (
          <PostItem item={item} user={user} dialogRef={dialogRef} />
        )}
        keyExtractor={item => item.Id.toString()}
        showsVerticalScrollIndicator={false}
        style={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />
        }
        contentContainerStyle={{
          gap: 8,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
        }}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (loading) {
            return (
              <View style={{gap: 8}}>
                {[1, 2, 3].map((_, index) => (
                  <SkeletonPlacePostCard key={`skeleton-${index}`} />
                ))}
              </View>
            );
          }
          return <EmptyPage />;
        }}
        ListFooterComponent={() => {
          if (loading && !isRefreshing) {
            return <SkeletonPlacePostCard />;
          }
          if (!hasMore && data.length > 0) {
            return (
              <View
                style={{
                  padding: 16,
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    ...TypoSkin.subtitle2,
                  }}>
                  {t('common.noMoreData')}
                </Text>
              </View>
            );
          }
          return null;
        }}
      />
    </View>
  );
}
