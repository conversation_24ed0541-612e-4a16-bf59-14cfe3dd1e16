import {
  FlatList,
  Image,
  KeyboardTypeOptions,
  ReturnKeyTypeOptions,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {RadioButton, TextField} from 'wini-mobile-components';
import {Text} from 'react-native-paper';
import {Control, Controller, FieldErrors, FieldValues} from 'react-hook-form';
import React from 'react';
import ImageCropPicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import {FontAwesomeIcon} from '@fortawesome/react-native-fontawesome';
import {faMinusCircle} from '@fortawesome/free-solid-svg-icons';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {TypoSkin} from '../../../../assets/skin/typography';
import {Ultis} from '../../../../utils/Utils';

export const TextFieldForm = ({
  loading = false,
  label,
  labelStyle,
  secureTextEntry,
  textFieldStyle = {},
  returnKeyType = 'done',
  onSubmit,
  numberOfLines,
  register,
  placeholder,
  required = false,
  control,
  name,
  style = {},
  disabled = false,
  prefix,
  suffix,
  errors,
  multiline = false,
  type,
  autoFocus = false,
  onFocus,
  onBlur,
  maxLength,
  textStyle = {},
}: {
  loading?: boolean;
  secureTextEntry?: boolean;
  register?: any;
  label?: string;
  returnKeyType?: ReturnKeyTypeOptions | undefined;
  numberOfLines?: number;
  textFieldStyle?: ViewStyle;
  labelStyle?: TextStyle;
  placeholder?: string;
  required?: boolean;
  control: Control<FieldValues>;
  name: string;
  maxLength?: number;
  style?: TextStyle;
  textStyle?: TextStyle;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  disabled?: boolean;
  errors: FieldErrors<FieldValues>;
  multiline?: boolean;
  type?: KeyboardTypeOptions | 'money';
  autoFocus?: boolean;
  onFocus?: (value: string) => void;
  onBlur?: (value: string) => void;
  onSubmit?: (value: string) => void;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => (
        <View
          style={[
            {gap: 8, overflow: 'visible'},
            style ?? {backgroundColor: '#fff'},
          ]}>
          {label ? (
            <View style={{flexDirection: 'row', gap: 4, alignItems: 'center'}}>
              <Text
                numberOfLines={1}
                style={[TypoSkin.label3, {...labelStyle}]}>
                {label}
              </Text>
              {required ? (
                <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
              ) : null}
              {maxLength ? (
                <Text
                  style={[
                    TypoSkin.regular1,
                    {
                      flex: 1,
                      textAlign: 'right',
                      color: '#61616B',
                    },
                  ]}>
                  {field.value?.length ?? 0}/{maxLength}
                </Text>
              ) : undefined}
            </View>
          ) : null}
          {loading ? (
            <SkeletonPlaceholder>
              <SkeletonPlaceholder.Item
                height={40}
                width={'100%'}
                borderRadius={8}></SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
          ) : (
            <TextField
              style={{
                ...textFieldStyle,
                width: '100%',
                paddingVertical: multiline ? 4 : 0,
                ...(type === 'money' ? {paddingRight: 0} : {}),
                ...textStyle,
              }}
              placeholder={
                placeholder
                  ? placeholder
                  : label
                  ? `Nhập ${label.toLowerCase()}`
                  : ''
              }
              value={field.value}
              numberOfLines={numberOfLines}
              onChange={field.onChange}
              onFocus={
                type === 'money'
                  ? () => {
                      if (field.value)
                        field.onChange(field.value.replaceAll(',', ''));
                      if (onFocus) onFocus(field.value);
                    }
                  : onFocus
                  ? () => {
                      onFocus(field.value);
                    }
                  : undefined
              }
              returnKeyType={returnKeyType}
              secureTextEntry={secureTextEntry}
              onBlur={
                type === 'money'
                  ? () => {
                      if (
                        field.value &&
                        isNaN(parseFloat(field.value?.replaceAll(',', '')))
                      ) {
                        field.onChange(0);
                      } else {
                        field.onChange(Ultis.money(field.value));
                      }
                      if (onBlur) onBlur(field.value);
                    }
                  : onBlur
                  ? () => {
                      onBlur(field.value);
                    }
                  : undefined
              }
              disabled={disabled}
              multiline={multiline}
              type={type === 'money' ? 'number-pad' : type}
              autoFocus={autoFocus}
              prefix={prefix}
              onSubmit={onSubmit}
              maxLength={maxLength}
              suffix={
                suffix ??
                (type === 'money' ? (
                  <View style={[styles.unitVND, {width: 40}]}>
                    <Text style={[TypoSkin.regular2]}>VNĐ</Text>
                  </View>
                ) : undefined)
              }
              helperText={
                convertErrors(errors, name) &&
                (convertErrors(errors, name)?.message?.length
                  ? convertErrors(errors, name)?.message
                  : `Vui lòng nhập ${(placeholder
                      ? placeholder
                      : label
                      ? `${label}`
                      : 'giá trị'
                    ).toLowerCase()}`)
              }
            />
          )}
        </View>
      )}
    />
  );
};

export const FRadioForm = ({
  value,
  control,
  onChange,
  name,
  label,
  style,
}: {
  value: string;
  onChange?: (value: string) => void;
  control: Control<FieldValues>;
  name: string;
  style?: TextStyle;
  label?: string;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({field}) => (
        <TouchableOpacity
          onPress={() => field.onChange(value)}
          style={[
            {
              flexDirection: 'row',
              alignSelf: 'baseline',
              gap: 4,
              alignItems: 'center',
            },
            style ?? {},
          ]}>
          <RadioButton
            status={field.value === value}
            value={value}
            onPress={vl => {
              if (vl) field.onChange(value);
            }}
          />
          {label ? (
            <Text numberOfLines={1} style={TypoSkin.regular2}>
              {label}
            </Text>
          ) : null}
        </TouchableOpacity>
      )}
    />
  );
};

// export const Fselect1Form = ({ loading = false, label, placeholder, required = false, control, name, style = {}, disabled = false, errors, options = [], onSearch, allowSearch = false, loadMore, onChange, onTap }:
//     { loading?: boolean, label?: string, placeholder?: string, required?: boolean, onChange?: (vl: any) => void, onTap?: () => void, control: Control<FieldValues>, name: string, style?: TextStyle, disabled?: boolean, errors: FieldErrors<FieldValues>, options: Array<{ id: string | number, name: string | ReactNode }>, allowSearch?: boolean, onSearch?: (value?: string) => Promise<Array<{ id: string | number; name: string | ReactNode }> | undefined>, loadMore?: (value?: string) => Promise<Array<{ id: string | number; name: string | ReactNode }> | undefined> }) => {
//     return <Controller
//         control={control}
//         name={name}
//         rules={{ required: required }}
//         render={({ field }) => {
//             return <View style={[{ gap: 8, overflow: 'visible' }, style]}>
//                 {label ? <View style={{ flexDirection: 'row', gap: 4 }}>
//                     <Text numberOfLines={1} style={TypoSkin.label3}>{label}</Text>
//                     {required ? <Text style={[TypoSkin.label4, { color: '#E14337' }]}>*</Text> : null}
//                 </View> : null}
//                 {loading ? <SkeletonPlaceholder>
//                     <SkeletonPlaceholder.Item
//                         height={style.height ?? 40}
//                         width={'100%'}
//                         borderRadius={8}
//                     >
//                     </SkeletonPlaceholder.Item>
//                 </SkeletonPlaceholder> : <FSelect1
//                     onTap={disabled ? undefined : () => {
//                         if (onTap) onTap()
//                     }}
//                     style={{ width: '100%', height: style.height ?? 40 }}
//                     placeholder={placeholder ? placeholder : label ? `Chọn ${label.toLowerCase()}` : ''}
//                     value={field.value}
//                     onChange={(vl) => {
//                         field.onChange(vl.id)
//                         if (onChange) onChange(vl)
//                     }}
//                     allowSearch={allowSearch}
//                     onSearch={onSearch}
//                     loadMore={loadMore}
//                     data={options}
//                     disabled={disabled}
//                     helperText={convertErrors(errors, name) && (convertErrors(errors, name)?.message?.length ? convertErrors(errors, name)?.message : `Vui lòng nhập ${(placeholder ? placeholder : label ? `Nhập ${label}` : 'gía trị').toLowerCase()}`)} />}
//             </View>
//         }}
//     />
// }

// export const FMultiSelectForm = ({ loading = false, label, placeholder, required = false, control, name, style = {}, disabled = false, errors, options = [], onSearch, loadMore }:
//     { loading?: boolean, label?: string, placeholder?: string, required?: boolean, control: Control<FieldValues>, name: string, style?: TextStyle, disabled?: boolean, errors: FieldErrors<FieldValues>, options: Array<{ id: string | number, name: string | ReactNode }>, allowSearch?: boolean, onSearch?: (value?: string) => Promise<Array<{ id: string | number; name: string | ReactNode }> | undefined>, loadMore?: (value?: string) => Promise<Array<{ id: string | number; name: string | ReactNode }> | undefined> }) => {
//     return <Controller
//         control={control}
//         name={name}
//         rules={{ required: required }}
//         render={({ field }) => <View style={[{ gap: 8, overflow: 'visible' }, style]}>
//             {label ? <View style={{ flexDirection: 'row', gap: 4 }}>
//                 <Text numberOfLines={1} style={TypoSkin.label3}>{label}</Text>
//                 {required ? <Text style={[TypoSkin.label4, { color: '#E14337' }]}>*</Text> : null}
//             </View> : null}
//             {
//                 loading ? <SkeletonPlaceholder >
//                     <SkeletonPlaceholder.Item
//                         height={40}
//                         width={'100%'}
//                         borderRadius={8}
//                     >
//                     </SkeletonPlaceholder.Item>
//                 </SkeletonPlaceholder> : <MultiSelect
//                     style={{ width: '100%' }}
//                     placeholder={placeholder ? placeholder : label ? `Chọn ${label.toLowerCase()}` : ''}
//                     value={field.value}
//                     onChange={(item) => {
//                         field.onChange(item)
//                     }}
//                     onSearch={onSearch}
//                     loadMore={loadMore}
//                     data={options}
//                     disabled={disabled}
//                     helperText={convertErrors(errors, name) && (convertErrors(errors, name)?.message?.length ? convertErrors(errors, name)?.message : `Vui lòng nhập ${(placeholder ? placeholder : label ? `Nhập ${label}` : 'gía trị').toLowerCase()}`)}
//                 />
//             }
//         </View>}
//     />
// }

export const FImportMultipleImg = ({
  loading = false,
  multipleImgs = true,
  linkImgs = [],
  label,
  required = false,
  control,
  name,
  style = {},
  errors,
  handlePickImg,
  pickerStyle = {},
}: {
  loading?: boolean;
  multipleImgs?: boolean;
  linkImgs: Array<string>;
  label?: string;
  required?: boolean;
  control: Control<FieldValues>;
  name: string;
  style?: TextStyle;
  pickerStyle?: TextStyle;
  disabled?: boolean;
  errors: FieldErrors<FieldValues>;
  handlePickImg: (value: Array<ImageOrVideo>) => Promise<any>;
}) => {
  async function pickerImg() {
    if (multipleImgs) {
      const img = await ImageCropPicker.openPicker({
        multiple: true,
        cropping: false,
        maxFiles: 5,
      });
      const res = await handlePickImg(img);
      return res;
    } else {
      const img = await ImageCropPicker.openPicker({
        multiple: false,
        cropping: false,
        maxFiles: 5,
      });
      const res = await handlePickImg([img]);
      return res;
    }
  }

  return (
    <Controller
      control={control}
      name={name}
      rules={{required: required}}
      render={({field}) => {
        return (
          <View style={[{overflow: 'visible', position: 'relative'}, style]}>
            {label ? (
              <View style={{flexDirection: 'row', gap: 4}}>
                <Text numberOfLines={1} style={TypoSkin.label3}>
                  {label}
                </Text>
                {required ? (
                  <Text style={[TypoSkin.label4, {color: '#E14337'}]}>*</Text>
                ) : null}
              </View>
            ) : null}
            {loading ? (
              <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item
                  height={pickerStyle.height ?? 104}
                  width={'100%'}
                  borderRadius={
                    pickerStyle.borderRadius ?? 8
                  }></SkeletonPlaceholder.Item>
              </SkeletonPlaceholder>
            ) : (
              <FlatList
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{paddingVertical: 12}}
                data={linkImgs}
                keyExtractor={(item, i) => item + '-' + i}
                ItemSeparatorComponent={() => <View style={{width: 12}} />}
                ListHeaderComponent={() => {
                  if (multipleImgs == false && linkImgs.length != 0)
                    return null;
                  return (
                    <TouchableOpacity
                      onPress={async () => {
                        const res = await pickerImg();
                        field.onChange(res);
                      }}
                      style={{
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: 8,
                        borderWidth: 0.4,
                        borderColor: '#61616B',
                        borderStyle: 'dashed',
                        borderRadius: 8,
                        height: 104,
                        width: 104,
                        marginTop: 12,
                        ...pickerStyle,
                      }}>
                      <Text
                        numberOfLines={1}
                        style={{
                          ...TypoSkin.buttonText4,
                          color: '#61616B',
                        }}>
                        Thêm ảnh
                      </Text>
                    </TouchableOpacity>
                  );
                }}
                renderItem={({item, index}) => {
                  return (
                    <View
                      style={{
                        position: 'relative',
                        overflow: 'visible',
                        padding: multipleImgs ? 12 : 0,
                        paddingBottom: 0,
                      }}>
                      <Image
                        source={{uri: item}}
                        style={{
                          height: pickerStyle.height ?? 104,
                          minWidth: 104,
                          maxWidth: 132,
                          borderRadius: pickerStyle.borderRadius ?? 8,
                        }}
                      />
                      <TouchableOpacity
                        onPress={() => {
                          // let newValue = field.value.replace(item.replace(ConfigAPI.imgUrlId, ''), '')
                          // newValue = newValue.split(',').filter((e: string) => e.length > 0)
                          // field.onChange(newValue.join(','))
                        }}
                        style={{
                          padding: multipleImgs ? 4 : 0,
                          position: 'absolute',
                          top: 0,
                          right: 0,
                        }}>
                        <FontAwesomeIcon
                          icon={faMinusCircle}
                          size={20}
                          color="#667994"
                          style={{backgroundColor: '#fff', borderRadius: 20}}
                        />
                      </TouchableOpacity>
                    </View>
                  );
                }}
              />
            )}
            {errors[name] ? (
              <Text
                numberOfLines={1}
                style={[
                  TypoSkin.subtitle3,
                  {
                    color: '#61616B',
                    position: 'absolute',
                    bottom: 0,
                    left: 2,
                    transform: [{translateY: 22}],
                  },
                ]}>
                {convertErrors(errors, name)?.message?.length
                  ? convertErrors(errors, name)?.message
                  : `Vui lòng chọn ít nhất 1 ảnh`}
              </Text>
            ) : null}
          </View>
        );
      }}
    />
  );
};

const convertErrors = (errors: any, name: string) => {
  if (errors && Object.keys(errors).length) {
    const props = name.split(/[.\[\]]/).filter(e => e?.length > 0);
    var value = errors;
    for (let p of props) {
      if (value) value = value[p];
    }
    return value;
  }
  return undefined;
};

const styles = StyleSheet.create({
  unitVND: {
    backgroundColor: '#61616B',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    width: 56,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
});
