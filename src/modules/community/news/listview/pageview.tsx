import React, {useState} from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  Dimensions,
  FlatList,
} from 'react-native';
import PagerView from 'react-native-pager-view';
import {ColorThemes} from '../../../../assets/skin/colors';
import {SkeletonPlaceCard} from '../card/defaultImage';
import {TypoSkin} from '../../../../assets/skin/typography';
import {DefaultProduct} from '../../../Default/card/defaultProduct';

interface Props {
  titleList?: string;
}

export default function DefaultPageView(props: Props) {
  const data1 = [
    {
      Id: '1',
      Name: 'Item 1',
      Img: null,
      Description: 'Description 1',
      Price: 100,
      Rate: 4,
    },
    {
      Id: '2',
      Name: 'Item 1',
      Img: null,
      Description: 'Description 1',
      Price: 100,
      Rate: 4,
    },
    {
      Id: '3',
      Name: 'Item 1',
      Img: null,
      Description: 'Description 1',
      Price: 100,
      Rate: 4,
    },
    {
      Id: '4',
      Name: 'Item 1',
      Img: null,
      Description: 'Description 1',
      Price: 100,
      Rate: 4,
    },
    {
      Id: '5',
      Name: 'Item 1',
      Img: null,
      Description: 'Description 1',
      Price: 100,
      Rate: 4,
    },
    {
      Id: '6',
      Name: 'Item 1',
      Img: null,
      Description: 'Description 1',
      Price: 100,
      Rate: 4,
    },
    {
      Id: '7',
      Name: 'Item 1',
      Img: null,
      Description: 'Description 1',
      Price: 100,
      Rate: 4,
    },
    {
      Id: '8',
      Name: 'Item 1',
      Img: null,
      Description: 'Description 1',
      Price: 100,
      Rate: 4,
    },
    {
      Id: '9',
      Name: 'Item 1',
      Img: null,
      Description: 'Description 1',
      Price: 100,
      Rate: 4,
    },
  ];

  const [isLoading, setLoading] = useState(false);
  const [isRefresh, setRefresh] = useState(false);
  const [isLoadMore, setLoadMore] = useState(false);

  const pages = (array: any[], size: number) => {
    const result = [];
    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size));
    }
    return result;
  };

  return (
    <View style={{width: '100%', height: undefined}}>
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingBottom: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}>
            {props.titleList}
          </Text>
        </View>
      ) : null}
      <PagerView style={{height: 434}} initialPage={0}>
        {pages(data1, 5).map((item, index) => (
          <View key={index}>
            <FlatList
              nestedScrollEnabled
              scrollEnabled={false}
              data={item}
              contentContainerStyle={{
                gap: 16,
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}
              renderItem={({item, index}) => {
                return (
                  <DefaultProduct
                    key={index}
                    flexDirection="row"
                    containerStyle={{
                      paddingHorizontal: 16,
                    }}
                    data={item}
                  />
                );
              }}
              style={{width: '100%', height: '100%'}}
              keyExtractor={item => item.Id.toString()}
              onEndReachedThreshold={0.5}
              ListEmptyComponent={() => {
                if (isLoading) {
                  return <SkeletonPlaceCard />;
                }
                if (isLoadMore) {
                  return (
                    <View
                      style={{
                        justifyContent: 'center',
                        alignItems: 'center',
                        flexDirection: 'row',
                      }}>
                      <ActivityIndicator
                        color={ColorThemes.light.Primary_Color_Main}
                      />
                    </View>
                  );
                }
                return <Text style={{color: '#000000'}}>Không có dữ liệu</Text>;
              }}
            />
          </View>
        ))}
      </PagerView>
    </View>
  );
}
