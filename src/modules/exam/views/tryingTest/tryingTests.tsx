/* eslint-disable react/no-unstable-nested-components */
import {
  FlatList,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
  useWindowDimensions,
} from 'react-native';
import {ColorThemes} from '../../../../assets/skin/colors';
import {useEffect, useState} from 'react';
import {AppButton, ListTile, Winicon} from 'wini-mobile-components';
import {useTranslation} from 'react-i18next';
import {TypoSkin} from '../../../../assets/skin/typography';
import {navigate, RootScreen} from '../../../../router/router';
import {examDA} from '../../da';
import {ExamType} from '../../../../Config/Contanst';
import EmptyPage from '../../../../Screen/emptyPage';
import TitleWithBottom from '../../../../Screen/Layout/titleWithBottom';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import {Tab<PERSON><PERSON>, TabView} from 'react-native-tab-view';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {LogoImg} from '../../../../Screen/Page/Home';
import {ScrollView} from 'react-native-gesture-handler';

// Skeleton placeholder for exam list items
const SkeletonPlaceCard = () => {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View
        style={{
          borderColor: ColorThemes.light.Neutral_Border_Color_Main,
          borderWidth: 1,
          marginHorizontal: 16,
          marginBottom: 16,
          borderRadius: 8,
        }}>
        {/* Title */}
        <View style={{padding: 16}}>
          <View
            style={{
              width: '70%',
              height: 18,
              borderRadius: 4,
              marginBottom: 8,
            }}
          />
          <View
            style={{
              width: '50%',
              height: 16,
              borderRadius: 4,
            }}
          />
        </View>

        {/* Bottom section */}
        <View
          style={{
            paddingHorizontal: 16,
            paddingBottom: 16,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
          <View
            style={{
              width: 100,
              height: 24,
              borderRadius: 4,
            }}
          />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};

export default function TryingTests() {
  const layout = useWindowDimensions();
  const [index, setIndex] = useState(0);
  const [isLoading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [data, setData] = useState<Array<any>>([]);
  const {t} = useTranslation();
  const exam = new examDA();
  const [ListSection, setListSection] = useState<Array<any>>([]);

  // Define routes for the tab view
  const routes = [
    {key: 'myExams', title: 'Thi thử'},
    {key: 'tryExams', title: 'Thi thật'},
  ];

  useEffect(() => {
    if (index === 0) {
      getDataTry();
    } else {
      getDataReal();
    }
  }, [index]);

  const getDataTry = async () => {
    setLoading(true);
    const result = await exam.getListExamTry();
    if (result) {
      setListSection(result.Section);
      setData(result.data);
    }
    setLoading(false);
  };

  const getDataReal = async () => {
    setLoading(true);
    const result = await exam.getListExamReal();
    if (result) {
      setListSection(result.Section);
      setData(result.data);
    }
    setLoading(false);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    if (index === 0) {
      getDataTry();
    } else {
      getDataReal();
    }
    setRefreshing(false);
  };

  const navigation = useNavigation<any>();

  // Custom tab bar renderer
  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      activeColor={ColorThemes.light.Primary_Color_Main}
      indicatorStyle={{
        backgroundColor: ColorThemes.light.Primary_Color_Main,
        height: 1.5,
      }}
      tabStyle={{paddingHorizontal: 4, paddingTop: 0}}
      inactiveColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
      style={{
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        height: 40,
        elevation: 0,
      }}
      labelStyle={{...TypoSkin.label4}}
    />
  );

  // Render the exam list
  const renderExamList = () => {
    return (
      <FlatList
        data={data}
        contentContainerStyle={{
          paddingTop: 16,
          gap: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ColorThemes.light.Primary_Color_Main]}
            tintColor={ColorThemes.light.Primary_Color_Main}
          />
        }
        // ListHeaderComponent={() => {
        //   if (isLoading) {
        //     return null;
        //   }
        //   return (
        //     <View style={{flexDirection: 'row', justifyContent: 'flex-end'}}>
        //       <AppButton
        //         title={'Lịch sử thi'}
        //         backgroundColor={
        //           ColorThemes.light.Neutral_Background_Color_Main
        //         }
        //         borderColor="transparent"
        //         containerStyle={{
        //           borderRadius: 8,
        //           marginTop: 16,
        //           marginHorizontal: 16,
        //           paddingHorizontal: 12,
        //           alignSelf: 'baseline',
        //         }}
        //         onPress={async () => {
        //           navigate(RootScreen.historyTryingList, {
        //             idsExam: data?.map((item: any) => item.Id),
        //           });
        //         }}
        //         textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
        //         textStyle={{...TypoSkin.buttonText3}}
        //         suffixIcon={'outline/arrows/refresh'}
        //         suffixIconSize={14}
        //       />
        //     </View>
        //   );
        // }}
        renderItem={({item, index}) => {
          return (
            <ListTile
              key={index}
              onPress={() => {
                navigation.push(RootScreen.OverviewTest, {
                  id: item.Id,
                  type: ExamType.Try,
                  totalQuestions: item.totalQuestions,
                  timeLimit: item.Time,
                  Score: item.Score ?? 0,
                  name: item.Name,
                  section: ListSection,
                });
              }}
              style={{
                marginHorizontal: 16,
                padding: 0,
                paddingHorizontal: 16,
                paddingVertical: 16,
                borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                borderWidth: 1,
                borderRadius: 8,
              }}
              title={`${item.Name}`}
              subtitle={
                <View
                  style={{
                    width: '100%',
                    gap: 4,
                  }}>
                  <Text
                    style={{
                      ...TypoSkin.subtitle3,
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}>
                    {item.Name}
                  </Text>
                  <View
                    style={{
                      width: '100%',
                      justifyContent: 'flex-start',
                      flexDirection: 'row',
                      alignItems: 'center',
                      paddingBottom: 8,
                      paddingTop: 4,
                    }}>
                    <AppButton
                      prefixIcon={'outline/buildings/time-clock'}
                      prefixIconSize={16}
                      title={`${item.Time ?? 0} phút`}
                      containerStyle={{alignSelf: 'baseline', height: 24}}
                      textStyle={{...TypoSkin.subtitle3}}
                      textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      borderColor="transparent"
                      backgroundColor={'transparent'}
                    />
                    <AppButton
                      prefixIcon={'outline/layout/circle-question'}
                      prefixIconSize={16}
                      title={`${item.totalQuestions ?? 0} Câu`}
                      containerStyle={{alignSelf: 'baseline', height: 24}}
                      textStyle={{...TypoSkin.subtitle3}}
                      textColor={ColorThemes.light.neutral_text_subtitle_color}
                      borderColor="transparent"
                      backgroundColor={'transparent'}
                    />
                  </View>
                </View>
              }
            />
          );
        }}
        style={{width: '100%', height: '100%'}}
        keyExtractor={a => a.Id?.toString()}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={() => {
          if (isLoading) {
            return (
              <View style={{paddingTop: 16}}>
                {[1, 2, 3].map((_, idx) => (
                  <SkeletonPlaceCard key={`skeleton-${idx}`} />
                ))}
              </View>
            );
          }
          return <EmptyPage title={t('nodata')} />;
        }}
      />
    );
  };

  return (
    <TitleWithBottom
      title="Luyện đề"
      prefix={
        <TouchableOpacity
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
          style={{padding: 4}}>
          <LogoImg />
        </TouchableOpacity>
      }>
      <View
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        {/* create tabbar not use package and show content by index */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
            height: 40,
            elevation: 0,
          }}>
          {routes.map((route, idx) => (
            <TouchableOpacity
              key={idx}
              onPress={() => setIndex(idx)}
              style={{
                flex: 1,
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                borderBottomWidth: 2,
                borderBottomColor:
                  idx === index
                    ? ColorThemes.light.Primary_Color_Main
                    : 'transparent',
              }}>
              <Text
                style={{
                  ...TypoSkin.label4,
                  color:
                    idx === index
                      ? ColorThemes.light.Primary_Color_Main
                      : ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}>
                {route.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        {renderExamList()}
        {/* <TabView
          navigationState={{index, routes}}
          renderScene={renderScene}
          renderTabBar={renderTabBar}
          onIndexChange={setIndex}
          initialLayout={{width: layout.width}}
        /> */}
      </View>
    </TitleWithBottom>
  );
}
