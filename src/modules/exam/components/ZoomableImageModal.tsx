import React, {useState} from 'react';
import {
  Modal,
  View,
  Dimensions,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Text,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withSpring,
} from 'react-native-reanimated';
import {GestureDetector, Gesture} from 'react-native-gesture-handler';
import {SafeAreaView} from 'react-native-safe-area-context';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface ZoomableImageModalProps {
  visible: boolean;
  imageUri: string;
  onClose: () => void;
}

const ZoomableImageModal: React.FC<ZoomableImageModalProps> = ({
  visible,
  imageUri,
  onClose,
}) => {
  const [lastTap, setLastTap] = useState(0);

  // Reanimated shared values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const savedScale = useSharedValue(1);
  const savedTranslateX = useSharedValue(0);
  const savedTranslateY = useSharedValue(0);

  // Helper function to clamp values within bounds
  const clampTranslate = (
    value: number,
    currentScale: number,
    dimension: 'x' | 'y',
  ) => {
    'worklet';
    const screenSize = dimension === 'x' ? screenWidth : screenHeight * 0.8;
    const imageSize = screenSize * currentScale;
    const maxOffset = Math.max(0, (imageSize - screenSize) / 2);
    return Math.max(-maxOffset, Math.min(maxOffset, value));
  };

  // Reset function
  const resetZoom = () => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    savedScale.value = 1;
    savedTranslateX.value = 0;
    savedTranslateY.value = 0;
  };

  // Create gestures
  const pinchGesture = Gesture.Pinch()
    .onStart(() => {
      savedScale.value = scale.value;
    })
    .onUpdate(event => {
      const newScale = Math.max(
        0.5,
        Math.min(4, savedScale.value * event.scale),
      );
      scale.value = newScale;
    })
    .onEnd(() => {
      if (scale.value < 1) {
        runOnJS(resetZoom)();
      }
    });

  const panGesture = Gesture.Pan()
    .onStart(() => {
      savedTranslateX.value = translateX.value;
      savedTranslateY.value = translateY.value;
    })
    .onUpdate(event => {
      if (scale.value > 1) {
        const newTranslateX = savedTranslateX.value + event.translationX;
        const newTranslateY = savedTranslateY.value + event.translationY;

        translateX.value = clampTranslate(newTranslateX, scale.value, 'x');
        translateY.value = clampTranslate(newTranslateY, scale.value, 'y');
      }
    });

  const composedGesture = Gesture.Simultaneous(pinchGesture, panGesture);

  // Animated styles
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {scale: scale.value},
        {translateX: translateX.value},
        {translateY: translateY.value},
      ],
    };
  });

  const handleDoubleTap = () => {
    const now = Date.now();
    const DOUBLE_TAP_DELAY = 300;

    if (now - lastTap < DOUBLE_TAP_DELAY) {
      // Double tap detected
      if (scale.value > 1) {
        resetZoom();
      } else {
        // Zoom to 2x and center the image
        scale.value = withSpring(2);
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        savedScale.value = 2;
        savedTranslateX.value = 0;
        savedTranslateY.value = 0;
      }
    }
    setLastTap(now);
  };

  const handleClose = () => {
    resetZoom();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onDismiss={handleClose}
      onRequestClose={handleClose}
      statusBarTranslucent={true}>
      <StatusBar backgroundColor="rgba(0,0,0,0.9)" barStyle="light-content" />
      <View style={styles.modalContainer}>
        {/* Background overlay - tap to close */}
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={handleClose}
        />

        {/* Close button */}
        <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
          <Winicon
            src="outline/layout/xmark"
            size={24}
            color={ColorThemes.light.Neutral_Background_Color_Absolute}
          />
        </TouchableOpacity>

        {/* Zoomable image */}
        <GestureDetector gesture={composedGesture}>
          <View style={styles.imageContainer}>
            <Animated.View
              style={[styles.animatedImageContainer, animatedStyle]}>
              <TouchableOpacity
                activeOpacity={1}
                onPress={handleDoubleTap}
                style={styles.imageWrapper}>
                <FastImage
                  source={{uri: imageUri}}
                  style={styles.image}
                  resizeMode={FastImage.resizeMode.contain}
                />
              </TouchableOpacity>
            </Animated.View>
          </View>
        </GestureDetector>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  closeButton: {
    position: 'absolute',
    top: 65,
    right: 20,
    zIndex: 1000,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 8,
  },
  imageContainer: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  animatedImageContainer: {
    width: screenWidth,
    height: screenHeight * 0.8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageWrapper: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  zoomIndicator: {
    position: 'absolute',
    top: 100,
    left: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    zIndex: 1000,
  },
  zoomText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  scrollHint: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 10,
    textAlign: 'center',
    marginTop: 2,
  },
});

export default ZoomableImageModal;
