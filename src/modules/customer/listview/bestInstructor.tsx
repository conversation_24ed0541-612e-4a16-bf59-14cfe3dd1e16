/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState} from 'react';
import {View, Text, FlatList} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {AppButton} from 'wini-mobile-components';
import DefaultBanner from '../../Default/card/infor';
import {SkeletonPlaceCard} from '../../Default/card/defaultImage';
import {CustomerDA} from '../da';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
}
export default function BestInstructor(props: Props) {
  const [data, setData] = useState<Array<any>>([]);
  const [isLoading, setLoading] = useState(false);
  const navigation = useNavigation<any>();
  const customerDA = new CustomerDA();
  useEffect(() => {
    getData();
  }, []);
  const getData = async () => {
    const result = await customerDA.getBestInstructor();
    if (result) {
      result.data.map((item: any) => {
        item.Img = item.AvatarUrl;
        return item;
      });
      setData(result.data);
      setLoading(false);
    }
  };
  return (
    <View
      style={{
        height:
          data.length == 0 && !isLoading
            ? 0
            : props.horizontal
            ? 400
            : undefined,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_MainReverse,
        marginBottom: data.length == 0 && !isLoading ? 0 : 30,
      }}>
      {data.length == 0 && !isLoading ? null : (
        <View style={{height: '100%'}}>
          {props.titleList ? (
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'space-between',
                paddingHorizontal: 16,
                paddingBottom: 16,
                paddingTop: 24,
                flexDirection: 'row',
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_MainReverse,
              }}>
              <Text
                style={{
                  ...TypoSkin.heading5,
                  color: ColorThemes.light.Neutral_Background_Color_Absolute,
                }}>
                {props.titleList}
              </Text>
              {props.isSeeMore ? (
                <AppButton
                  title={'Xem thêm'}
                  containerStyle={{
                    justifyContent: 'flex-start',
                    alignSelf: 'baseline',
                  }}
                  backgroundColor={'transparent'}
                  textStyle={{...TypoSkin.buttonText3, color: '#FFFFFF'}}
                  borderColor="transparent"
                  suffixIconSize={16}
                  suffixIcon={'outline/arrows/circle-arrow-right'}
                  onPress={() => {
                    navigation.navigate(RootScreen.Instructors);
                  }}
                  textColor={ColorThemes.light.Info_Color_Main}
                />
              ) : null}
            </View>
          ) : null}
          <FlatList
            data={data}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            // refreshControl={
            //   <RefreshControl
            //     refreshing={isLoading}
            //     onRefresh={() => {
            //       onRefresh();
            //     }}
            //   />
            // }
            contentContainerStyle={{
              gap: 16,
              backgroundColor:
                ColorThemes.light.Neutral_Background_Color_MainReverse,
            }}
            renderItem={({item, index}) => {
              return (
                <DefaultBanner
                  onPressDetail={() => {
                    navigation.push(RootScreen.ProfileCommunity, {
                      Id: item.Id,
                      forEschool: true,
                    });
                  }}
                  key={index}
                  flexDirection="default"
                  containerStyle={{width: 180, height: 274}}
                  data={item}
                  titleStyle={{fontSize: 14}}
                />
              );
            }}
            style={{width: '100%', height: '100%', paddingHorizontal: 16}}
            keyExtractor={item => item.Id?.toString()}
            horizontal={props.horizontal}
            ListEmptyComponent={() => {
              if (isLoading) {
                return <SkeletonPlaceCard />;
              }
              return <Text style={{color: '#000000'}}>Không có dữ liệu</Text>;
            }}
          />{' '}
        </View>
      )}
    </View>
  );
}
