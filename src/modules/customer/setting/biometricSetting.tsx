import {Text, View} from 'react-native';
import {navigateBack} from '../../../router/router';
import TitleWithBackAction from '../../../Screen/Layout/titleWithBackAction';
import {ListTile, WSwitch} from 'wini-mobile-components';
import {useState, useEffect} from 'react';
import ReactNativeBiometrics from 'react-native-biometrics';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {FaceID, TouchID} from '../../../features/local-authen/local-authen';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ScreenHeader from '../../../Screen/Layout/header';
import {SafeAreaView} from 'react-native-safe-area-context';
import IOSSwitch from '../../../components/IOSSwitch';

export default function BiometricSetting() {
  const customer = useSelectorCustomerState().data;
  const [biometric, setBiometric] = useState(false);
  const [biometricType, setBiometricType] = useState<any>();
  const [avaiBiometric, setAvaiBiometric] = useState(false);
  const rnBiometrics = new ReactNativeBiometrics({
    allowDeviceCredentials: true,
  });
  useEffect(() => {
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        setAvaiBiometric(true);
        getDataToAsyncStorage('biometryType').then(result => {
          setBiometricType(result);
        });
        getDataToAsyncStorage('Biometrics').then(result => {
          setBiometric(result == 'true' ? true : false);
        });
      } else {
        setAvaiBiometric(false);
        setBiometric(false);
      }
    });
  }, []);
  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <ScreenHeader
        title="Cài đặt sinh trắc học"
        onBack={() => navigateBack()}
      />
      <View style={{paddingHorizontal: 16}}>
        <Text
          style={{
            ...TypoSkin.body3,
            color: ColorThemes.light.Neutral_Text_Color_Subtitle,
          }}>
          Sinh trắc học là một tính năng bảo mật cho phép bạn đăng nhập bằng vân
          tay hoặc khuôn mặt.
        </Text>
      </View>
      <ListTile
        leading={
          <View>
            {(() => {
              if (biometricType == 'TouchID') {
                return (
                  <TouchID
                    size={20}
                    color={
                      avaiBiometric
                        ? ColorThemes.light.Neutral_Text_Color_Subtitle
                        : ColorThemes.light.Neutral_Text_Color_Disable
                    }
                  />
                );
              } else {
                return (
                  <FaceID
                    size={20}
                    color={
                      avaiBiometric
                        ? ColorThemes.light.Neutral_Text_Color_Subtitle
                        : ColorThemes.light.Neutral_Text_Color_Disable
                    }
                  />
                );
              }
            })()}
          </View>
        }
        title="Sử dụng sinh trắc học"
        titleStyle={[
          TypoSkin.heading8,
          {
            color: avaiBiometric
              ? ColorThemes.light.Neutral_Text_Color_Title
              : ColorThemes.light.Neutral_Text_Color_Disable,
          },
        ]}
        trailing={
          <IOSSwitch
            value={biometric}
            disabled={!avaiBiometric}
            onColor={ColorThemes.light.Primary_Color_Main}
            onValueChange={vl => {
              rnBiometrics
                .simplePrompt({promptMessage: 'Xác nhận sinh trắc học'})
                .then(resultObject => {
                  const {success} = resultObject;
                  if (success) {
                    if (vl) {
                      setTimeout(() => {
                        saveDataToAsyncStorage('Biometrics', 'true');
                        setBiometric(true);
                      }, 100);
                      return;
                    } else {
                      setTimeout(() => {
                        saveDataToAsyncStorage('Biometrics', 'false');
                        setBiometric(false);
                      }, 100);
                    }
                  } else {
                    setTimeout(() => {
                      saveDataToAsyncStorage(
                        'Biometrics',
                        biometric ? 'true' : 'false',
                      );
                      setBiometric(biometric);
                    }, 100);
                    console.log('user cancelled biometric prompt', biometric);
                  }
                })
                .catch(() => {
                  setTimeout(() => {
                    saveDataToAsyncStorage('Biometrics', 'false');
                    setBiometric(false);
                  }, 100);
                  console.log('biometrics failed', biometric);
                });
            }}
          />
        }
      />
    </SafeAreaView>
  );
}
