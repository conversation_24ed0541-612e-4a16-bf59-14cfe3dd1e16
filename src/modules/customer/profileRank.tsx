import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {navigateBack} from '../../router/router';
import {Ultis} from '../../utils/Utils';

import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import ConfigAPI from '../../Config/ConfigAPI';

const {width} = Dimensions.get('window');

const ProfileRankScreen = () => {
  const customer = useSelectorCustomerState().data;
  const currentRank = customer?.Rank || 0;
  const ranksData = customer?.RanksData || [];
  // Tính toán progress đến hạng tiếp theo dựa trên customer?.Rank
  const getCustomerRankProgress = () => {
    if (ranksData.length === 0) {
      return {
        progress: 0,
        nextPoints: 0,
        currentRankInfo: null,
        nextRankInfo: null,
      };
    }

    // Sắp xếp ranks theo điểm số tăng dần
    const sortedRanks = [...ranksData].sort(
      (a, b) => parseFloat(a.Score) - parseFloat(b.Score),
    );

    // Tìm hạng hiện tại và hạng tiếp theo
    let currentRankInfo = null;
    let nextRankInfo = null;

    for (let i = 0; i < sortedRanks.length; i++) {
      const rankScore = parseFloat(sortedRanks[i].Score);
      if (currentRank >= rankScore) {
        currentRankInfo = sortedRanks[i];
        nextRankInfo = sortedRanks[i + 1] || null;
      } else {
        if (!currentRankInfo) {
          nextRankInfo = sortedRanks[i];
        }
        break;
      }
    }

    // Tính toán progress
    let progress = 0;
    if (nextRankInfo) {
      const currentRankPoints = currentRankInfo
        ? parseFloat(currentRankInfo.Score)
        : 0;
      const nextRankPoints = parseFloat(nextRankInfo.Score);
      const totalNeeded = nextRankPoints - currentRankPoints;
      const currentProgress = currentRank - currentRankPoints;
      progress = totalNeeded > 0 ? (currentProgress / totalNeeded) * 100 : 0;
    } else {
      // Đã đạt hạng cao nhất
      progress = 100;
    }

    return {
      progress: Math.max(0, Math.min(100, progress)),
      nextPoints: nextRankInfo ? parseFloat(nextRankInfo.Score) : 0,
      currentRankInfo,
      nextRankInfo,
    };
  };

  const {progress, nextPoints, currentRankInfo, nextRankInfo} =
    getCustomerRankProgress();
  return (
    <LinearGradient colors={['#FF9966', '#FF5E62']} style={styles.container}>
      <SafeAreaView edges={['top']} style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={navigateBack} style={styles.backButton}>
            <Winicon
              src="outline/arrows/left-arrow"
              size={20}
              color={ColorThemes.light.Neutral_Background_Color_Absolute}
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Điểm của bạn</Text>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}>
          {/* Status Card */}
          <View style={styles.statusCard}>
            {customer?.RankInfor ? (
              <Winicon
                src={
                  ConfigAPI.url.replace('/api/', '') + customer?.RankInfor?.Icon
                }
                size={100}
              />
            ) : null}
            <Text style={styles.statusTitle}>
              {customer?.RankInfor?.Name ? '' : 'Chưa được xếp hạng'}
            </Text>
          </View>
          {/* Progress Section */}
          <View style={styles.progressSection}>
            {/* Current Rank Progress */}
            <View style={styles.currentRankProgress}>
              <View style={styles.progressHeader}>
                <Text style={styles.progressTitle}>Tiến độ hạng hiện tại</Text>
                <Text style={styles.progressPercentage}>
                  {Math.round(progress)}%
                </Text>
              </View>

              {/* Progress Bar */}
              <View style={styles.progressBarContainer}>
                <View style={styles.progressBarBackground}>
                  <View
                    style={[styles.progressBarFill, {width: `${progress}%`}]}
                  />
                </View>
              </View>

              {/* Progress Info */}
              <View style={styles.progressInfo}>
                <View style={styles.progressInfoItem}>
                  <Text style={styles.progressInfoLabel}>Hạng hiện tại</Text>
                  <Text style={styles.progressInfoValue}>
                    {currentRankInfo?.Name || 'Chưa có hạng'}
                  </Text>
                </View>
                {nextRankInfo && (
                  <View style={styles.progressInfoItem}>
                    <Text style={styles.progressInfoLabel}>Hạng tiếp theo</Text>
                    <Text style={styles.progressInfoValue}>
                      {nextRankInfo.Name}
                    </Text>
                  </View>
                )}
              </View>

              {/* Current Points Display */}
              <View style={styles.currentPointsDisplay}>
                <Text style={styles.currentPointsLabel}>Điểm hiện tại</Text>
                <Text style={styles.currentPointsValue}>
                  {Ultis.money(customer?.Rank || 0)} điểm
                </Text>
                {nextRankInfo && (
                  <Text style={styles.pointsToNext}>
                    Cần thêm {Ultis.money(nextPoints - (customer?.Rank || 0))}{' '}
                    điểm để lên hạng
                  </Text>
                )}
              </View>
            </View>

            {/* Progress Indicators */}
            {/* <View style={styles.progressIndicators}>
              {customer?.RanksData?.map((item: any) => (
                <View key={item.Id} style={styles.progressItem}>
                  <View style={styles.progressIcon}>
                    <Winicon
                      src={ConfigAPI.url.replace('/api/', '') + item.Icon}
                      size={22}
                    />
                  </View>
                  <Text style={styles.progressLabel}>{item.Name}</Text>
                </View>
              ))}
            </View> */}
          </View>

          {/* Ranking Tiers Section */}
          <View style={styles.rankingSection}>
            <Text style={styles.sectionTitle}>Chỉ tiêu hạng</Text>
            <View style={styles.rankingGrid}>
              {customer?.RanksData?.map((tier: any) => (
                <View key={tier.Id} style={styles.rankTierCard}>
                  <View style={[styles.rankTierIcon]}>
                    <Winicon
                      src={ConfigAPI.url.replace('/api/', '') + tier.Icon}
                      size={30}
                    />
                  </View>
                  <Text style={styles.rankTierLabel}>Hạng {tier.Name}</Text>
                  <Text style={styles.rankTierPoints}>
                    {Ultis.money(parseFloat(tier.Score))} điểm
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 16,
    position: 'relative',
  },
  backButton: {
    position: 'absolute',
    left: 16,
    top: 12,
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  menuButton: {
    position: 'absolute',
    right: 16,
    top: 12,
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  scrollView: {
    flex: 1,
  },
  trophySection: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 20,
  },
  trophyContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
    width: width - 32,
  },
  decorativeElements: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },

  trophy: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 50,
    padding: 20,
  },
  statusCard: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    borderRadius: 16,
    marginHorizontal: 16,
    padding: 20,
    marginTop: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    gap: 16,
  },
  statusTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
  },
  statusSubtitle: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    textAlign: 'center',
  },
  progressSection: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    margin: 16,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  currentRankProgress: {
    marginBottom: 0,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  progressTitle: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: 'bold',
  },
  progressPercentage: {
    ...TypoSkin.heading7,
    color: ColorThemes.light.Primary_Color_Main,
    fontWeight: 'bold',
  },
  progressBarContainer: {
    marginBottom: 16,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    borderRadius: 4,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  progressInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  progressInfoLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginBottom: 4,
  },
  progressInfoValue: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  currentPointsDisplay: {
    backgroundColor: ColorThemes.light.Secondary_3_Color_Background,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  currentPointsLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginBottom: 4,
  },
  currentPointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  pointsToNext: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Primary_Color_Main,
    textAlign: 'center',
  },
  currentPointsCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.Secondary_3_Color_Background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  pointsIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  pointsInfo: {
    flex: 1,
  },
  pointsLabel: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 2,
  },
  pointsSubLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  pointsValue: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: 'bold',
  },
  progressIndicators: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressItem: {
    alignItems: 'center',
    flex: 1,
  },
  progressIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    textAlign: 'center',
  },
  rankingSection: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    margin: 16,
    marginTop: 0,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 16,
  },
  rankingGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  rankTierCard: {
    width: '48%',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  rankTierIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  rankTierLabel: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 4,
    textAlign: 'center',
  },
  rankTierPoints: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  achievementsSection: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    margin: 16,
    marginTop: 0,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 32,
  },
  achievementCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  achievementIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementName: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    marginBottom: 2,
  },
  achievementDescription: {
    ...TypoSkin.body3,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
  },
  achievementProgress: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  achievementProgressText: {
    ...TypoSkin.heading8,
    color: ColorThemes.light.Neutral_Text_Color_Title,
    fontWeight: 'bold',
  },
});

export default ProfileRankScreen;
