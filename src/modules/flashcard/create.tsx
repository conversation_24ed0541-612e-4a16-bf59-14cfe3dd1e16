/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import {
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import {
  AppButton,
  ComponentStatus,
  FBottomSheet,
  hideBottomSheet,
  ListTile,
  showBottomSheet,
  showSnackbar,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {useForm} from 'react-hook-form';
import {useEffect, useRef, useState} from 'react';
import {Text} from 'react-native';
import ImageCropPicker from 'react-native-image-crop-picker';

import {randomGID} from '../../utils/Utils';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {
  CheckboxForm,
  DropdownForm,
  TextFieldForm,
} from '../Default/form/component-form';

import ScreenHeader from '../../Screen/Layout/header';
import {navigateBack} from '../../router/router';
import {flashCardDA} from './da';
import {BaseDA} from '../../base/BaseDA';
import ConfigAPI from '../../Config/ConfigAPI';
import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';

export default function CreateFlashCard() {
  const [list, setList] = useState<Array<any>>([]);
  const customer = useSelectorCustomerState().data;
  const FlashCardDA = new flashCardDA();
  const bottomSheetRef = useRef<any>(null);
  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {
      Id: randomGID(),
      CustomerId: customer?.Id,
      DateCreated: new Date().getTime(),
    },
  });
  const route = useRoute<any>();

  useEffect(() => {
    // edit
    if (route.params?.id) {
      FlashCardDA.getbyId(route.params.id).then((res: any) => {
        if (res) {
          const tmp = res.data;
          methods.setValue('Id', route.params.id);
          methods.setValue('Name', tmp.Name);
          methods.setValue('Description', tmp.Description);
          methods.setValue('TopicId', tmp.TopicId);
        }
      });

      FlashCardDA.getListDetailbyId(route.params.id).then((res: any) => {
        if (res) {
          const tmp = res.data;
          setList(tmp);
        }
      });
    }
  }, [route.params]);

  const submitForm = async (value: any) => {
    let lstDetail = [];
    if (list?.length > 0) {
      lstDetail = await Promise.all(
        list.map(async (item: any) => {
          let imgId = null;
          let audioId = null;
          // Xử lý image
          if (item.image) {
            if (item.image.isFromServer) {
              // Nếu là ảnh từ server và không thay đổi, giữ nguyên ID
              imgId = item.image.serverId;
            } else {
              // Upload ảnh mới
              const imageFile = await BaseDA.uploadFiles([
                {
                  name: item.image.filename ?? 'new file img',
                  type: item.image.mime,
                  uri: item.image.path,
                },
              ]);
              imgId = imageFile[0]?.Id;
            }
          } else if (item.Img) {
            // Trường hợp edit item existing mà không có item.image (không thay đổi ảnh)
            // Giữ nguyên ID ảnh cũ từ database
            imgId = item.Img;
          }

          // Xử lý audio
          if (item.audio) {
            if (item.audio.isFromServer) {
              // Nếu là audio từ server và không thay đổi, giữ nguyên ID
              audioId = item.audio.serverId;
            } else {
              // Upload audio mới
              const audioFile = await BaseDA.uploadFiles([
                {
                  name: item.audio.filename ?? 'new file audio.mp3',
                  type: item.audio.mime,
                  uri: item.audio.path,
                },
              ]);
              audioId = audioFile[0]?.Id;
            }
          } else if (item.Audio) {
            // Trường hợp edit item existing mà không có item.audio (không thay đổi audio)
            // Giữ nguyên ID audio cũ từ database
            audioId = item.Audio;
          }
          return {
            ...item,
            Img: imgId,
            Audio: audioId,
            FlashCardId: value.Id,
            image: undefined,
            audio: undefined,
          };
        }),
      );
    }
    if (route.params?.id) {
      // edit
      const result = await FlashCardDA.edit(value, lstDetail);
      if (result) {
        showSnackbar({
          message: 'Sửa Flashcard thành công',
          status: ComponentStatus.SUCCSESS,
        });
        navigateBack();
      } else {
        showSnackbar({message: 'Có lỗi xảy ra', status: ComponentStatus.ERROR});
      }
    } else {
      const result = await FlashCardDA.add(value);
      if (lstDetail.length > 0) {
        await FlashCardDA.addDetail(lstDetail);
      }
      if (result) {
        showSnackbar({
          message: 'Tạo Flashcard mới thành công',
          status: ComponentStatus.SUCCSESS,
        });
        navigateBack();
      } else {
        showSnackbar({message: 'Có lỗi xảy ra', status: ComponentStatus.ERROR});
      }
    }
  };

  const errorForm = (value: any) => {
    console.log('error' + value);
  };
  // lấy danh sách chủ đề
  const [listTopic, setListTopic] = useState<Array<any>>([]);
  useEffect(() => {
    FlashCardDA.getTopic().then((res: any) => {
      if (res) {
        setListTopic(res.data);
      }
    });
  }, []);

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <ScreenHeader
        title="Tạo mới FlashCard"
        onBack={() => {
          navigateBack();
        }}
        action={
          <AppButton
            backgroundColor={'transparent'}
            borderColor="transparent"
            title={'Lưu'}
            onPress={methods.handleSubmit(submitForm, errorForm)}
            textColor={ColorThemes.light.Primary_Color_Main}
            containerStyle={{paddingHorizontal: 16, alignSelf: 'baseline'}}
          />
        }
      />
      <KeyboardAvoidingView style={{flex: 1, paddingHorizontal: 16}}>
        <ScrollView nestedScrollEnabled style={{flex: 1}}>
          {/* forms */}
          <View style={{gap: 16}}>
            <TextFieldForm
              label="Tiêu đề"
              required
              textFieldStyle={{padding: 16}}
              style={{width: '100%'}}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              name="Name"
            />
            <DropdownForm
              control={methods.control}
              name="TopicId"
              options={listTopic.map((item: any) => ({
                id: item.Id,
                name: item.Name,
              }))}
              label="Chủ đề"
              errors={methods.formState.errors}
            />
            <TextFieldForm
              required
              control={methods.control}
              name="Description"
              errors={methods.formState.errors}
              label="Mô tả"
              style={{backgroundColor: ColorThemes.light.transparent}}
              textFieldStyle={{
                height: 100,
                paddingHorizontal: 16,
                paddingTop: 16,
                paddingBottom: 16,
                justifyContent: 'flex-start',
                backgroundColor: ColorThemes.light.transparent,
              }}
              textStyle={{textAlignVertical: 'top'}}
              numberOfLines={10}
              multiline={true}
              onBlur={value => {
                if (value == undefined || value.length == 0) {
                  methods.setError('TextArea', {
                    message: 'Vui lòng nhập thông tin mô tả',
                  });
                } else {
                  methods.clearErrors('TextArea');
                }
              }}
              register={methods.register}
            />
          </View>
          {/* cards */}
          <FlatList
            nestedScrollEnabled
            data={list}
            scrollEnabled={false}
            style={{paddingVertical: 16}}
            ListHeaderComponent={() => {
              // create
              return (
                <View
                  style={{
                    borderRadius: 12,
                    backgroundColor:
                      ColorThemes.light.Neutral_Border_Color_Main,
                    width: '100%',
                    padding: 16,
                    gap: 16,
                  }}>
                  <ListTile
                    style={{padding: 0, backgroundColor: 'transparent'}}
                    onPress={() => {
                      showBottomSheet({
                        ref: bottomSheetRef,
                        enableDismiss: true,
                        title: 'Thêm từ mới',
                        children: (
                          <FormCard
                            item={null}
                            setList={setList}
                            list={list}
                            bottomSheetRef={bottomSheetRef}
                          />
                        ),
                      });
                    }}
                    title="Thẻ FlashCard"
                    trailing={
                      <Winicon src="outline/layout/circle-plus" size={20} />
                    }
                  />
                </View>
              );
            }}
            contentContainerStyle={{
              gap: 16,
            }}
            renderItem={({item, index}) => {
              return (
                <ListTile
                  key={item.Id}
                  title={item.Name}
                  style={{
                    borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                    borderWidth: 1,
                  }}
                  subtitle={item.Transcription}
                  bottom={
                    item.Note ? (
                      <View style={{flex: 1, width: '100%'}}>
                        <Text style={{...TypoSkin.body3, fontWeight: '700'}}>
                          Ghi chú:
                        </Text>
                        <Text style={{...TypoSkin.body3}}>{item.Note}</Text>
                      </View>
                    ) : null
                  }
                  trailing={
                    <View
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                        gap: 16,
                      }}>
                      <TouchableOpacity
                        style={{
                          padding: 8,
                          borderRadius: 100,
                          borderColor:
                            ColorThemes.light.Neutral_Border_Color_Main,
                          borderWidth: 1,
                        }}
                        onPress={() => {
                          setList(list.filter(e => e.Id !== item.Id));
                          showSnackbar({
                            message: 'Xóa thành công',
                            status: ComponentStatus.SUCCSESS,
                          });
                        }}>
                        <Winicon
                          src="outline/user interface/trash-can"
                          color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                          size={16}
                        />
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={{
                          padding: 8,
                          borderRadius: 100,
                          borderColor:
                            ColorThemes.light.Neutral_Border_Color_Main,
                          borderWidth: 1,
                        }}
                        onPress={() => {
                          showBottomSheet({
                            ref: bottomSheetRef,
                            enableDismiss: true,
                            title: 'Sửa Từ',
                            children: (
                              <FormCard
                                item={item}
                                setList={setList}
                                list={list}
                                bottomSheetRef={bottomSheetRef}
                              />
                            ),
                          });
                        }}>
                        <Winicon
                          src="outline/user interface/edit"
                          color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                          size={16}
                        />
                      </TouchableOpacity>
                    </View>
                  }
                />
              );
            }}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const FormCard = ({
  item,
  list,
  setList,
  bottomSheetRef,
}: {
  item: any;
  list: any;
  setList: any;
  bottomSheetRef: any;
}) => {
  const methodsCard = useForm<any>({
    shouldFocusError: false,
  });
  const layout = useWindowDimensions();
  const [img, setImg] = useState<any>();
  const [audio, setAudio] = useState<any>();

  const pickerImg = async () => {
    const image = await ImageCropPicker.openPicker({
      multiple: false,
      cropping: false,
      maxFiles: 5,
    });
    if (image) {
      setImg(image);
    }
  };

  const pickerAudio = async () => {
    try {
      // Sử dụng ImageCropPicker để chọn file (có thể chọn video/audio)
      const result = await ImageCropPicker.openPicker({
        multiple: false,
        mediaType: 'any', // Cho phép chọn mọi loại file
        cropping: false,
      });

      // Kiểm tra nếu là file audio (mp3, wav, etc.)
      if (result.mime && result.mime.includes('audio')) {
        setAudio(result);
      } else {
        // Nếu không phải audio, tạo object giả lập cho demo
        // Trong thực tế, bạn có thể sử dụng react-native-document-picker
        setAudio({
          path: result.path,
          filename: result.filename || 'audio.mp3',
          mime: 'audio/mp3',
          size: result.size,
        });
      }
    } catch (error) {
      console.log('User cancelled audio picker');
    }
  };
  const cardFormSubmit = (value: any) => {
    value.image = img;
    value.audio = audio;

    if (list.find((e: any) => e.Id === value.Id)) {
      // Edit existing item
      const index = list.findIndex((e: any) => e.Id === value.Id);
      const existingItem = list[index];

      // Preserve existing Img and Audio IDs if no new image/audio is provided
      if (!img && existingItem.Img) {
        value.Img = existingItem.Img;
      }
      if (!audio && existingItem.Audio) {
        value.Audio = existingItem.Audio;
      }

      list[index] = value;
      setList([...list]);
    } else {
      // Create new item
      setList([
        ...list,
        {...value, Id: randomGID(), DateCreated: new Date().getTime()},
      ]);
    }
    methodsCard.reset();
    setImg(undefined);
    setAudio(undefined);
    hideBottomSheet(bottomSheetRef);
  };
  const errorCardForm = (value: any) => {
    console.log('error' + value);
  };

  useEffect(() => {
    if (item) {
      // Load local image/audio (khi edit từ form chưa save)
      if (item.image) {
        setImg(item.image);
      }
      if (item.audio) {
        setAudio(item.audio);
      }

      // Load image/audio từ server (khi edit từ database)
      if (item.Img && !item.image) {
        // Tạo object image từ server data
        setImg({
          path: `${ConfigAPI.urlImg}${item.Img}`, // URL đầy đủ từ server
          filename: 'image.jpg',
          mime: 'image/jpeg',
          isFromServer: true, // Flag để biết đây là ảnh từ server
          serverId: item.Img, // Lưu ID để xử lý khi save
        });
      }

      if (item.Audio && !item.audio) {
        // Tạo object audio từ server data
        setAudio({
          path: `${ConfigAPI.urlImg}${item.Audio}`, // URL đầy đủ từ server
          filename: 'audio.mp3',
          mime: 'audio/mp3',
          isFromServer: true, // Flag để biết đây là audio từ server
          serverId: item.Audio, // Lưu ID để xử lý khi save
        });
      }

      // Set form values
      Object.keys(item).map((key: any) => {
        methodsCard.setValue(key, item[key]);
      });
    }
  }, [item, methodsCard]);
  return (
    <View
      style={{
        height: Math.min(layout.height * 0.9, 800), // Chiều cao tối ưu cho BottomSheet
        width: '100%',
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{flex: 1}}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
        <ScrollView
          style={{flex: 1}}
          contentContainerStyle={{
            paddingHorizontal: 16,
            paddingBottom: 120, // Tăng padding bottom để đảm bảo không bị che
          }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          nestedScrollEnabled={true}>
          <View style={{gap: 16}}>
            <Text style={{...TypoSkin.body3}}>Ảnh minh họa</Text>
            {img ? (
              <View
                style={{
                  flex: 1,
                  width: 85,
                  position: 'relative',
                }}>
                <TouchableOpacity
                  style={{
                    padding: 4,
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    zIndex: 11,
                  }}
                  onPress={() => setImg(undefined)}>
                  <Winicon
                    src="fill/user interface/c-remove"
                    size={20}
                    color={ColorThemes.light.Error_Color_Main}
                  />
                </TouchableOpacity>
                <SkeletonImage
                  source={{
                    uri: img.path, // URL đã được xử lý đúng trong useEffect
                  }}
                  style={{
                    width: 85,
                    height: 85,
                    borderRadius: 8,
                  }}
                />
              </View>
            ) : (
              <TouchableOpacity
                onPress={pickerImg}
                style={{
                  width: 85,
                  height: 85,
                  borderRadius: 8,
                  borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                  borderWidth: 1,
                  borderStyle: 'dotted',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text>Thêm ảnh</Text>
              </TouchableOpacity>
            )}

            {/* Audio Section */}
            <Text style={{...TypoSkin.body3}}>File âm thanh (MP3)</Text>
            {audio ? (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  padding: 12,
                  borderRadius: 8,
                  borderColor: ColorThemes.light.Neutral_Border_Color_Main,
                  borderWidth: 1,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                }}>
                <Winicon
                  src="outline/multimedia/music-note"
                  size={20}
                  color={ColorThemes.light.Primary_Color_Main}
                  style={{marginRight: 8}}
                />
                <View style={{flex: 1}}>
                  <Text style={{...TypoSkin.body3, fontWeight: '600'}}>
                    {audio.filename ||
                      (audio.isFromServer ? 'Audio từ server' : 'audio.mp3')}
                  </Text>
                  <Text
                    style={{
                      ...TypoSkin.subtitle4,
                      color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    }}>
                    {audio.size
                      ? `${Math.round(audio.size / 1024)} KB`
                      : audio.isFromServer
                      ? 'File âm thanh'
                      : 'Audio file'}
                  </Text>
                </View>
                <TouchableOpacity
                  style={{
                    padding: 4,
                  }}
                  onPress={() => setAudio(undefined)}>
                  <Winicon
                    src="fill/user interface/c-remove"
                    size={20}
                    color={ColorThemes.light.Error_Color_Main}
                  />
                </TouchableOpacity>
              </View>
            ) : (
              <TouchableOpacity
                onPress={pickerAudio}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  padding: 12,
                  borderRadius: 8,
                  borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
                  backgroundColor:
                    ColorThemes.light.Neutral_Background_Color_Absolute,
                  borderWidth: 1,
                  borderStyle: 'dotted',
                }}>
                <Winicon
                  src="outline/multimedia/music-note"
                  size={20}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                  style={{marginRight: 8}}
                />
                <Text style={{...TypoSkin.body3}}>Thêm file âm thanh</Text>
              </TouchableOpacity>
            )}
            <TextFieldForm
              label="Từ gốc"
              required
              textFieldStyle={{
                padding: 16,
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}
              style={{width: '100%'}}
              register={methodsCard.register}
              control={methodsCard.control}
              errors={methodsCard.formState.errors}
              name="Name"
            />
            <TextFieldForm
              label="Dịch nghĩa"
              required
              textStyle={{textAlignVertical: 'top'}}
              numberOfLines={10}
              multiline={true}
              textFieldStyle={{
                paddingHorizontal: 16,
                paddingTop: 16,
                // paddingBottom: 16,
                height: 100,
                justifyContent: 'flex-start',
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}
              style={{width: '100%'}}
              register={methodsCard.register}
              control={methodsCard.control}
              errors={methodsCard.formState.errors}
              name="Define"
            />
            <TextFieldForm
              label="Phiên âm"
              textFieldStyle={{
                padding: 16,
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}
              style={{width: '100%'}}
              register={methodsCard.register}
              control={methodsCard.control}
              errors={methodsCard.formState.errors}
              name="Transcription"
            />
            <TextFieldForm
              label="Ví dụ"
              textFieldStyle={{
                padding: 16,
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}
              style={{width: '100%'}}
              register={methodsCard.register}
              control={methodsCard.control}
              errors={methodsCard.formState.errors}
              name="Example"
            />
            <TextFieldForm
              label="Phiên âm thuần Nhật"
              textFieldStyle={{
                padding: 16,
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}
              style={{width: '100%'}}
              register={methodsCard.register}
              control={methodsCard.control}
              errors={methodsCard.formState.errors}
              name="Hiragana"
            />
            <TextFieldForm
              label="Phiên âm Hán Nhật"
              textFieldStyle={{
                padding: 16,
                backgroundColor:
                  ColorThemes.light.Neutral_Background_Color_Absolute,
              }}
              style={{width: '100%'}}
              register={methodsCard.register}
              control={methodsCard.control}
              errors={methodsCard.formState.errors}
              name="Katakana"
            />

            {/* Submit Button - Đặt trong ScrollView để tránh bị che */}
            <AppButton
              title={'Thêm'}
              backgroundColor={ColorThemes.light.Primary_Color_Main}
              prefixIcon={'outline/layout/plus'}
              prefixIconSize={16}
              borderColor="transparent"
              containerStyle={{
                height: 45,
                borderRadius: 8,
                marginTop: 24,
                marginBottom: 16,
                paddingHorizontal: 12,
              }}
              onPress={methodsCard.handleSubmit(cardFormSubmit, errorCardForm)}
              textColor={ColorThemes.light.Neutral_Background_Color_Absolute}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};
