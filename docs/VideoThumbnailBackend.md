# Video Thumbnail Generation Backend

## Overview
Để generate thumbnail cho MP4 files từ server, b<PERSON><PERSON> c<PERSON>n implement một backend endpoint sử dụng FFmpeg.

## Backend Implementation Options

### Option 1: Node.js + FFmpeg

```javascript
// backend/routes/video.js
const express = require('express');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const router = express.Router();

router.get('/thumbnail', async (req, res) => {
  try {
    const { url, time = 1, width = 320, height = 240 } = req.query;
    
    if (!url) {
      return res.status(400).json({ error: 'Video URL is required' });
    }

    // Generate unique filename
    const filename = `thumb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
    const outputPath = path.join(__dirname, '../temp', filename);

    // Generate thumbnail using FFmpeg
    ffmpeg(url)
      .seekInput(time)
      .frames(1)
      .size(`${width}x${height}`)
      .output(outputPath)
      .on('end', () => {
        // Send file and cleanup
        res.sendFile(outputPath, (err) => {
          if (!err) {
            fs.unlinkSync(outputPath); // Delete temp file
          }
        });
      })
      .on('error', (err) => {
        console.error('FFmpeg error:', err);
        res.status(500).json({ error: 'Failed to generate thumbnail' });
      })
      .run();

  } catch (error) {
    console.error('Thumbnail generation error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
```

### Option 2: Python + FFmpeg

```python
# backend/thumbnail_generator.py
import subprocess
import tempfile
import os
from flask import Flask, request, send_file, jsonify

app = Flask(__name__)

@app.route('/video/thumbnail')
def generate_thumbnail():
    try:
        video_url = request.args.get('url')
        time = request.args.get('time', '1')
        width = request.args.get('w', '320')
        height = request.args.get('h', '240')
        
        if not video_url:
            return jsonify({'error': 'Video URL is required'}), 400
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        # FFmpeg command
        cmd = [
            'ffmpeg',
            '-i', video_url,
            '-ss', str(time),
            '-vframes', '1',
            '-s', f'{width}x{height}',
            '-y',  # Overwrite output file
            output_path
        ]
        
        # Execute FFmpeg
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            return jsonify({'error': 'Failed to generate thumbnail'}), 500
        
        # Send file and cleanup
        def remove_file(response):
            try:
                os.unlink(output_path)
            except Exception:
                pass
            return response
        
        return send_file(output_path, mimetype='image/jpeg', as_attachment=False)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
```

### Option 3: AWS Lambda + FFmpeg

```javascript
// lambda/thumbnail-generator.js
const AWS = require('aws-sdk');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const fs = require('fs');
const path = require('path');

ffmpeg.setFfmpegPath(ffmpegPath);

exports.handler = async (event) => {
    try {
        const { videoUrl, time = 1, width = 320, height = 240 } = event.queryStringParameters;
        
        if (!videoUrl) {
            return {
                statusCode: 400,
                body: JSON.stringify({ error: 'Video URL is required' })
            };
        }

        const outputPath = `/tmp/thumbnail_${Date.now()}.jpg`;

        return new Promise((resolve, reject) => {
            ffmpeg(videoUrl)
                .seekInput(time)
                .frames(1)
                .size(`${width}x${height}`)
                .output(outputPath)
                .on('end', () => {
                    const imageBuffer = fs.readFileSync(outputPath);
                    const base64Image = imageBuffer.toString('base64');
                    
                    resolve({
                        statusCode: 200,
                        headers: {
                            'Content-Type': 'image/jpeg',
                        },
                        body: base64Image,
                        isBase64Encoded: true
                    });
                })
                .on('error', (err) => {
                    reject({
                        statusCode: 500,
                        body: JSON.stringify({ error: 'Failed to generate thumbnail' })
                    });
                })
                .run();
        });

    } catch (error) {
        return {
            statusCode: 500,
            body: JSON.stringify({ error: error.message })
        };
    }
};
```

## Frontend Integration

Để sử dụng backend endpoint, uncomment và update URL trong `VideoThumbnailGenerator.ts`:

```typescript
// In generateThumbnailFromBackend method
return `${ConfigAPI.url}video/thumbnail?url=${encodeURIComponent(videoUrl)}&w=${width}&h=${height}&t=${time}&q=${quality}`;
```

## Cloudinary Integration (Recommended)

Cloudinary cung cấp video thumbnail generation built-in:

```typescript
// In generateThumbnailFromBackend method
const cloudName = 'your-cloud-name';
return `https://res.cloudinary.com/${cloudName}/video/upload/so_${time},w_${width},h_${height},c_fill,q_auto/${encodeURIComponent(videoUrl)}`;
```

## Performance Considerations

1. **Caching**: Cache generated thumbnails để tránh regenerate
2. **Async Processing**: Sử dụng queue system cho video lớn
3. **CDN**: Store thumbnails trên CDN để load nhanh hơn
4. **Fallback**: Luôn có fallback image khi generation fails

## Security Considerations

1. **URL Validation**: Validate video URL để tránh SSRF attacks
2. **Rate Limiting**: Implement rate limiting cho thumbnail endpoint
3. **File Size Limits**: Giới hạn size của video file
4. **Timeout**: Set timeout cho FFmpeg process

## Example Usage

```bash
# Generate thumbnail at 5 seconds, 400x300 size
GET /video/thumbnail?url=https://example.com/video.mp4&time=5&w=400&h=300
```

## Current Implementation

Hiện tại, app sẽ:
1. **YouTube/Vimeo**: Hiển thị thumbnail chất lượng cao
2. **MP4 files**: Hiển thị placeholder với video icon
3. **Error cases**: Fallback về default image

Để enable MP4 thumbnail generation, implement một trong các backend options trên và update `generateThumbnailFromBackend` method.
