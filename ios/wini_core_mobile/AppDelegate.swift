import UIKit
import React
import React_RCTAppDelegate
import ReactAppDependencyProvider
import Firebase
import GoogleSignIn

@main
class AppDelegate: RCTAppDelegate {
  override func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
    self.moduleName = "wini_core_mobile"
    self.dependencyProvider = RCTAppDependencyProvider()

    // You can add your custom initial props in the dictionary below.
    // They will be passed down to the ViewController used by React Native.
    self.initialProps = [:]
  
    FirebaseApp.configure()

    // Since window is non-optional in RCTAppDelegate, we need to check if rootViewController exists
    // if let rootViewController = self.window.rootViewController {
    //     CallAppInterface.setHomeViewController(rootViewController)
    // }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  override func bundleURL() -> URL? {
    #if DEBUG
      // In debug, always use the Metro server
      return RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
    #else
      // In release, use the bundled JS file
      return Bundle.main.url(forResource: "main", withExtension: "jsbundle") ?? RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
    #endif
  }
}
