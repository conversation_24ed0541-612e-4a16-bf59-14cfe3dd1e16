{"name": "wini_core_mobile", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@notifee/react-native": "^9.1.3", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-clipboard/clipboard": "^1.15.0", "@react-native-community/slider": "^4.5.6", "@react-native-firebase/app": "^21.6.1", "@react-native-firebase/auth": "^21.12.0", "@react-native-firebase/messaging": "^21.6.1", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-masked-view/masked-view": "^0.2.8", "@react-navigation/bottom-tabs": "^7.1.3", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.0.15", "@react-navigation/native-stack": "^7.2.1", "@reduxjs/toolkit": "^2.6.1", "@wuba/react-native-echarts": "^1.3.1", "axios": "^1.8.3", "date-fns": "^4.1.0", "i18next": "^24.2.3", "react": "19.0.0", "react-hook-form": "^7.53.2", "react-i18next": "^15.4.1", "react-native": "0.78.0", "react-native-animatable": "^1.4.0", "react-native-animated-spinkit": "^1.5.2", "react-native-biometrics": "^3.0.1", "react-native-collapsible-tab-view": "^8.0.1", "react-native-confetti-cannon": "^1.5.2", "react-native-date-picker": "^5.0.12", "react-native-device-info": "^14.0.2", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.25.0", "react-native-get-random-values": "^1.11.0", "react-native-image-crop-picker": "^0.41.6", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.4.1", "react-native-modalize": "^2.1.1", "react-native-orientation-locker": "^1.7.0", "react-native-pager-view": "^6.7.0", "react-native-paper": "^5.12.5", "react-native-pell-rich-editor": "^1.9.0", "react-native-reanimated": "^3.10.1", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.9.2", "react-native-skeleton-placeholder": "^5.2.4", "react-native-sound": "^0.11.2", "react-native-svg": "^15.11.2", "react-native-swiper-flatlist": "^3.2.5", "react-native-tab-view": "^4.0.6", "react-native-video": "6.0.0-alpha.7", "react-native-view-shot": "^4.0.3", "react-native-webview": "^13.13.2", "react-native-xml2js": "^1.0.3", "react-native-youtube-iframe": "^2.3.0", "react-redux": "^9.2.0", "svg-path-properties": "^1.3.0", "uuid": "^11.1.0", "validate.js": "^0.13.1", "wini-mobile-components": "^1.0.52"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.0", "@react-native/eslint-config": "0.78.0", "@react-native/metro-config": "0.78.0", "@react-native/typescript-config": "0.78.0", "@types/globalthis": "^1.0.4", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "babel-jest": "^29.7.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.7.2"}, "engines": {"node": ">=18"}}